{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SaveListFormItemReq } from 'src/services/api/models';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"src/app/shared/components/create-item-dialog/create-item-dialog.service\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@nebular/theme\";\nimport * as i12 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i13 from \"../../../../shared/components/household-binding/household-binding.component\";\nfunction DetailContentManagementSalesAccountComponent_div_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 27);\n    i0.ɵɵelement(2, \"path\", 65);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredArrListFormItemReq.length, \" / \", ctx_r1.arrListFormItemReq.length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 13)(2, \"div\", 57)(3, \"div\", 58);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 59);\n    i0.ɵɵelement(5, \"path\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_div_28_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function DetailContentManagementSalesAccountComponent_div_28_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DetailContentManagementSalesAccountComponent_div_28_button_7_Template, 3, 0, \"button\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_div_28_div_8_Template, 2, 2, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 20);\n    i0.ɵɵtext(2, \"\\u5C55\\u958B\\u63A7\\u5236:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 69);\n    i0.ɵɵelement(6, \"path\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.collapseAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 69);\n    i0.ɵɵelement(9, \"path\", 72);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 75);\n    i0.ɵɵelement(3, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 77);\n    i0.ɵɵtext(5, \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 78);\n    i0.ɵɵtext(7, \"\\u8ACB\\u5617\\u8A66\\u8ABF\\u6574\\u641C\\u5C0B\\u95DC\\u9375\\u5B57\\u6216\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_43_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(9, \" \\u6E05\\u9664\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const idx_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeFormItem(idx_r9));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 27);\n    i0.ɵɵelement(2, \"path\", 94);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const idx_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollToItem(idx_r9 - 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 96);\n    i0.ɵɵelement(2, \"path\", 97);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const idx_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollToItem(idx_r9 + 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 96);\n    i0.ɵɵelement(2, \"path\", 99);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 155);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r1.getCurrentImage(formItemReq_r7)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.prevImage(formItemReq_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 27);\n    i0.ɵɵelement(2, \"path\", 157);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.nextImage(formItemReq_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 27);\n    i0.ɵɵelement(2, \"path\", 159);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r7.currentImageIndex || 0) + 1, \" / \", formItemReq_r7.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_button_1_Template_button_click_0_listener() {\n      const i_r17 = i0.ɵɵrestoreView(_r16).index;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openImageModal(formItemReq_r7, i_r17));\n    });\n    i0.ɵɵelement(1, \"img\", 164);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r18 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r17 === (formItemReq_r7.currentImageIndex || 0))(\"border-gray-300\", i_r17 !== (formItemReq_r7.currentImageIndex || 0))(\"ring-2\", i_r17 === (formItemReq_r7.currentImageIndex || 0))(\"ring-blue-200\", i_r17 === (formItemReq_r7.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r17 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 10, imageUrl_r18), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 161);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_button_1_Template, 3, 12, \"button\", 162);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r7.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 144);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openImageModal(formItemReq_r7));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_img_2_Template, 2, 3, \"img\", 145);\n    i0.ɵɵelementStart(3, \"div\", 146)(4, \"div\", 147)(5, \"div\", 148);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 149);\n    i0.ɵɵelement(7, \"path\", 150);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_8_Template, 3, 0, \"button\", 151)(9, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_9_Template, 3, 0, \"button\", 152)(10, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_10_Template, 2, 2, \"div\", 153);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_Template, 2, 1, \"div\", 154);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage(formItemReq_r7));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 166);\n    i0.ɵɵelement(2, \"path\", 167);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 168);\n    i0.ɵɵtext(4, \"\\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r19.CSelectName, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"label\", 169);\n    i0.ɵɵtext(2, \"\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 170)(4, \"div\", 171);\n    i0.ɵɵtemplate(5, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_span_5_Template, 2, 1, \"span\", 172);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 173);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r7.TblFormSelect);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", formItemReq_r7.TblFormSelect.length, \" \\u9805\\u5EFA\\u6750\\u9078\\u9805 \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_nb_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r20.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 178)(1, \"div\", 179);\n    i0.ɵɵelement(2, \"img\", 180)(3, \"div\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 182);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template_input_blur_4_listener($event) {\n      const i_r23 = i0.ɵɵrestoreView(_r22).index;\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.renameFile($event, i_r23, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 183);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template_button_click_5_listener() {\n      const picture_r24 = i0.ɵɵrestoreView(_r22).$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeImage(picture_r24.id, formItemReq_r7));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 184);\n    i0.ɵɵelement(7, \"path\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u522A\\u9664\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r24 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r24.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r24.name)(\"disabled\", (tmp_11_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 176);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template, 9, 4, \"div\", 177);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r7.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 185)(1, \"label\", 186);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 179);\n    i0.ɵɵelement(4, \"img\", 187);\n    i0.ɵɵpipe(5, \"base64Image\");\n    i0.ɵɵelement(6, \"div\", 181);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(5, 1, formItemReq_r7.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 188);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 189);\n    i0.ɵɵelement(2, \"path\", 190);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 191);\n    i0.ɵɵtext(4, \"\\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 193);\n    i0.ɵɵelement(2, \"path\", 194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 195);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 206);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const remark_r26 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.selectedRemarkType[remark_r26], $event) || (formItemReq_r7.selectedRemarkType[remark_r26] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const remark_r26 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckboxRemarkChange($event, remark_r26, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r26 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r7.selectedRemarkType[remark_r26]);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 203);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template, 1, 2, \"nb-checkbox\", 204);\n    i0.ɵɵelementStart(2, \"span\", 205);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const remark_r26 = ctx.$implicit;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.selectedRemarkType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(remark_r26);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 201);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_Template, 4, 2, \"label\", 202);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 193);\n    i0.ɵɵelement(2, \"path\", 207);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 195);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 196)(1, \"div\", 111);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 197);\n    i0.ɵɵelement(3, \"path\", 198);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h5\", 199);\n    i0.ɵɵtext(5, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_Template, 2, 1, \"div\", 200)(7, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_ng_template_7_Template, 5, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r27 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.CRemarkTypeOptions && ctx_r1.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r27);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"div\", 102)(3, \"div\", 103)(4, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 104);\n    i0.ɵɵelement(6, \"path\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"label\", 106);\n    i0.ɵɵtext(8, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_Template, 12, 5, \"div\", 107)(10, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_10_Template, 5, 0, \"div\", 108);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 109)(12, \"div\", 110)(13, \"div\", 111);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 104);\n    i0.ɵɵelement(15, \"path\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"label\", 106);\n    i0.ɵɵtext(17, \"\\u57FA\\u672C\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 103)(19, \"div\", 112)(20, \"label\", 113);\n    i0.ɵɵtext(21, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 114)(23, \"span\", 115);\n    i0.ɵɵtext(24, \" \\u986F\\u793A\\u540D\\u7A31: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.CDisplayName, $event) || (formItemReq_r7.CDisplayName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(26, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_Template, 8, 2, \"div\", 117);\n    i0.ɵɵelementStart(27, \"div\", 118)(28, \"div\", 112)(29, \"label\", 113);\n    i0.ɵɵtext(30, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 119)(32, \"input\", 120);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.CRequireAnswer, $event) || (formItemReq_r7.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 112)(34, \"label\", 113);\n    i0.ɵɵtext(35, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"nb-select\", 121);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_nb_select_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.selectedCUiType, $event) || (formItemReq_r7.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_nb_select_selectedChange_36_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSelectCUiType(formItemReq_r7));\n    });\n    i0.ɵɵtemplate(37, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_nb_option_37_Template, 2, 2, \"nb-option\", 122);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(38, \"div\", 102)(39, \"div\", 103)(40, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 104);\n    i0.ɵɵelement(42, \"path\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"label\", 106);\n    i0.ɵɵtext(44, \"\\u6982\\u5FF5\\u8A2D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const inputFile_r21 = i0.ɵɵreference(51);\n      return i0.ɵɵresetView(inputFile_r21.click());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(46, \"svg\", 125);\n    i0.ɵɵelement(47, \"path\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(48, \"span\");\n    i0.ɵɵtext(49, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"input\", 127, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_change_50_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.detectFiles($event, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(52, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_Template, 2, 1, \"div\", 128)(53, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_53_Template, 7, 3, \"div\", 129)(54, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_54_Template, 5, 0, \"div\", 130);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 131)(56, \"div\", 119)(57, \"div\", 132);\n    i0.ɵɵelement(58, \"div\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 134)(60, \"span\", 135);\n    i0.ɵɵtext(61, \"\\u8A2D\\u5B9A\\u9078\\u9805\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(62, \"div\", 136)(63, \"div\", 137)(64, \"div\", 111);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(65, \"svg\", 138);\n    i0.ɵɵelement(66, \"path\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(67, \"h5\", 140);\n    i0.ɵɵtext(68, \"\\u9069\\u7528\\u6236\\u578B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"app-household-binding\", 141);\n    i0.ɵɵlistener(\"selectionChange\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_app_household_binding_selectionChange_69_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHouseholdSelectionChange(ctx_r1.extractHouseholdCodes($event), formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(70, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_70_Template, 5, 0, \"div\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(71, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_Template, 9, 2, \"div\", 143);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_10_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    let tmp_27_0;\n    const ctx_r27 = i0.ɵɵnextContext();\n    const formItemReq_r7 = ctx_r27.$implicit;\n    const idx_r9 = ctx_r27.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl && formItemReq_r7.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.CMatrialUrl || formItemReq_r7.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"for\", \"CDisplayName_\" + idx_r9);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"id\", \"CDisplayName_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.CDisplayName);\n    i0.ɵɵproperty(\"disabled\", (tmp_10_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_10_0 !== undefined ? tmp_10_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.TblFormSelect && formItemReq_r7.TblFormSelect.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r7.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.CUiTypeOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.listPictures && formItemReq_r7.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CDesignFileUrl && (!formItemReq_r7.listPictures || formItemReq_r7.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.CDesignFileUrl && (!formItemReq_r7.listPictures || formItemReq_r7.listPictures.length === 0));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"buildingData\", ctx_r1.buildingData)(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u578B\")(\"disabled\", (tmp_27_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_27_0 !== undefined ? tmp_27_0 : false)(\"allowBatchSelect\", true)(\"ngModel\", formItemReq_r7.selectedHouseholdsCached)(\"useHouseNameMode\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.houseHoldList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 80)(2, \"div\", 81)(3, \"div\", 5)(4, \"div\", 13)(5, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_Template_button_click_5_listener() {\n      const formItemReq_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleItemCollapse(formItemReq_r7));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 83);\n    i0.ɵɵelement(7, \"path\", 72);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 84);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 85)(11, \"h4\", 86);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 66);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 13)(16, \"span\", 87);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 88);\n    i0.ɵɵtemplate(19, DetailContentManagementSalesAccountComponent_ng_container_44_button_19_Template, 3, 0, \"button\", 89)(20, DetailContentManagementSalesAccountComponent_ng_container_44_button_20_Template, 3, 0, \"button\", 90)(21, DetailContentManagementSalesAccountComponent_ng_container_44_button_21_Template, 3, 0, \"button\", 91);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(22, DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template, 72, 28, \"div\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_10_0;\n    const formItemReq_r7 = ctx.$implicit;\n    const idx_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"form-item-\" + idx_r9);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", formItemReq_r7.isCollapsed ? \"\\u5C55\\u958B\\u9805\\u76EE\" : \"\\u6536\\u5408\\u9805\\u76EE\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"rotate-180\", formItemReq_r7.isCollapsed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", idx_r9 + 1, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", formItemReq_r7.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9805\\u76EE\\u7DE8\\u865F #\", idx_r9 + 1, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (formItemReq_r7.selectedCUiType == null ? null : formItemReq_r7.selectedCUiType.label) || \"\\u672A\\u8A2D\\u5B9A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !((tmp_10_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_10_0 !== undefined ? tmp_10_0 : false));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", idx_r9 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", idx_r9 < ctx_r1.arrListFormItemReq.length - 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.isCollapsed);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 222);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r30 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r1.getCurrentImage(formItemReq_r30)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 223);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const formItemReq_r30 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevImageModal(formItemReq_r30));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 224);\n    i0.ɵɵelement(2, \"path\", 157);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 225);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const formItemReq_r30 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextImageModal(formItemReq_r30));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 224);\n    i0.ɵɵelement(2, \"path\", 159);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226)(1, \"div\", 13);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 125);\n    i0.ɵɵelement(3, \"path\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 227);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r30 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", (formItemReq_r30.currentImageIndex || 0) + 1, \" / \", formItemReq_r30.CMatrialUrl.length, \"\");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 231);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_button_2_Template_button_click_0_listener() {\n      const i_r34 = i0.ɵɵrestoreView(_r33).index;\n      const formItemReq_r30 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r30.currentImageIndex = i_r34);\n    });\n    i0.ɵɵelement(1, \"img\", 232);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r35 = ctx.$implicit;\n    const i_r34 = ctx.index;\n    const formItemReq_r30 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r34 === (formItemReq_r30.currentImageIndex || 0))(\"border-gray-400\", i_r34 !== (formItemReq_r30.currentImageIndex || 0))(\"ring-3\", i_r34 === (formItemReq_r30.currentImageIndex || 0))(\"ring-white\", i_r34 === (formItemReq_r30.currentImageIndex || 0))(\"ring-opacity-50\", i_r34 === (formItemReq_r30.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r34 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 12, imageUrl_r35), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 228)(1, \"div\", 229);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_button_2_Template, 3, 14, \"button\", 230);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r30 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r30.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 209);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const formItemReq_r30 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeImageModal(formItemReq_r30));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const formItemReq_r30 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event, formItemReq_r30));\n    });\n    i0.ɵɵelementStart(1, \"button\", 210);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const formItemReq_r30 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeImageModal(formItemReq_r30));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 211);\n    i0.ɵɵelement(3, \"path\", 212);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 213);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 214);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_img_6_Template, 2, 3, \"img\", 215)(7, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_7_Template, 3, 0, \"button\", 216)(8, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_8_Template, 3, 0, \"button\", 217);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_9_Template, 6, 2, \"div\", 218);\n    i0.ɵɵelementStart(10, \"div\", 219)(11, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 27);\n    i0.ɵɵelement(13, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\", 220);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_Template, 3, 1, \"div\", 221);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r30 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage(formItemReq_r30));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r30.CMatrialUrl && formItemReq_r30.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r30.CMatrialUrl && formItemReq_r30.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r30.CMatrialUrl && formItemReq_r30.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", formItemReq_r30.CLocation, \"C\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r30.CMatrialUrl && formItemReq_r30.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template, 17, 6, \"div\", 208);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r30 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r30.isModalOpen);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 233);\n    i0.ɵɵelement(1, \"path\", 234);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 50);\n    i0.ɵɵelement(1, \"path\", 235);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"button\", 236);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_69_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copyToNewForm());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 50);\n    i0.ɵɵelement(3, \"path\", 237);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 47);\n    i0.ɵɵtext(5, \" \\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSubmitting);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, router, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseService, cdr, createItemDialogService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.router = router;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.cdr = cdr;\n    this.createItemDialogService = createItemDialogService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.fromID = null;\n    this.isSubmitting = false;\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.listFormItem = null;\n    this.isNew = true;\n    this.arrListFormItemReq = [];\n    this.filteredArrListFormItemReq = [];\n    this.searchQuery = '';\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n      // 同時設定 CFormType 以保持一致性\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const fromIDParam = params.get('fromID');\n        const id = idParam ? +idParam : 0;\n        const fromID = fromIDParam ? +fromIDParam : null;\n        this.buildCaseId = id;\n        this.fromID = fromID;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      // 確保 currentImageIndex 有效\n      const index = formItemReq.currentImageIndex !== undefined ? formItemReq.currentImageIndex : 0;\n      const validIndex = Math.max(0, Math.min(index, formItemReq.CMatrialUrl.length - 1));\n      console.log(`getCurrentImage - CMatrialUrl 長度: ${formItemReq.CMatrialUrl.length}, 索引: ${validIndex}, URL: ${formItemReq.CMatrialUrl[validIndex]}`);\n      return formItemReq.CMatrialUrl[validIndex];\n    }\n    console.log('getCurrentImage - 沒有圖片或 CMatrialUrl 為空');\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem?.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CDisplayName: o.CDisplayName ? o.CDisplayName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              CSpaeId: o.CSpaeId || null,\n              // 新增空間ID\n              TblFormSelect: o.TblFormSelect || [],\n              // 新增材料選項資料\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              isCollapsed: true,\n              // 現有項目默認收合\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n          // 初始化過濾列表\n          this.updateFilteredList();\n          // 手動觸發變更檢測\n          this.cdr.detectChanges();\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n        // 最終觸發變更檢測\n        this.cdr.detectChanges();\n      }\n    })).subscribe({\n      error: error => {\n        // Error handled silently\n      }\n    });\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CDisplayName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CDisplayName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    // 設置提交狀態\n    this.isSubmitting = true;\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e, index) => {\n      const item = {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId || 0,\n        CLocation: e.CLocation,\n        CDisplayName: e.CDisplayName,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value,\n        CSpaeId: e.CSpaeId || null,\n        // 新增空間ID\n        TblFormSelect: e.TblFormSelect || null // 新增材料選項資料\n      };\n      return item;\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      // 滾動到第一個有錯誤的項目\n      this.scrollToFirstErrorItem();\n      this.isSubmitting = false;\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItemData();\n    }\n  }\n  saveListFormItemData() {\n    this.saveListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormID: this.listFormItem?.CFormId,\n      CFormItem: this.saveListFormItemReq,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItem\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n      }\n    });\n  }\n  createListFormItem() {\n    // Use SaveListFormItem for both create and update operations\n    this.saveListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormID: this.listFormItem?.CFormId,\n      CFormItem: this.saveListFormItemReq,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItem\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        if (this.arrListFormItemReq.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = this.arrListFormItemReq.map((e, index) => {\n          const item = {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: 0,\n            // 設為 0 以建立新項目\n            CLocation: e.CLocation,\n            CDisplayName: e.CDisplayName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value,\n            CSpaeId: e.CSpaeId || null,\n            // 新增空間ID\n            TblFormSelect: e.TblFormSelect || null // 新增材料選項資料\n          };\n          return item;\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 使用 SaveListFormItem 建立複製的表單\n        const copyFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormID: 0,\n          CFormItem: this.saveListFormItemReq,\n          CFormType: this.typeContentManagementSalesAccount.CFormType\n        };\n        this._formItemService.apiFormItemSaveListFormItemPost$Json({\n          body: copyFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0 && createRes.Entries) {\n            const newFormId = createRes.Entries.CFormID;\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${this.arrListFormItemReq.length} 個有效項目`);\n            // 導航到新的表單頁面\n            const currentQueryParams = this.route.snapshot.queryParams;\n            this.router.navigate(['/pages/content-management-sales-account', this.buildCaseId, newFormId], {\n              queryParams: currentQueryParams\n            });\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseService.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.buildCaseId\n    }).subscribe({\n      next: response => {\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        }\n      },\n      error: error => {\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        // 載入建築物資料 (只呼叫一次 GetDropDown API)\n        this.loadBuildingDataFromAPI();\n        this.getListFormItem();\n      }\n    })).subscribe({\n      error: error => {\n        // Error handled silently\n      }\n    });\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  // UI優化相關方法\n  /**\n   * 檢查項目是否已完成\n   */\n  isItemCompleted(formItemReq) {\n    // 檢查必填欄位是否都已填寫\n    const hasItemName = !!formItemReq.CDisplayName && formItemReq.CDisplayName.trim() !== '';\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\n    let hasRemarkType = true;\n    if (formItemReq.selectedCUiType?.value === 3) {\n      hasRemarkType = !!formItemReq.selectedRemarkType && Object.values(formItemReq.selectedRemarkType).some(selected => selected);\n    }\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\n  }\n  /**\n   * 獲取已完成項目數量\n   */\n  getCompletedItemsCount() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\n  }\n  /**\n   * 獲取進度百分比\n   */\n  getProgressPercentage() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    const completed = this.getCompletedItemsCount();\n    return Math.round(completed / this.arrListFormItemReq.length * 100);\n  }\n  /**\n   * 滾動到指定項目\n   */\n  scrollToItem(index) {\n    const element = document.getElementById(`form-item-${index}`);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n      // 添加高亮效果\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      setTimeout(() => {\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      }, 2000);\n    }\n  }\n  /**\n   * 滾動到第一個未完成的項目\n   */\n  scrollToFirstIncompleteItem() {\n    if (!this.arrListFormItemReq) return;\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstIncompleteIndex !== -1) {\n      this.scrollToItem(firstIncompleteIndex);\n    }\n  }\n  /**\n   * 滾動到第一個有錯誤的項目\n   */\n  scrollToFirstErrorItem() {\n    if (!this.arrListFormItemReq) return;\n    // 找到第一個有錯誤的項目\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstErrorIndex !== -1) {\n      this.scrollToItem(firstErrorIndex);\n    }\n  }\n  /**\n   * 滾動到頂部\n   */\n  scrollToTop() {\n    window.scrollTo(0, 0);\n  }\n  /**\n   * 回到頂部\n   */\n  goToTop() {\n    // 優先滾動到頁面最頂部的header區塊\n    const headerElement = document.querySelector('nb-card-header') || document.querySelector('.card-header') || document.querySelector('nb-card') || document.querySelector('.header') || document.querySelector('h1, h2, h3') || document.body.firstElementChild;\n    if (headerElement) {\n      headerElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n      // 額外向上滾動一點，確保header完全可見\n      setTimeout(() => {\n        window.scrollBy({\n          top: -50,\n          // 向上滾動50px\n          behavior: 'smooth'\n        });\n      }, 500);\n      return;\n    }\n    // 備用方案：滾動到第一個表單項目的上方\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\n      const firstElement = document.getElementById('form-item-0');\n      if (firstElement) {\n        firstElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n        // 向上滾動更多距離，確保看到header\n        setTimeout(() => {\n          window.scrollBy({\n            top: -200,\n            // 向上滾動200px\n            behavior: 'smooth'\n          });\n        }, 500);\n        return;\n      }\n    }\n    // 最後的備用方法\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  /**\n   * 滾動到底部 - 滾動到 footer 資訊區塊\n   */\n  scrollToBottom() {\n    // 立即顯示一個簡短的視覺反饋\n    const button = document.querySelector('button[title=\"到底部\"]');\n    if (button) {\n      button.style.transform = 'scale(0.95)';\n      setTimeout(() => {\n        button.style.transform = '';\n      }, 150);\n    }\n    // 滾動到 footer 資訊區塊\n    setTimeout(() => {\n      // 方法1: 滾動到 nb-card-footer 元素\n      const footerElement = document.querySelector('nb-card-footer');\n      if (footerElement) {\n        footerElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          // 改為 center 讓 footer 在畫面中央\n          inline: 'nearest'\n        });\n        return;\n      }\n      // 方法2: 尋找包含統計資訊的 footer 區域（使用更精確的選擇器）\n      const progressFooter = document.querySelector('nb-card-footer .flex.items-center.justify-center');\n      if (progressFooter) {\n        progressFooter.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n        return;\n      }\n      // 方法3: 滾動到最後一個表單項目\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\n        if (lastElement) {\n          lastElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'end',\n            inline: 'nearest'\n          });\n          // 額外向下滾動到 footer 區域\n          setTimeout(() => {\n            window.scrollBy({\n              top: 200,\n              // 增加滾動距離以確保看到 footer\n              behavior: 'smooth'\n            });\n          }, 500);\n          return;\n        }\n      }\n      // 備用方法: 滾動到頁面底部\n      window.scrollTo({\n        top: document.body.scrollHeight,\n        behavior: 'smooth'\n      });\n    }, 100);\n  }\n  /**\n   * 切換項目收合狀態\n   */\n  toggleItemCollapse(formItemReq) {\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\n  }\n  /**\n   * 全部展開\n   */\n  expandAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = false;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 全部收合\n   */\n  collapseAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = true;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 只展開未完成的項目\n   */\n  expandIncompleteOnly() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = this.isItemCompleted(item);\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 搜尋功能\n   */\n  onSearch() {\n    if (!this.searchQuery.trim()) {\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    } else {\n      const query = this.searchQuery.toLowerCase().trim();\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\n        return item.CLocation?.toLowerCase().includes(query) || item.CDisplayName?.toLowerCase().includes(query);\n      });\n    }\n    this.cdr.detectChanges();\n  }\n  /**\n   * 清除搜尋\n   */\n  clearSearch() {\n    this.searchQuery = '';\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    this.cdr.detectChanges();\n  }\n  /**\n   * 更新過濾列表\n   */\n  updateFilteredList() {\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    if (this.searchQuery.trim()) {\n      this.onSearch();\n    }\n  }\n  /**\n   * 移除表單項目 (表單未鎖定時可用)\n   */\n  removeFormItem(index) {\n    // 檢查表單是否鎖定\n    if (this.listFormItem?.CIsLock ?? false) {\n      this.message.showErrorMSG('表單已鎖定，無法移除項目');\n      return;\n    }\n    if (index < 0 || index >= this.arrListFormItemReq.length) {\n      this.message.showErrorMSG('無效的項目索引');\n      return;\n    }\n    const item = this.arrListFormItemReq[index];\n    // 為現有項目提供更詳細的確認訊息\n    const isExistingItem = !!item.CFormItemId && item.CFormItemId > 0;\n    const confirmMessage = isExistingItem ? `確定要刪除現有項目「${item.CDisplayName || item.CLocation}」嗎？此操作將會永久刪除該項目。` : `確定要移除項目「${item.CDisplayName || item.CLocation}」嗎？`;\n    // 確認對話框\n    if (confirm(confirmMessage)) {\n      // 如果是已存在的項目，先呼叫API刪除\n      if (isExistingItem) {\n        this._formItemService.apiFormItemDeleteListFormItemPost$Json({\n          body: item.CFormItemId\n        }).subscribe({\n          next: response => {\n            if (response.StatusCode == 0) {\n              console.log('表單項目刪除成功:', response.Message);\n              // API刪除成功後，更新UI\n              this.removeItemFromUI(index, item, isExistingItem);\n            } else {\n              this.message.showErrorMSG(`刪除失敗: ${response.Message}`);\n            }\n          },\n          error: error => {\n            console.error('刪除表單項目時發生錯誤:', error);\n            this.message.showErrorMSG('刪除表單項目時發生錯誤');\n          }\n        });\n      } else {\n        // 如果是新增未儲存的項目，直接從UI移除\n        this.removeItemFromUI(index, item, isExistingItem);\n      }\n    }\n  }\n  /**\n   * 從UI中移除項目的輔助方法\n   */\n  removeItemFromUI(index, item, isExistingItem) {\n    // 從陣列中移除項目\n    this.arrListFormItemReq.splice(index, 1);\n    // 更新過濾列表\n    this.updateFilteredList();\n    // 顯示成功訊息\n    const successMessage = isExistingItem ? `已刪除項目「${item.CDisplayName || item.CLocation}」` : `已移除項目「${item.CDisplayName || item.CLocation}」`;\n    this.message.showSucessMSG(successMessage);\n    // 觸發變更檢測\n    this.cdr.detectChanges();\n  }\n  /**\n   * 開啟建立選項對話框\n   */\n  openCreateItemDialog() {\n    if (!this.buildCaseId) {\n      this.message.showErrorMSG('請先選擇建案');\n      return;\n    }\n    this.createItemDialogService.open({\n      buildCaseId: this.buildCaseId,\n      title: '建立選項'\n    }).subscribe(result => {\n      if (result && !result.cancelled) {\n        this.promptAutoSelectHouseholds(result.selectedSpaces, result.selectedMaterials);\n      }\n    });\n  }\n  /**\n   * 從材料清單中提取圖片URLs\n   */\n  extractMaterialImages(materials) {\n    const imageUrls = [];\n    console.log('extractMaterialImages - 輸入材料數量:', materials.length);\n    materials.forEach((material, materialIndex) => {\n      console.log(`材料 ${materialIndex}:`, material);\n      // 如果材料有圖片資料\n      if (material.CSelectPicture && Array.isArray(material.CSelectPicture)) {\n        console.log(`材料 ${materialIndex} 有 ${material.CSelectPicture.length} 張圖片`);\n        material.CSelectPicture.forEach((picture, pictureIndex) => {\n          console.log(`圖片 ${pictureIndex}:`, picture);\n          // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\n          if (picture.CFile) {\n            imageUrls.push(picture.CFile);\n            console.log(`添加圖片 URL: ${picture.CFile}`);\n          } else if (picture.CGuid) {\n            // 可以實作透過 CGuid 取得圖片的功能\n            console.log('Picture has CGuid:', picture.CGuid);\n          }\n        });\n      } else {\n        console.log(`材料 ${materialIndex} 沒有圖片資料`);\n      }\n    });\n    console.log('extractMaterialImages - 最終提取到的圖片 URLs:', imageUrls);\n    return imageUrls.length > 0 ? imageUrls : null;\n  }\n  /**\n   * 詢問是否自動選擇已勾選的適用戶型\n   */\n  promptAutoSelectHouseholds(spaces, materials) {\n    // 收集所有已存在項目的已選戶型\n    const existingSelectedHouseholds = this.getExistingSelectedHouseholds();\n    if (existingSelectedHouseholds.length > 0) {\n      const householdNames = existingSelectedHouseholds.join('、');\n      const confirmMessage = `檢測到已有項目選擇了以下適用戶型：\\n${householdNames}\\n\\n是否要為新建立的項目自動選擇這些適用戶型？`;\n      if (confirm(confirmMessage)) {\n        this.createFormItems(spaces, materials, existingSelectedHouseholds);\n      } else {\n        this.createFormItems(spaces, materials);\n      }\n    } else {\n      // 沒有已選戶型，直接建立項目\n      this.createFormItems(spaces, materials);\n    }\n  }\n  /**\n   * 取得現有項目中已選擇的戶型\n   */\n  getExistingSelectedHouseholds() {\n    const allSelectedHouseholds = new Set();\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\n      this.arrListFormItemReq.forEach(item => {\n        if (item.selectedHouseholdsCached && item.selectedHouseholdsCached.length > 0) {\n          item.selectedHouseholdsCached.forEach(household => {\n            allSelectedHouseholds.add(household);\n          });\n        }\n      });\n    }\n    return Array.from(allSelectedHouseholds);\n  }\n  /**\n   * 根據選擇的空間和材料建立表單項目\n   * @param spaces 選擇的空間\n   * @param materials 選擇的材料\n   * @param autoSelectedHouseholds 可選：自動選擇的戶型\n   */\n  createFormItems(spaces, materials, autoSelectedHouseholds) {\n    const newItems = [];\n    console.log(materials);\n    // 將所有選擇的材料轉換為 TblFormSelect 格式\n    const tblFormSelectItems = materials.map(material => ({\n      CFormItemId: undefined,\n      CFormSelectId: undefined,\n      CMaterialId: material.CId || undefined,\n      CSelectName: material.CSelectName || null\n    }));\n    // 每個空間建立一個項目，包含所有選擇的材料\n    spaces.forEach(space => {\n      // 準備選中戶型的物件\n      const selectedItems = {};\n      let selectedHouseholdsCached = [];\n      // 如果有自動選擇的戶型，設定選中狀態\n      if (autoSelectedHouseholds && autoSelectedHouseholds.length > 0) {\n        autoSelectedHouseholds.forEach(household => {\n          selectedItems[household] = true;\n        });\n        selectedHouseholdsCached = [...autoSelectedHouseholds];\n      }\n      const newItem = {\n        // 基本資料 - 根據 SaveListFormItemReq 接口\n        CLocation: space.CLocation || '',\n        CDisplayName: space.CLocation,\n        // 使用空間名稱作為預設顯示名稱\n        CRequireAnswer: 1,\n        CUiType: 1,\n        // 預設為建材選色\n        CRemarkType: '',\n        CSpaeId: space.CSpaceID,\n        CTotalAnswer: tblFormSelectItems.length,\n        CFormItemId: 0,\n        CDesignFileUrl: null,\n        CFile: undefined,\n        CFormItemHouseHold: [],\n        TblFormSelect: tblFormSelectItems,\n        // 業務欄位（擴展）\n        CMatrialUrl: this.extractMaterialImages(materials),\n        CFormID: this.listFormItem?.CFormId || 0,\n        // UI 狀態欄位\n        selectedCUiType: this.CUiTypeOptions[0],\n        // 預設選擇第一個選項（建材選色）\n        selectedItems: selectedItems,\n        selectedRemarkType: {},\n        allSelected: autoSelectedHouseholds ? this.houseHoldList.length > 0 && this.houseHoldList.every(item => selectedItems[item]) : false,\n        listPictures: [],\n        currentImageIndex: 0,\n        isModalOpen: false,\n        selectedHouseholdsCached: selectedHouseholdsCached,\n        isCollapsed: false\n      };\n      console.log(`新建立的項目 ${space.CLocation}:`, newItem);\n      console.log(`CMatrialUrl:`, newItem.CMatrialUrl);\n      newItems.push(newItem);\n    });\n    console.log('所有新建立的項目:', newItems);\n    // 將新項目添加到現有列表\n    this.arrListFormItemReq = [...this.arrListFormItemReq, ...newItems];\n    this.updateFilteredList();\n    // 立即觸發變更檢測以確保 DOM 更新\n    this.cdr.detectChanges();\n    // 使用 setTimeout 確保 DOM 完全更新後再次觸發變更檢測\n    setTimeout(() => {\n      this.cdr.detectChanges();\n      console.log('延遲變更檢測完成');\n    }, 0);\n    // 顯示成功訊息\n    const autoSelectMsg = autoSelectedHouseholds && autoSelectedHouseholds.length > 0 ? `，並自動選擇了 ${autoSelectedHouseholds.length} 個適用戶型` : '';\n    this.message.showSucessMSG(`成功建立 ${newItems.length} 個選項${autoSelectMsg}`);\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i9.CreateItemDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 88,\n      vars: 19,\n      consts: [[\"inputFile\", \"\"], [\"noRemarkOptions\", \"\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-gray-100\"], [1, \"shadow-xl\", \"border-0\", \"rounded-xl\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-1\", \"h-8\", \"bg-green-500\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-green-100\", \"text-green-800\", \"rounded-full\", \"font-medium\"], [1, \"p-6\", \"bg-gray-50\"], [1, \"space-y-8\"], [1, \"bg-white\", \"rounded-xl\", \"p-6\", \"shadow-sm\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-blue-100\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [1, \"text-right\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"class\", \"mt-4 mb-4\", 4, \"ngIf\"], [1, \"mt-6\", \"bg-gradient-to-r\", \"from-gray-50\", \"to-blue-50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"justify-between\", \"flex-wrap\", \"gap-4\"], [1, \"flex\", \"items-center\", \"flex-wrap\", \"gap-3\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"px-4\", \"py-2\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"rounded-lg\", \"transition-colors\", \"flex\", \"items-center\", \"gap-2\", \"shadow-md\", \"disabled:bg-gray-400\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4v16m8-8H4\"], [\"class\", \"flex items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-4\"], [1, \"hidden\", \"lg:flex\", \"items-center\", \"gap-2\", \"text-xs\", \"text-gray-500\", \"bg-blue-50\", \"rounded-lg\", \"px-3\", \"py-2\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/95\", \"backdrop-blur-sm\", \"border-t\", \"border-gray-200\", \"p-4\", \"sticky\", \"bottom-0\", \"shadow-lg\", \"z-30\"], [1, \"flex\", \"items-center\", \"justify-center\"], [1, \"flex\", \"items-center\", \"space-x-6\", \"text-sm\", \"text-gray-600\"], [1, \"w-3\", \"h-3\", \"bg-blue-500\", \"rounded-full\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"fixed\", \"right-6\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"z-50\"], [1, \"floating-button-group\"], [1, \"floating-btn-container\"], [\"title\", \"\\u5132\\u5B58\\u8B8A\\u66F4\", 1, \"floating-btn\", \"floating-btn-primary\", \"floating-btn-large\", 3, \"click\", \"disabled\"], [\"class\", \"w-6 h-6 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-6 h-6\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"floating-btn-tooltip\"], [\"class\", \"floating-btn-container\", 4, \"ngIf\"], [\"title\", \"\\u53D6\\u6D88\\u4E26\\u8FD4\\u56DE\", 1, \"floating-btn\", \"floating-btn-gray\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [\"title\", \"\\u56DE\\u5230\\u9802\\u90E8\", 1, \"floating-btn\", \"floating-btn-purple\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 10l7-7m0 0l7 7m-7-7v18\"], [\"title\", \"\\u5230\\u5E95\\u90E8\", 1, \"floating-btn\", \"floating-btn-indigo\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 14l-7 7m0 0l-7-7m7 7V3\"], [1, \"mt-4\", \"mb-4\"], [1, \"flex-1\", \"relative\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u90E8\\u4F4D\\u3001\\u4F4D\\u7F6E...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-10\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-blue-500\", \"text-sm\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-sm text-gray-600\", 4, \"ngIf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", \"text-gray-400\", \"hover:text-gray-600\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"flex\", \"items-center\", \"bg-white\", \"rounded-lg\", \"shadow-sm\", \"border\", \"border-gray-200\", \"overflow-hidden\"], [\"title\", \"\\u5168\\u90E8\\u5C55\\u958B\", 1, \"px-3\", \"py-2\", \"text-sm\", \"bg-white\", \"hover:bg-purple-50\", \"text-purple-700\", \"transition-colors\", \"border-r\", \"border-gray-200\", \"group\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"], [\"title\", \"\\u5168\\u90E8\\u6536\\u5408\", 1, \"px-3\", \"py-2\", \"text-sm\", \"bg-white\", \"hover:bg-purple-50\", \"text-purple-700\", \"transition-colors\", \"border-r\", \"border-gray-200\", \"group\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"text-center\", \"py-12\"], [1, \"bg-gray-50\", \"rounded-xl\", \"p-8\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-16\", \"h-16\", \"text-gray-400\", \"mx-auto\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\", \"mb-2\"], [1, \"text-gray-500\", \"mb-4\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded-lg\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-gray-200\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-xl\", 3, \"id\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-indigo-50\", \"px-6\", \"py-4\", \"border-b\", \"border-gray-200\"], [1, \"w-8\", \"h-8\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\", \"title\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\", \"transition-transform\", \"duration-200\"], [1, \"w-8\", \"h-8\", \"bg-blue-500\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"font-bold\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"text-blue-800\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"w-7 h-7 bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-700 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u79FB\\u9664\\u6B64\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-6\", 4, \"ngIf\"], [\"title\", \"\\u79FB\\u9664\\u6B64\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-600\", \"hover:text-red-700\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [\"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-4\", \"gap-6\"], [1, \"lg:col-span-1\"], [1, \"space-y-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-700\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"lg:col-span-2\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", 3, \"for\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"text-sm\", \"text-gray-600\", \"font-medium\", \"px-2\", \"py-1\", \"bg-blue-100\", \"rounded-md\", \"whitespace-nowrap\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"flex-1\", \"border-0\", \"bg-transparent\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"rounded-md\", \"p-2\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"class\", \"group\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"relative\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-200\", \"rounded-lg\", \"p-3\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-blue-500\", \"to-blue-600\", \"hover:from-blue-600\", \"hover:to-blue-700\", \"text-white\", \"font-medium\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"my-8\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\"], [1, \"w-full\", \"border-t\", \"border-gray-300\"], [1, \"relative\", \"flex\", \"justify-center\", \"text-sm\"], [1, \"px-4\", \"bg-white\", \"text-gray-500\", \"font-medium\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"bg-blue-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"font-semibold\", \"text-blue-800\"], [1, \"w-full\", 3, \"selectionChange\", \"buildingData\", \"placeholder\", \"disabled\", \"allowBatchSelect\", \"ngModel\", \"useHouseNameMode\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"bg-orange-50 p-4 rounded-lg border border-orange-200\", 4, \"ngIf\"], [1, \"aspect-square\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\"], [1, \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-gray-500\", \"mt-2\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\", \"bg-blue-100\", \"text-blue-800\"], [3, \"value\"], [1, \"space-y-3\"], [\"class\", \"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gray-50\", \"border\", \"border-gray-200\", \"p-3\", \"rounded-lg\", \"hover:shadow-md\", \"transition-all\", \"duration-200\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"mb-3\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-200\", \"rounded-lg\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-2\", \"text-sm\", \"mb-2\", \"border\", \"border-gray-200\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", 3, \"blur\", \"value\", \"disabled\"], [1, \"w-full\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-700\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-md\", \"transition-colors\", \"duration-200\", \"text-sm\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [1, \"space-y-2\"], [1, \"block\", \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-lg\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"mb-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"text-xs\"], [1, \"text-center\", \"py-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-gray-400\", \"mx-auto\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"bg-orange-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [1, \"font-semibold\", \"text-orange-800\"], [\"class\", \"grid grid-cols-1 gap-2\", 4, \"ngIf\", \"ngIfElse\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-orange-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [\"value\", \"item\", \"class\", \"mr-3\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [1, \"text-gray-700\"], [\"value\", \"item\", 1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fade-in-up\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"modal-close-btn\", \"fixed\", \"top-6\", \"right-6\", \"z-[60]\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"shadow-2xl\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-slide-in-left\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[85vh]\", \"object-contain\", \"rounded-xl\", \"animate-fade-in-up\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\", \"text-lg\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\", \"modal-thumbnails\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-200\", 3, \"src\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"animate-spin\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [\"title\", \"\\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE\", 1, \"floating-btn\", \"floating-btn-blue\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"nb-card\", 3)(2, \"nb-card-header\", 4)(3, \"div\", 5)(4, \"div\", 6);\n          i0.ɵɵelement(5, \"div\", 7);\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"nb-card-body\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 5)(15, \"div\", 13)(16, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 15);\n          i0.ɵɵelement(18, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"h3\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 18);\n          i0.ɵɵtext(23, \"\\u7BA1\\u7406\\u9078\\u6A23\\u9805\\u76EE\\u7684\\u8A73\\u7D30\\u8A2D\\u5B9A\\u8207\\u914D\\u7F6E\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 6)(25, \"div\", 19)(26, \"div\", 20);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(28, DetailContentManagementSalesAccountComponent_div_28_Template, 9, 3, \"div\", 21);\n          i0.ɵɵelementStart(29, \"div\", 22)(30, \"div\", 23)(31, \"div\", 24)(32, \"div\", 25)(33, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_33_listener() {\n            return ctx.openCreateItemDialog();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(34, \"svg\", 27);\n          i0.ɵɵelement(35, \"path\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" \\u5EFA\\u7ACB\\u9078\\u9805 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, DetailContentManagementSalesAccountComponent_div_37_Template, 10, 0, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(38, \"div\", 30)(39, \"div\", 31);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(40, \"svg\", 32);\n          i0.ɵɵelement(41, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" \\u4F7F\\u7528\\u53F3\\u5074\\u6D6E\\u52D5\\u6309\\u9215\\u5FEB\\u901F\\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(43, DetailContentManagementSalesAccountComponent_div_43_Template, 10, 0, \"div\", 34)(44, DetailContentManagementSalesAccountComponent_ng_container_44_Template, 23, 12, \"ng-container\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(45, \"nb-card-footer\", 36)(46, \"div\", 37)(47, \"div\", 38)(48, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(49, \"svg\", 27);\n          i0.ɵɵelement(50, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(51, \"span\");\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 6)(54, \"div\", 8);\n          i0.ɵɵelement(55, \"div\", 39);\n          i0.ɵɵelementStart(56, \"span\");\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 40);\n          i0.ɵɵtext(59, \" \\u4F7F\\u7528\\u53F3\\u5074\\u61F8\\u6D6E\\u6309\\u9215\\u9032\\u884C\\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(60, DetailContentManagementSalesAccountComponent_ng_container_60_Template, 2, 1, \"ng-container\", 35);\n          i0.ɵɵelementStart(61, \"div\", 41)(62, \"div\", 42)(63, \"div\", 43)(64, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_64_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(65, DetailContentManagementSalesAccountComponent__svg_svg_65_Template, 2, 0, \"svg\", 45)(66, DetailContentManagementSalesAccountComponent__svg_svg_66_Template, 2, 0, \"svg\", 46);\n          i0.ɵɵelementStart(67, \"div\", 47);\n          i0.ɵɵtext(68);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(69, DetailContentManagementSalesAccountComponent_div_69_Template, 6, 1, \"div\", 48);\n          i0.ɵɵelementStart(70, \"div\", 43)(71, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_71_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(72, \"svg\", 50);\n          i0.ɵɵelement(73, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(74, \"div\", 47);\n          i0.ɵɵtext(75, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 43)(77, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_77_listener() {\n            return ctx.goToTop();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(78, \"svg\", 50);\n          i0.ɵɵelement(79, \"path\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(80, \"div\", 47);\n          i0.ɵɵtext(81, \" \\u56DE\\u5230\\u9802\\u90E8 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"div\", 43)(83, \"button\", 54);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_83_listener() {\n            return ctx.scrollToBottom();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(84, \"svg\", 50);\n          i0.ɵɵelement(85, \"path\", 55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(86, \"div\", 47);\n          i0.ɵɵtext(87, \" \\u5230\\u5E95\\u90E8 \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_12_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.dynamicTitle, \" \");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.dynamicTitle);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A08\\uFF1A\", (ctx.arrListFormItemReq == null ? null : ctx.arrListFormItemReq.length) || 0, \" \\u500B\\u9805\\u76EE \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq.length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", (tmp_4_0 = ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock) !== null && tmp_4_0 !== undefined ? tmp_4_0 : false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery && ctx.filteredArrListFormItemReq.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredArrListFormItemReq);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9078\\u6A23\\u9805\\u76EE\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u7E3D\\u8A08 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9805\\u76EE\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"animate-pulse\", ctx.isSubmitting);\n          i0.ɵɵproperty(\"disabled\", ((tmp_12_0 = ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false) || ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u5132\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u8B8A\\u66F4\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, FormsModule, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.NgModel, SharedModule, i11.NbCardComponent, i11.NbCardBodyComponent, i11.NbCardFooterComponent, i11.NbCardHeaderComponent, i11.NbCheckboxComponent, i11.NbInputDirective, i11.NbSelectComponent, i11.NbOptionComponent, i12.BreadcrumbComponent, AppSharedModule, i13.HouseholdBindingComponent, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  min-height: 100vh;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  animation: _ngcontent-%COMP%_gradientShift 20s ease infinite;\\n  background-size: 400% 400%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\nnb-card[_ngcontent-%COMP%] {\\n  border-radius: 1rem !important;\\n  overflow: hidden;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\nnb-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;\\n}\\n\\n.item-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\\n}\\n\\ninput[nbInput][_ngcontent-%COMP%], \\nnb-select[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out !important;\\n}\\ninput[nbInput][_ngcontent-%COMP%]:focus, \\nnb-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;\\n}\\n\\n.btn-enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.status-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: inherit;\\n  border-radius: inherit;\\n  opacity: 0.1;\\n  transform: scale(0);\\n  transition: transform 0.3s ease;\\n}\\n.status-badge[_ngcontent-%COMP%]:hover::before {\\n  transform: scale(1.1);\\n}\\n\\n.responsive-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n\\n.image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.carousel-btn[_ngcontent-%COMP%] {\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n@media (max-width: 768px) {\\n  .carousel-btn[_ngcontent-%COMP%] {\\n    opacity: 1 !important;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #2563eb, #7c3aed);\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n  border-radius: 0.75rem;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5));\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.7));\\n}\\n\\n.modal-nav-btn[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  z-index: 9995 !important;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .modal-nav-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n.modal-close-btn[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  pointer-events: auto;\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.2) !important;\\n  transform: scale(1.1) rotate(90deg);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n    top: 1rem;\\n    right: 1rem;\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.5s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.5s ease-out;\\n}\\n\\n.animate-fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.25rem;\\n    height: 1.25rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], \\n   *[_ngcontent-%COMP%]::before, \\n   *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .page-background[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    background: rgba(31, 41, 55, 0.9);\\n    border-color: rgba(156, 163, 175, 0.3);\\n  }\\n}\\n.floating-button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n.floating-button-group[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.floating-btn-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.floating-btn-container[_ngcontent-%COMP%]:first-child   .floating-btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 16px;\\n  border-top-right-radius: 16px;\\n}\\n.floating-btn-container[_ngcontent-%COMP%]:last-child   .floating-btn[_ngcontent-%COMP%] {\\n  border-bottom-left-radius: 16px;\\n  border-bottom-right-radius: 16px;\\n}\\n\\n.floating-btn[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n.floating-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-2px);\\n  z-index: 10;\\n}\\n.floating-btn[_ngcontent-%COMP%]:active {\\n  transform: translateX(-1px);\\n}\\n.floating-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.floating-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.15s ease;\\n}\\n.floating-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.floating-btn-primary[_ngcontent-%COMP%] {\\n  background: #10b981;\\n  color: white;\\n}\\n.floating-btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #059669;\\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\\n}\\n\\n.floating-btn-large[_ngcontent-%COMP%] {\\n  height: 64px;\\n}\\n.floating-btn-large[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.floating-btn-blue[_ngcontent-%COMP%] {\\n  background: #3b82f6;\\n  color: white;\\n}\\n.floating-btn-blue[_ngcontent-%COMP%]:hover {\\n  background: #2563eb;\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\\n}\\n\\n.floating-btn-gray[_ngcontent-%COMP%] {\\n  background: #6b7280;\\n  color: white;\\n}\\n.floating-btn-gray[_ngcontent-%COMP%]:hover {\\n  background: #4b5563;\\n  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);\\n}\\n\\n.floating-btn-purple[_ngcontent-%COMP%] {\\n  background: #8b5cf6;\\n  color: white;\\n}\\n.floating-btn-purple[_ngcontent-%COMP%]:hover {\\n  background: #7c3aed;\\n  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);\\n}\\n\\n.floating-btn-indigo[_ngcontent-%COMP%] {\\n  background: #6366f1;\\n  color: white;\\n}\\n.floating-btn-indigo[_ngcontent-%COMP%]:hover {\\n  background: #4f46e5;\\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\\n}\\n\\n.floating-btn-tooltip[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 64px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: #1f2937;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 8px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: all 0.2s ease;\\n  z-index: 1000;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.floating-btn-tooltip[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 100%;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  border: 5px solid transparent;\\n  border-left-color: #1f2937;\\n}\\n\\n.floating-btn[_ngcontent-%COMP%]:hover   .floating-btn-tooltip[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(-50%) translateX(-4px);\\n}\\n\\n@media (max-width: 768px) {\\n  .floating-button-group[_ngcontent-%COMP%] {\\n    border-radius: 12px;\\n  }\\n  .floating-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .floating-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .floating-btn-large[_ngcontent-%COMP%] {\\n    height: 56px;\\n  }\\n  .floating-btn-large[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .floating-btn-container[_ngcontent-%COMP%]:first-child   .floating-btn[_ngcontent-%COMP%] {\\n    border-top-left-radius: 12px;\\n    border-top-right-radius: 12px;\\n  }\\n  .floating-btn-container[_ngcontent-%COMP%]:last-child   .floating-btn[_ngcontent-%COMP%] {\\n    border-bottom-left-radius: 12px;\\n    border-bottom-right-radius: 12px;\\n  }\\n  .floating-btn-tooltip[_ngcontent-%COMP%] {\\n    right: 56px;\\n    font-size: 0.75rem;\\n    padding: 4px 8px;\\n  }\\n  .floating-btn-tooltip[_ngcontent-%COMP%]::after {\\n    border-width: 4px;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .floating-button-group[_ngcontent-%COMP%] {\\n    background: #1f2937;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  }\\n  .floating-btn-tooltip[_ngcontent-%COMP%] {\\n    background: #374151;\\n  }\\n  .floating-btn-tooltip[_ngcontent-%COMP%]::after {\\n    border-left-color: #374151;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .floating-button-group[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n    background: #fff;\\n  }\\n  .floating-btn[_ngcontent-%COMP%] {\\n    border: 1px solid currentColor;\\n  }\\n  .floating-btn-tooltip[_ngcontent-%COMP%] {\\n    background: #000;\\n    color: #fff;\\n    border: 1px solid #fff;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .floating-button-group[_ngcontent-%COMP%], \\n   .floating-btn[_ngcontent-%COMP%], \\n   .floating-btn-tooltip[_ngcontent-%COMP%] {\\n    transition: none !important;\\n    animation: none !important;\\n  }\\n  .floating-btn[_ngcontent-%COMP%]:hover {\\n    transform: none;\\n    box-shadow: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "NbCheckboxModule", "tap", "SaveListFormItemReq", "SharedModule", "AppSharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_div_28_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "filteredArrListFormItemReq", "length", "arrListFormItemReq", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_div_28_Template_input_ngModelChange_6_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "searchQuery", "DetailContentManagementSalesAccountComponent_div_28_Template_input_input_6_listener", "onSearch", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_div_28_button_7_Template", "DetailContentManagementSalesAccountComponent_div_28_div_8_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_4_listener", "_r4", "expandAll", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_7_listener", "collapseAll", "DetailContentManagementSalesAccountComponent_div_43_Template_button_click_8_listener", "_r5", "DetailContentManagementSalesAccountComponent_ng_container_44_button_19_Template_button_click_0_listener", "_r8", "idx_r9", "index", "removeFormItem", "DetailContentManagementSalesAccountComponent_ng_container_44_button_20_Template_button_click_0_listener", "_r10", "scrollToItem", "DetailContentManagementSalesAccountComponent_ng_container_44_button_21_Template_button_click_0_listener", "_r11", "ɵɵpipeBind1", "getCurrentImage", "formItemReq_r7", "ɵɵsanitizeUrl", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_8_Template_button_click_0_listener", "_r14", "$implicit", "prevImage", "stopPropagation", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_9_Template_button_click_0_listener", "_r15", "nextImage", "currentImageIndex", "CMatrialUrl", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_button_1_Template_button_click_0_listener", "i_r17", "_r16", "openImageModal", "ɵɵclassProp", "imageUrl_r18", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_Template_div_click_1_listener", "_r13", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_button_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_div_11_Template", "ɵɵtextInterpolate1", "item_r19", "CSelectName", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_span_5_Template", "TblFormSelect", "case_r20", "label", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template_input_blur_4_listener", "i_r23", "_r22", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template_button_click_5_listener", "picture_r24", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_div_1_Template", "listPictures", "CDesignFileUrl", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r25", "remark_r26", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_nb_checkbox_1_Template", "ɵɵtextInterpolate", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_div_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_ng_template_7_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r27", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_ngModelChange_25_listener", "_r12", "CDisplayName", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_26_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_ngModelChange_32_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_nb_select_ngModelChange_36_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_nb_select_selected<PERSON><PERSON>e_36_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_nb_option_37_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_button_click_45_listener", "inputFile_r21", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_input_change_50_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_52_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_53_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_54_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template_app_household_binding_selectionChange_69_listener", "onHouseholdSelectionChange", "extractHouseholdCodes", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_70_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_div_71_Template", "tmp_10_0", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "buildingData", "tmp_27_0", "selectedHouseholdsCached", "houseHoldList", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_44_Template_button_click_5_listener", "_r6", "toggleItemCollapse", "DetailContentManagementSalesAccountComponent_ng_container_44_button_19_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_button_20_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_button_21_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_22_Template", "isCollapsed", "CLocation", "formItemReq_r30", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_7_Template_button_click_0_listener", "_r31", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_8_Template_button_click_0_listener", "_r32", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_button_2_Template_button_click_0_listener", "i_r34", "_r33", "imageUrl_r35", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_button_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_click_0_listener", "_r29", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_button_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template_div_click_4_listener", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_div_16_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent_div_69_Template_button_click_1_listener", "_r36", "copyToNewForm", "isSubmitting", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "router", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseService", "cdr", "createItemDialogService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "地主戶", "銷售戶", "fromID", "selectedItems", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "fromIDParam", "buildCaseId", "getListRegularNoticeFileHouseHold", "showErrorMSG", "goBack", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "validIndex", "Math", "max", "min", "console", "log", "imageIndex", "key", "preventDefault", "households", "Array", "isArray", "map", "h", "code", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "updateAllSelectedHouseholdsCache", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CBuildCaseId", "CIsPaging", "pipe", "res", "Entries", "StatusCode", "formItems", "o", "CFormId", "CFirstMatrialUrl", "CFormItemHouseHold", "CFormItemId", "CName", "<PERSON>art", "CTotalAnswer", "CUiType", "CSpaeId", "tblFormItemHouseholds", "createArrayObjectFromArray", "updateFilteredList", "detectChanges", "error", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "errorMessages", "showErrorMSGs", "scrollToFirstErrorItem", "createListFormItem", "saveListFormItemData", "saveListFormItem", "CFormID", "CFormItem", "apiFormItemSaveListFormItemPost$Json", "next", "showSucessMSG", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "apiMaterialGetMaterialListPost$Json", "CPagi", "validMaterialKeys", "Set", "material", "add", "copyFormItem", "createRes", "newFormId", "currentQueryParams", "snapshot", "navigate", "loadBuildingDataFromAPI", "apiHouseGetDropDownPost$Json", "response", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "house", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "ceil", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "isItemCompleted", "hasItemName", "trim", "hasUiType", "hasRequireAnswer", "hasRemarkType", "values", "selected", "getCompletedItemsCount", "getProgressPercentage", "completed", "round", "element", "getElementById", "scrollIntoView", "behavior", "block", "inline", "classList", "setTimeout", "remove", "scrollToFirstIncompleteItem", "firstIncompleteIndex", "findIndex", "firstErrorIndex", "scrollToTop", "window", "scrollTo", "goToTop", "headerElement", "querySelector", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollBy", "top", "firstElement", "scrollToBottom", "button", "transform", "footerElement", "progressFooter", "lastIndex", "lastElement", "scrollHeight", "expandIncompleteOnly", "query", "toLowerCase", "isExistingItem", "confirmMessage", "confirm", "apiFormItemDeleteListFormItemPost$Json", "Message", "removeItemFromUI", "splice", "successMessage", "openCreateItemDialog", "open", "title", "cancelled", "promptAutoSelectHouseholds", "selectedSpaces", "selectedMaterials", "extractMaterialImages", "materials", "imageUrls", "materialIndex", "CSelectPicture", "picture", "pictureIndex", "CGuid", "spaces", "existingSelectedHouseholds", "getExistingSelectedHouseholds", "householdNames", "createFormItems", "allSelectedHouseholds", "from", "autoSelectedHouseholds", "newItems", "tblFormSelectItems", "CFormSelectId", "CMaterialId", "CId", "space", "newItem", "CSpaceID", "autoSelectMsg", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "Router", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "HouseService", "ChangeDetectorRef", "i9", "CreateItemDialogService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_div_28_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_33_listener", "DetailContentManagementSalesAccountComponent_div_37_Template", "DetailContentManagementSalesAccountComponent_div_43_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_Template", "DetailContentManagementSalesAccountComponent_ng_container_60_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_64_listener", "DetailContentManagementSalesAccountComponent__svg_svg_65_Template", "DetailContentManagementSalesAccountComponent__svg_svg_66_Template", "DetailContentManagementSalesAccountComponent_div_69_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_71_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_77_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_83_listener", "tmp_4_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i10", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i11", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i12", "BreadcrumbComponent", "i13", "HouseholdBindingComponent", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCheckboxModule, NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService, HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse, SaveListFormItem } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { CreateItemDialogService } from 'src/app/shared/components/create-item-dialog/create-item-dialog.service';\r\nimport { SpacePickerItem } from 'src/app/shared/components/space-picker/space-picker.component';\r\nimport { MaterialPickerItem } from 'src/app/shared/components/material-picker/material-picker.component';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq extends SaveListFormItemReq {\r\n  // 業務欄位（擴展）\r\n  CMatrialUrl?: string[] | null;\r\n  CFormID?: number;\r\n\r\n\r\n  // UI 狀態欄位\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean };\r\n  selectedRemarkType?: { [key: string]: boolean };\r\n  allSelected: boolean;\r\n  listPictures: any[];\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n  isCollapsed?: boolean; // 是否收合狀態\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService,\r\n    private cdr: ChangeDetectorRef,\r\n    private createItemDialogService: CreateItemDialogService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n      // 同時設定 CFormType 以保持一致性\r\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n  fromID: number | null = null;\r\n  isSubmitting: boolean = false;\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const fromIDParam = params.get('fromID');\r\n        const id = idParam ? +idParam : 0;\r\n        const fromID = fromIDParam ? +fromIDParam : null;\r\n        this.buildCaseId = id;\r\n        this.fromID = fromID;\r\n\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      // 確保 currentImageIndex 有效\r\n      const index = formItemReq.currentImageIndex !== undefined ? formItemReq.currentImageIndex : 0;\r\n      const validIndex = Math.max(0, Math.min(index, formItemReq.CMatrialUrl.length - 1));\r\n\r\n      console.log(`getCurrentImage - CMatrialUrl 長度: ${formItemReq.CMatrialUrl.length}, 索引: ${validIndex}, URL: ${formItemReq.CMatrialUrl[validIndex]}`);\r\n\r\n      return formItemReq.CMatrialUrl[validIndex];\r\n    }\r\n    console.log('getCurrentImage - 沒有圖片或 CMatrialUrl 為空');\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes | null = null\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem?.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CDisplayName: o.CDisplayName ? o.CDisplayName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                CSpaeId: o.CSpaeId || null, // 新增空間ID\r\n                TblFormSelect: o.TblFormSelect || [], // 新增材料選項資料\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                isCollapsed: true, // 現有項目默認收合\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n\r\n            // 初始化過濾列表\r\n            this.updateFilteredList();\r\n\r\n            // 手動觸發變更檢測\r\n            this.cdr.detectChanges();\r\n          }\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n\r\n          // 最終觸發變更檢測\r\n          this.cdr.detectChanges();\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        // Error handled silently\r\n      }\r\n    })\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  filteredArrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  searchQuery: string = ''\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CDisplayName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CDisplayName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[];\r\n  saveListFormItem: SaveListFormItem;\r\n\r\n  onSubmit() {\r\n    // 設置提交狀態\r\n    this.isSubmitting = true;\r\n\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any, index: number) => {\r\n      const item = {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId || 0,\r\n        CLocation: e.CLocation,\r\n        CDisplayName: e.CDisplayName,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n        CSpaeId: e.CSpaeId || null, // 新增空間ID\r\n        TblFormSelect: e.TblFormSelect || null, // 新增材料選項資料\r\n      };\r\n\r\n\r\n      return item;\r\n    })\r\n\r\n\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      // 滾動到第一個有錯誤的項目\r\n      this.scrollToFirstErrorItem();\r\n      this.isSubmitting = false;\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItemData()\r\n    }\r\n  }\r\n\r\n  saveListFormItemData() {\r\n    this.saveListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormID: this.listFormItem?.CFormId,\r\n      CFormItem: this.saveListFormItemReq,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType\r\n    }\r\n\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItem\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n      }\r\n    })\r\n  }\r\n\r\n  createListFormItem() {\r\n    // Use SaveListFormItem for both create and update operations\r\n    this.saveListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormID: this.listFormItem?.CFormId,\r\n      CFormItem: this.saveListFormItemReq,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType\r\n    }\r\n\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItem\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          if (this.arrListFormItemReq.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = this.arrListFormItemReq.map((e: any, index: number) => {\r\n            const item = {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: 0, // 設為 0 以建立新項目\r\n              CLocation: e.CLocation,\r\n              CDisplayName: e.CDisplayName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n              CSpaeId: e.CSpaeId || null, // 新增空間ID\r\n              TblFormSelect: e.TblFormSelect || null, // 新增材料選項資料\r\n            };\r\n\r\n\r\n            return item;\r\n          });\r\n\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 使用 SaveListFormItem 建立複製的表單\r\n          const copyFormItem: SaveListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormID: 0,\r\n            CFormItem: this.saveListFormItemReq,\r\n            CFormType: this.typeContentManagementSalesAccount.CFormType\r\n          }\r\n\r\n          this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n            body: copyFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0 && createRes.Entries) {\r\n              const newFormId = createRes.Entries.CFormID;\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${this.arrListFormItemReq.length} 個有效項目`);\r\n\r\n              // 導航到新的表單頁面\r\n              const currentQueryParams = this.route.snapshot.queryParams;\r\n              this.router.navigate(['/pages/content-management-sales-account', this.buildCaseId, newFormId], {\r\n                queryParams: currentQueryParams\r\n              });\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.buildCaseId }).subscribe({\r\n      next: (response) => {\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  houseHoldList: any[];\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n\r\n          // 載入建築物資料 (只呼叫一次 GetDropDown API)\r\n          this.loadBuildingDataFromAPI();\r\n\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        // Error handled silently\r\n      }\r\n    })\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  // UI優化相關方法\r\n\r\n  /**\r\n   * 檢查項目是否已完成\r\n   */\r\n  isItemCompleted(formItemReq: ExtendedSaveListFormItemReq): boolean {\r\n    // 檢查必填欄位是否都已填寫\r\n    const hasItemName = !!formItemReq.CDisplayName && formItemReq.CDisplayName.trim() !== '';\r\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\r\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\r\n\r\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\r\n    let hasRemarkType = true;\r\n    if (formItemReq.selectedCUiType?.value === 3) {\r\n      hasRemarkType = !!formItemReq.selectedRemarkType &&\r\n        Object.values(formItemReq.selectedRemarkType).some(selected => selected);\r\n    }\r\n\r\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\r\n  }\r\n\r\n  /**\r\n   * 獲取已完成項目數量\r\n   */\r\n  getCompletedItemsCount(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\r\n  }\r\n\r\n  /**\r\n   * 獲取進度百分比\r\n   */\r\n  getProgressPercentage(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    const completed = this.getCompletedItemsCount();\r\n    return Math.round((completed / this.arrListFormItemReq.length) * 100);\r\n  }\r\n\r\n  /**\r\n   * 滾動到指定項目\r\n   */\r\n  scrollToItem(index: number): void {\r\n    const element = document.getElementById(`form-item-${index}`);\r\n    if (element) {\r\n      element.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n\r\n      // 添加高亮效果\r\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      setTimeout(() => {\r\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個未完成的項目\r\n   */\r\n  scrollToFirstIncompleteItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstIncompleteIndex !== -1) {\r\n      this.scrollToItem(firstIncompleteIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個有錯誤的項目\r\n   */\r\n  scrollToFirstErrorItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    // 找到第一個有錯誤的項目\r\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstErrorIndex !== -1) {\r\n      this.scrollToItem(firstErrorIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到頂部\r\n   */\r\n  scrollToTop(): void {\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  /**\r\n   * 回到頂部\r\n   */\r\n  goToTop(): void {\r\n\r\n    // 優先滾動到頁面最頂部的header區塊\r\n    const headerElement = document.querySelector('nb-card-header') ||\r\n      document.querySelector('.card-header') ||\r\n      document.querySelector('nb-card') ||\r\n      document.querySelector('.header') ||\r\n      document.querySelector('h1, h2, h3') ||\r\n      document.body.firstElementChild;\r\n\r\n    if (headerElement) {\r\n      (headerElement as HTMLElement).scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start'\r\n      });\r\n\r\n      // 額外向上滾動一點，確保header完全可見\r\n      setTimeout(() => {\r\n        window.scrollBy({\r\n          top: -50, // 向上滾動50px\r\n          behavior: 'smooth'\r\n        });\r\n      }, 500);\r\n      return;\r\n    }\r\n\r\n    // 備用方案：滾動到第一個表單項目的上方\r\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\r\n      const firstElement = document.getElementById('form-item-0');\r\n      if (firstElement) {\r\n        firstElement.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'start'\r\n        });\r\n\r\n        // 向上滾動更多距離，確保看到header\r\n        setTimeout(() => {\r\n          window.scrollBy({\r\n            top: -200, // 向上滾動200px\r\n            behavior: 'smooth'\r\n          });\r\n        }, 500);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 最後的備用方法\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n\r\n  /**\r\n   * 滾動到底部 - 滾動到 footer 資訊區塊\r\n   */\r\n  scrollToBottom(): void {\r\n\r\n    // 立即顯示一個簡短的視覺反饋\r\n    const button = document.querySelector('button[title=\"到底部\"]') as HTMLElement;\r\n    if (button) {\r\n      button.style.transform = 'scale(0.95)';\r\n      setTimeout(() => {\r\n        button.style.transform = '';\r\n      }, 150);\r\n    }\r\n\r\n    // 滾動到 footer 資訊區塊\r\n    setTimeout(() => {\r\n      // 方法1: 滾動到 nb-card-footer 元素\r\n      const footerElement = document.querySelector('nb-card-footer') as HTMLElement;\r\n      if (footerElement) {\r\n        footerElement.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'center', // 改為 center 讓 footer 在畫面中央\r\n          inline: 'nearest'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 方法2: 尋找包含統計資訊的 footer 區域（使用更精確的選擇器）\r\n      const progressFooter = document.querySelector('nb-card-footer .flex.items-center.justify-center') as HTMLElement;\r\n      if (progressFooter) {\r\n        progressFooter.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'center',\r\n          inline: 'nearest'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 方法3: 滾動到最後一個表單項目\r\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\r\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\r\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\r\n        if (lastElement) {\r\n          lastElement.scrollIntoView({\r\n            behavior: 'smooth',\r\n            block: 'end',\r\n            inline: 'nearest'\r\n          });\r\n\r\n          // 額外向下滾動到 footer 區域\r\n          setTimeout(() => {\r\n            window.scrollBy({\r\n              top: 200, // 增加滾動距離以確保看到 footer\r\n              behavior: 'smooth'\r\n            });\r\n          }, 500);\r\n\r\n          return;\r\n        }\r\n      }\r\n\r\n      // 備用方法: 滾動到頁面底部\r\n      window.scrollTo({\r\n        top: document.body.scrollHeight,\r\n        behavior: 'smooth'\r\n      });\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * 切換項目收合狀態\r\n   */\r\n  toggleItemCollapse(formItemReq: ExtendedSaveListFormItemReq): void {\r\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\r\n  }\r\n\r\n  /**\r\n   * 全部展開\r\n   */\r\n  expandAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = false;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 全部收合\r\n   */\r\n  collapseAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = true;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 只展開未完成的項目\r\n   */\r\n  expandIncompleteOnly(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = this.isItemCompleted(item);\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 搜尋功能\r\n   */\r\n  onSearch(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    } else {\r\n      const query = this.searchQuery.toLowerCase().trim();\r\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\r\n        return (\r\n          item.CLocation?.toLowerCase().includes(query) ||\r\n          item.CDisplayName?.toLowerCase().includes(query)\r\n        );\r\n      });\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 清除搜尋\r\n   */\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 更新過濾列表\r\n   */\r\n  private updateFilteredList(): void {\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    if (this.searchQuery.trim()) {\r\n      this.onSearch();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 移除表單項目 (表單未鎖定時可用)\r\n   */\r\n  removeFormItem(index: number): void {\r\n    // 檢查表單是否鎖定\r\n    if (this.listFormItem?.CIsLock ?? false) {\r\n      this.message.showErrorMSG('表單已鎖定，無法移除項目');\r\n      return;\r\n    }\r\n\r\n    if (index < 0 || index >= this.arrListFormItemReq.length) {\r\n      this.message.showErrorMSG('無效的項目索引');\r\n      return;\r\n    }\r\n\r\n    const item = this.arrListFormItemReq[index];\r\n\r\n    // 為現有項目提供更詳細的確認訊息\r\n    const isExistingItem = !!item.CFormItemId && item.CFormItemId > 0;\r\n    const confirmMessage = isExistingItem\r\n      ? `確定要刪除現有項目「${item.CDisplayName || item.CLocation}」嗎？此操作將會永久刪除該項目。`\r\n      : `確定要移除項目「${item.CDisplayName || item.CLocation}」嗎？`;\r\n\r\n    // 確認對話框\r\n    if (confirm(confirmMessage)) {\r\n      // 如果是已存在的項目，先呼叫API刪除\r\n      if (isExistingItem) {\r\n        this._formItemService.apiFormItemDeleteListFormItemPost$Json({ body: item.CFormItemId }).subscribe({\r\n          next: (response) => {\r\n            if (response.StatusCode == 0) {\r\n              console.log('表單項目刪除成功:', response.Message);\r\n              // API刪除成功後，更新UI\r\n              this.removeItemFromUI(index, item, isExistingItem);\r\n            } else {\r\n              this.message.showErrorMSG(`刪除失敗: ${response.Message}`);\r\n            }\r\n          },\r\n          error: (error) => {\r\n            console.error('刪除表單項目時發生錯誤:', error);\r\n            this.message.showErrorMSG('刪除表單項目時發生錯誤');\r\n          }\r\n        });\r\n      } else {\r\n        // 如果是新增未儲存的項目，直接從UI移除\r\n        this.removeItemFromUI(index, item, isExistingItem);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 從UI中移除項目的輔助方法\r\n   */\r\n  private removeItemFromUI(index: number, item: ExtendedSaveListFormItemReq, isExistingItem: boolean): void {\r\n    // 從陣列中移除項目\r\n    this.arrListFormItemReq.splice(index, 1);\r\n\r\n    // 更新過濾列表\r\n    this.updateFilteredList();\r\n\r\n    // 顯示成功訊息\r\n    const successMessage = isExistingItem\r\n      ? `已刪除項目「${item.CDisplayName || item.CLocation}」`\r\n      : `已移除項目「${item.CDisplayName || item.CLocation}」`;\r\n    this.message.showSucessMSG(successMessage);\r\n\r\n    // 觸發變更檢測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 開啟建立選項對話框\r\n   */\r\n  openCreateItemDialog(): void {\r\n    if (!this.buildCaseId) {\r\n      this.message.showErrorMSG('請先選擇建案');\r\n      return;\r\n    }\r\n\r\n    this.createItemDialogService.open({\r\n      buildCaseId: this.buildCaseId,\r\n      title: '建立選項'\r\n    }).subscribe(result => {\r\n      if (result && !result.cancelled) {\r\n        this.promptAutoSelectHouseholds(result.selectedSpaces, result.selectedMaterials);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 從材料清單中提取圖片URLs\r\n   */\r\n  private extractMaterialImages(materials: MaterialPickerItem[]): string[] | null {\r\n    const imageUrls: string[] = [];\r\n\r\n    console.log('extractMaterialImages - 輸入材料數量:', materials.length);\r\n\r\n    materials.forEach((material, materialIndex) => {\r\n      console.log(`材料 ${materialIndex}:`, material);\r\n\r\n      // 如果材料有圖片資料\r\n      if (material.CSelectPicture && Array.isArray(material.CSelectPicture)) {\r\n        console.log(`材料 ${materialIndex} 有 ${material.CSelectPicture.length} 張圖片`);\r\n\r\n        material.CSelectPicture.forEach((picture: any, pictureIndex) => {\r\n          console.log(`圖片 ${pictureIndex}:`, picture);\r\n\r\n          // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\r\n          if (picture.CFile) {\r\n            imageUrls.push(picture.CFile);\r\n            console.log(`添加圖片 URL: ${picture.CFile}`);\r\n          } else if (picture.CGuid) {\r\n            // 可以實作透過 CGuid 取得圖片的功能\r\n            console.log('Picture has CGuid:', picture.CGuid);\r\n          }\r\n        });\r\n      } else {\r\n        console.log(`材料 ${materialIndex} 沒有圖片資料`);\r\n      }\r\n    });\r\n\r\n    console.log('extractMaterialImages - 最終提取到的圖片 URLs:', imageUrls);\r\n    return imageUrls.length > 0 ? imageUrls : null;\r\n  }\r\n\r\n  /**\r\n   * 詢問是否自動選擇已勾選的適用戶型\r\n   */\r\n  private promptAutoSelectHouseholds(spaces: SpacePickerItem[], materials: MaterialPickerItem[]): void {\r\n    // 收集所有已存在項目的已選戶型\r\n    const existingSelectedHouseholds = this.getExistingSelectedHouseholds();\r\n\r\n    if (existingSelectedHouseholds.length > 0) {\r\n      const householdNames = existingSelectedHouseholds.join('、');\r\n      const confirmMessage = `檢測到已有項目選擇了以下適用戶型：\\n${householdNames}\\n\\n是否要為新建立的項目自動選擇這些適用戶型？`;\r\n\r\n      if (confirm(confirmMessage)) {\r\n        this.createFormItems(spaces, materials, existingSelectedHouseholds);\r\n      } else {\r\n        this.createFormItems(spaces, materials);\r\n      }\r\n    } else {\r\n      // 沒有已選戶型，直接建立項目\r\n      this.createFormItems(spaces, materials);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 取得現有項目中已選擇的戶型\r\n   */\r\n  private getExistingSelectedHouseholds(): string[] {\r\n    const allSelectedHouseholds = new Set<string>();\r\n\r\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        if (item.selectedHouseholdsCached && item.selectedHouseholdsCached.length > 0) {\r\n          item.selectedHouseholdsCached.forEach(household => {\r\n            allSelectedHouseholds.add(household);\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return Array.from(allSelectedHouseholds);\r\n  }\r\n\r\n  /**\r\n   * 根據選擇的空間和材料建立表單項目\r\n   * @param spaces 選擇的空間\r\n   * @param materials 選擇的材料\r\n   * @param autoSelectedHouseholds 可選：自動選擇的戶型\r\n   */\r\n  private createFormItems(spaces: SpacePickerItem[], materials: MaterialPickerItem[], autoSelectedHouseholds?: string[]): void {\r\n\r\n    const newItems: ExtendedSaveListFormItemReq[] = [];\r\n    console.log(materials);\r\n\r\n    // 將所有選擇的材料轉換為 TblFormSelect 格式\r\n    const tblFormSelectItems = materials.map(material => ({\r\n      CFormItemId: undefined,\r\n      CFormSelectId: undefined,\r\n      CMaterialId: material.CId || undefined,\r\n      CSelectName: material.CSelectName || null\r\n    }));\r\n\r\n    // 每個空間建立一個項目，包含所有選擇的材料\r\n    spaces.forEach(space => {\r\n      // 準備選中戶型的物件\r\n      const selectedItems: { [key: string]: boolean } = {};\r\n      let selectedHouseholdsCached: string[] = [];\r\n\r\n      // 如果有自動選擇的戶型，設定選中狀態\r\n      if (autoSelectedHouseholds && autoSelectedHouseholds.length > 0) {\r\n        autoSelectedHouseholds.forEach(household => {\r\n          selectedItems[household] = true;\r\n        });\r\n        selectedHouseholdsCached = [...autoSelectedHouseholds];\r\n      }\r\n\r\n      const newItem: ExtendedSaveListFormItemReq = {\r\n        // 基本資料 - 根據 SaveListFormItemReq 接口\r\n        CLocation: space.CLocation || '',\r\n        CDisplayName: space.CLocation, // 使用空間名稱作為預設顯示名稱\r\n        CRequireAnswer: 1,\r\n        CUiType: 1, // 預設為建材選色\r\n        CRemarkType: '',\r\n        CSpaeId: space.CSpaceID,\r\n        CTotalAnswer: tblFormSelectItems.length,\r\n        CFormItemId: 0,\r\n        CDesignFileUrl: null,\r\n        CFile: undefined,\r\n        CFormItemHouseHold: [],\r\n        TblFormSelect: tblFormSelectItems,\r\n\r\n        // 業務欄位（擴展）\r\n        CMatrialUrl: this.extractMaterialImages(materials),\r\n        CFormID: this.listFormItem?.CFormId || 0,\r\n\r\n        // UI 狀態欄位\r\n        selectedCUiType: this.CUiTypeOptions[0], // 預設選擇第一個選項（建材選色）\r\n        selectedItems: selectedItems,\r\n        selectedRemarkType: {},\r\n        allSelected: autoSelectedHouseholds ? this.houseHoldList.length > 0 &&\r\n          this.houseHoldList.every(item => selectedItems[item]) : false,\r\n        listPictures: [],\r\n        currentImageIndex: 0,\r\n        isModalOpen: false,\r\n        selectedHouseholdsCached: selectedHouseholdsCached,\r\n        isCollapsed: false\r\n      };\r\n\r\n      console.log(`新建立的項目 ${space.CLocation}:`, newItem);\r\n      console.log(`CMatrialUrl:`, newItem.CMatrialUrl);\r\n\r\n      newItems.push(newItem);\r\n    });\r\n\r\n    console.log('所有新建立的項目:', newItems);\r\n\r\n    // 將新項目添加到現有列表\r\n    this.arrListFormItemReq = [...this.arrListFormItemReq, ...newItems];\r\n\r\n    this.updateFilteredList();\r\n\r\n    // 立即觸發變更檢測以確保 DOM 更新\r\n    this.cdr.detectChanges();\r\n\r\n    // 使用 setTimeout 確保 DOM 完全更新後再次觸發變更檢測\r\n    setTimeout(() => {\r\n      this.cdr.detectChanges();\r\n      console.log('延遲變更檢測完成');\r\n    }, 0);\r\n\r\n    // 顯示成功訊息\r\n    const autoSelectMsg = autoSelectedHouseholds && autoSelectedHouseholds.length > 0\r\n      ? `，並自動選擇了 ${autoSelectedHouseholds.length} 個適用戶型`\r\n      : '';\r\n    this.message.showSucessMSG(`成功建立 ${newItems.length} 個選項${autoSelectMsg}`);\r\n  }\r\n\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\"> <nb-card class=\"shadow-xl border-0 rounded-xl\">\r\n    <nb-card-header class=\"bg-white border-b border-gray-200 p-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"w-1 h-8 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <ngx-breadcrumb></ngx-breadcrumb>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <span class=\"px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full font-medium\">\r\n            {{ dynamicTitle }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"p-6 bg-gray-50\">\r\n      <div class=\"space-y-8\">\r\n        <!-- Page Title Section -->\r\n        <div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                  </path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 class=\"text-xl font-bold text-gray-800\">{{ dynamicTitle }}</h3>\r\n                <p class=\"text-sm text-gray-600 mt-1\">管理選樣項目的詳細設定與配置</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Progress Indicator -->\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"text-right\">\r\n                <div class=\"text-sm font-medium text-gray-700\">\r\n                  總計：{{ arrListFormItemReq?.length || 0 }} 個項目\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Search Bar -->\r\n          <div class=\"mt-4 mb-4\" *ngIf=\"arrListFormItemReq.length > 0\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"flex-1 relative\">\r\n                <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <input type=\"text\" [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\" placeholder=\"搜尋項目名稱、部位、位置...\"\r\n                  class=\"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\" />\r\n                <button *ngIf=\"searchQuery\" (click)=\"clearSearch()\"\r\n                  class=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\">\r\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\">\r\n                    </path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 搜尋結果統計 -->\r\n              <div class=\"text-sm text-gray-600\" *ngIf=\"searchQuery\">\r\n                找到 {{ filteredArrListFormItemReq.length }} / {{ arrListFormItemReq.length }} 個項目\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Enhanced Header Controls -->\r\n          <div class=\"mt-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200\">\r\n            <div class=\"flex items-center justify-between flex-wrap gap-4\">\r\n              <!-- Left Side: Navigation & Actions -->\r\n              <div class=\"flex items-center flex-wrap gap-3\">\r\n                <!-- 建立選項按鈕 -->\r\n                <div class=\"flex items-center gap-2\">\r\n                  <button\r\n                    class=\"px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors flex items-center gap-2 shadow-md disabled:bg-gray-400 disabled:cursor-not-allowed\"\r\n                    (click)=\"openCreateItemDialog()\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\"></path>\r\n                    </svg>\r\n                    建立選項\r\n                  </button>\r\n                </div>\r\n\r\n                <!-- Collapse Controls Group -->\r\n                <div class=\"flex items-center gap-2\" *ngIf=\"arrListFormItemReq.length > 0\">\r\n                  <span class=\"text-sm font-medium text-gray-700\">展開控制:</span>\r\n                  <div class=\"flex items-center bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                    <button\r\n                      class=\"px-3 py-2 text-sm bg-white hover:bg-purple-50 text-purple-700 transition-colors border-r border-gray-200 group\"\r\n                      (click)=\"expandAll()\" title=\"全部展開\">\r\n                      <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      class=\"px-3 py-2 text-sm bg-white hover:bg-purple-50 text-purple-700 transition-colors border-r border-gray-200 group\"\r\n                      (click)=\"collapseAll()\" title=\"全部收合\">\r\n                      <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                      </svg>\r\n                    </button>\r\n\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Right Side: Tips -->\r\n              <div class=\"flex items-center gap-4\">\r\n                <!-- Tips -->\r\n                <div\r\n                  class=\"hidden lg:flex items-center gap-2 text-xs text-gray-500 bg-blue-50 rounded-lg px-3 py-2 border border-blue-200\">\r\n                  <svg class=\"w-4 h-4 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  使用右側浮動按鈕快速操作\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div> <!-- Form Items Section -->\r\n\r\n        <!-- 無搜尋結果提示 -->\r\n        <div *ngIf=\"searchQuery && filteredArrListFormItemReq.length === 0\" class=\"text-center py-12\">\r\n          <div class=\"bg-gray-50 rounded-xl p-8\">\r\n            <svg class=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n            </svg>\r\n            <h3 class=\"text-lg font-medium text-gray-900 mb-2\">找不到符合條件的項目</h3>\r\n            <p class=\"text-gray-500 mb-4\">請嘗試調整搜尋關鍵字或清除搜尋條件</p>\r\n            <button (click)=\"clearSearch()\"\r\n              class=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\">\r\n              清除搜尋\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <ng-container *ngFor=\"let formItemReq of filteredArrListFormItemReq; let idx = index\">\r\n          <div [id]=\"'form-item-' + idx\"\r\n            class=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl\">\r\n            <!-- Item Header -->\r\n            <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- 收合/展開按鈕 -->\r\n                  <button\r\n                    class=\"w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                    (click)=\"toggleItemCollapse(formItemReq)\" [title]=\"formItemReq.isCollapsed ? '展開項目' : '收合項目'\">\r\n                    <svg class=\"w-4 h-4 text-gray-600 transition-transform duration-200\"\r\n                      [class.rotate-180]=\"formItemReq.isCollapsed\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                    </svg>\r\n                  </button>\r\n\r\n                  <div\r\n                    class=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {{idx + 1}}\r\n                  </div>\r\n                  <div class=\"flex-1\">\r\n                    <h4 class=\"text-lg font-semibold text-gray-800\">\r\n                      {{formItemReq.CLocation}}\r\n                    </h4>\r\n                    <p class=\"text-sm text-gray-600\">項目編號 #{{idx + 1}}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- UI Type Badge -->\r\n                  <span class=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\">\r\n                    {{formItemReq.selectedCUiType?.label || '未設定'}}\r\n                  </span>\r\n\r\n                  <!-- Item Navigation and Actions -->\r\n                  <div class=\"flex items-center space-x-1\">\r\n                    <!-- Remove Item Button (available when not locked) -->\r\n                    <button *ngIf=\"!(listFormItem?.CIsLock ?? false)\"\r\n                      class=\"w-7 h-7 bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-700 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"removeFormItem(idx)\" title=\"移除此項目\">\r\n                      <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n\r\n                    <button *ngIf=\"idx > 0\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx - 1)\" title=\"上一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n                    <button *ngIf=\"idx < arrListFormItemReq.length - 1\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx + 1)\" title=\"下一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Main Content Area -->\r\n            <div class=\"p-6\" *ngIf=\"!formItemReq.isCollapsed\">\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n\r\n                <!-- Material Images Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">主要材料示意</label>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                      <!-- Enhanced Image carousel container -->\r\n                      <div\r\n                        class=\"aspect-square w-full relative overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n                        (click)=\"openImageModal(formItemReq)\">\r\n                        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                        <!-- Enhanced Zoom overlay -->\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n                          <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                            <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                              <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Navigation buttons -->\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"prevImage(formItemReq); $event.stopPropagation()\" title=\"上一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"nextImage(formItemReq); $event.stopPropagation()\" title=\"下一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <!-- Enhanced Image counter -->\r\n                        <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Enhanced Thumbnail navigation -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n                        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                          class=\"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n                          [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-2]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-blue-200]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                          <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                            [src]=\"imageUrl | base64Image\">\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                      class=\"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-sm\">無主要材料示意</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields Section (Enhanced) -->\r\n                <div class=\"lg:col-span-2\">\r\n                  <div class=\"space-y-6\">\r\n                    <div class=\"flex items-center space-x-2 mb-4\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">基本設定</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Form Groups -->\r\n                    <div class=\"space-y-4\">\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'CDisplayName_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">項目名稱</label>\r\n                        <div class=\"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <span\r\n                            class=\"text-sm text-gray-600 font-medium px-2 py-1 bg-blue-100 rounded-md whitespace-nowrap\">\r\n                            顯示名稱:\r\n                          </span>\r\n                          <input type=\"text\" [id]=\"'CDisplayName_' + idx\"\r\n                            class=\"flex-1 border-0 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-md p-2\"\r\n                            nbInput [(ngModel)]=\"formItemReq.CDisplayName\" placeholder=\"例如：廚房檯面\"\r\n                            [disabled]=\"listFormItem?.CIsLock ?? false\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 選項欄位 - 顯示建材名稱 -->\r\n                      <div class=\"group\" *ngIf=\"formItemReq.TblFormSelect && formItemReq.TblFormSelect.length > 0\">\r\n                        <label class=\"block text-sm font-medium text-gray-700 mb-2\">選項</label>\r\n                        <div class=\"bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <div class=\"flex flex-wrap gap-2\">\r\n                            <span *ngFor=\"let item of formItemReq.TblFormSelect\"\r\n                              class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\r\n                              {{ item.CSelectName }}\r\n                            </span>\r\n                          </div>\r\n                          <div class=\"text-xs text-gray-500 mt-2\">\r\n                            共 {{ formItemReq.TblFormSelect.length }} 項建材選項\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div class=\"group\">\r\n                          <label [for]=\"'cRequireAnswer_' + idx\"\r\n                            class=\"block text-sm font-medium text-gray-700 mb-2\">必填數量</label>\r\n                          <div class=\"relative\">\r\n                            <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\"\r\n                              class=\"w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg p-3 transition-all duration-200\"\r\n                              nbInput placeholder=\"輸入數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                              [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem?.CIsLock ?? false)\" />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div class=\"group\">\r\n                          <label [for]=\"'uiType_' + idx\"\r\n                            class=\"block text-sm font-medium text-gray-700 mb-2\">前台UI類型</label>\r\n                          <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\"\r\n                            [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                            class=\"w-full border-2 border-gray-200 focus:border-blue-500 rounded-lg transition-all duration-200\"\r\n                            (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                            [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                            <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                              {{ case.label }}\r\n                            </nb-option>\r\n                          </nb-select>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Concept Design Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">概念設計</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Upload Button -->\r\n                    <button\r\n                      class=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      [disabled]=\"listFormItem?.CIsLock\" (click)=\"inputFile.click()\">\r\n                      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\">\r\n                        </path>\r\n                      </svg>\r\n                      <span>上傳概念設計圖</span>\r\n                    </button>\r\n                    <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                      accept=\"image/png, image/gif, image/jpeg\">\r\n\r\n                    <!-- Enhanced Uploaded Pictures List -->\r\n                    <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"space-y-3\">\r\n                      <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                        class=\"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\">\r\n                        <div class=\"relative group\">\r\n                          <img class=\"w-full h-32 object-cover rounded-lg mb-3 border border-gray-200\"\r\n                            [src]=\"picture.data\">\r\n                          <div\r\n                            class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                          </div>\r\n                        </div>\r\n                        <input nbInput\r\n                          class=\"w-full p-2 text-sm mb-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          type=\"text\" placeholder=\"圖片說明/檔名\" [value]=\"picture.name\"\r\n                          (blur)=\"renameFile($event, i, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                        <button\r\n                          class=\"w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-3 rounded-md transition-colors duration-200 text-sm\"\r\n                          (click)=\"removeImage(picture.id, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                          <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                              d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\">\r\n                            </path>\r\n                          </svg>\r\n                          刪除圖片\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Default Concept Design Image -->\r\n                    <div class=\"space-y-2\"\r\n                      *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                      <label class=\"block text-xs font-medium text-gray-600\">預設概念圖</label>\r\n                      <div class=\"relative group\">\r\n                        <img class=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\r\n                          [src]=\"formItemReq.CDesignFileUrl | base64Image\">\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                      class=\"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-8 h-8 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-xs\">無概念設計圖</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Enhanced Separator -->\r\n              <div class=\"my-8\">\r\n                <div class=\"relative\">\r\n                  <div class=\"absolute inset-0 flex items-center\">\r\n                    <div class=\"w-full border-t border-gray-300\"></div>\r\n                  </div>\r\n                  <div class=\"relative flex justify-center text-sm\">\r\n                    <span class=\"px-4 bg-white text-gray-500 font-medium\">設定選項</span>\r\n                  </div>\r\n                </div>\r\n              </div> <!-- Enhanced Applicable Households Section -->\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <div class=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-blue-800\">適用戶型</h5>\r\n                  </div> <!-- 新的戶別選擇器 -->\r\n                  <app-household-binding [buildingData]=\"buildingData\" [placeholder]=\"'請選擇適用戶型'\"\r\n                    [disabled]=\"listFormItem?.CIsLock ?? false\" [allowBatchSelect]=\"true\"\r\n                    [ngModel]=\"formItemReq.selectedHouseholdsCached\"\r\n                    (selectionChange)=\"onHouseholdSelectionChange(extractHouseholdCodes($event), formItemReq)\"\r\n                    class=\"w-full\" [useHouseNameMode]=\"true\">\r\n                  </app-household-binding>\r\n\r\n                  <!-- 無戶別資料時的顯示 -->\r\n                  <div class=\"text-center py-4\" *ngIf=\"houseHoldList.length === 0\">\r\n                    <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Enhanced Remark Options Section -->\r\n                <div class=\"bg-orange-50 p-4 rounded-lg border border-orange-200\"\r\n                  *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-orange-800\">備註選項</h5>\r\n                  </div>\r\n\r\n                  <div class=\"grid grid-cols-1 gap-2\"\r\n                    *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                    <label *ngFor=\"let remark of CRemarkTypeOptions\"\r\n                      class=\"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\">\r\n                      <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                        [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem?.CIsLock\"\r\n                        value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"text-gray-700\">{{ remark }}</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  <ng-template #noRemarkOptions>\r\n                    <div class=\"text-center py-4\">\r\n                      <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <!-- Simplified Footer with Info Only -->\r\n    <nb-card-footer class=\"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sticky bottom-0 shadow-lg z-30\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <div class=\"flex items-center space-x-6 text-sm text-gray-600\">\r\n          <div class=\"flex items-center space-x-2\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n            </svg>\r\n            <span>共 {{arrListFormItemReq.length || 0}} 個選樣項目</span>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-4\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>總計 {{ arrListFormItemReq.length || 0 }} 個項目</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"text-xs text-gray-500\">\r\n            使用右側懸浮按鈕進行操作\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</div>\r\n\r\n<!-- Enhanced Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n\r\n    <!-- Enhanced Close Button -->\r\n    <button\r\n      class=\"modal-close-btn fixed top-6 right-6 z-[60] bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-2xl\"\r\n      (click)=\"closeImageModal(formItemReq)\" title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n      <svg class=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Enhanced Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-slide-in-left\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Main Image Container -->\r\n      <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n        <img class=\"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\"\r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n        <!-- Enhanced Navigation Buttons -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"prevImageModal(formItemReq)\" title=\"上一張圖片 (按 ← 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"nextImageModal(formItemReq)\" title=\"下一張圖片 (按 → 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Counter -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n            </path>\r\n          </svg>\r\n          <span class=\"font-medium text-lg\">{{(formItemReq.currentImageIndex || 0) + 1}} /\r\n            {{formItemReq.CMatrialUrl.length}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Info -->\r\n      <div\r\n        class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-2\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium\">{{formItemReq.CLocation}}C</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n        <div class=\"flex gap-3 overflow-x-auto max-w-[80vw] modal-thumbnails\">\r\n          <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n            class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\"\r\n            [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-3]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-opacity-50]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            (click)=\"formItemReq.currentImageIndex = i\" [title]=\"'跳至第 ' + (i + 1) + ' 張圖片'\">\r\n            <img class=\"w-full h-full object-cover transition-transform duration-200\" [src]=\"imageUrl | base64Image\">\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- Right Side Floating Action Button Group -->\r\n<div class=\"fixed right-6 top-1/2 transform -translate-y-1/2 z-50\">\r\n  <div class=\"floating-button-group\">\r\n    <!-- Save Button -->\r\n    <div class=\"floating-btn-container\">\r\n      <button class=\"floating-btn floating-btn-primary floating-btn-large\" [class.animate-pulse]=\"isSubmitting\"\r\n        (click)=\"onSubmit()\" [disabled]=\"((listFormItem?.CIsLock ?? false) || isSubmitting)\" title=\"儲存變更\">\r\n\r\n        <!-- Loading Spinner -->\r\n        <svg *ngIf=\"isSubmitting\" class=\"w-6 h-6 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\">\r\n          </path>\r\n        </svg>\r\n\r\n        <!-- Save Icon -->\r\n        <svg *ngIf=\"!isSubmitting\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n        </svg>\r\n\r\n        <!-- Tooltip -->\r\n        <div class=\"floating-btn-tooltip\">\r\n          {{ isSubmitting ? '儲存中...' : '儲存變更' }}\r\n        </div>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Copy to New Form Button (only show when locked) -->\r\n    <div class=\"floating-btn-container\" *ngIf=\"listFormItem?.CIsLock\">\r\n      <button class=\"floating-btn floating-btn-blue\" (click)=\"copyToNewForm()\" [disabled]=\"isSubmitting\" title=\"複製到新表單\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n            d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\">\r\n          </path>\r\n        </svg>\r\n\r\n        <!-- Tooltip -->\r\n        <div class=\"floating-btn-tooltip\">\r\n          複製到新表單\r\n        </div>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Cancel Button -->\r\n    <div class=\"floating-btn-container\">\r\n      <button class=\"floating-btn floating-btn-gray\" (click)=\"goBack()\" [disabled]=\"isSubmitting\" title=\"取消並返回\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n        </svg>\r\n\r\n        <!-- Tooltip -->\r\n        <div class=\"floating-btn-tooltip\">\r\n          取消\r\n        </div>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Scroll to Top Button -->\r\n    <div class=\"floating-btn-container\">\r\n      <button class=\"floating-btn floating-btn-purple\" (click)=\"goToTop()\" title=\"回到頂部\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 10l7-7m0 0l7 7m-7-7v18\"></path>\r\n        </svg>\r\n\r\n        <!-- Tooltip -->\r\n        <div class=\"floating-btn-tooltip\">\r\n          回到頂部\r\n        </div>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Scroll to Bottom Button -->\r\n    <div class=\"floating-btn-container\">\r\n      <button class=\"floating-btn floating-btn-indigo\" (click)=\"scrollToBottom()\" title=\"到底部\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 14l-7 7m0 0l-7-7m7 7V3\"></path>\r\n        </svg>\r\n\r\n        <!-- Tooltip -->\r\n        <div class=\"floating-btn-tooltip\">\r\n          到底部\r\n        </div>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAyB,gBAAgB;AAKlE,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA4CC,mBAAmB,QAAmD,yBAAyB;AAG3I,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;;IC2CjDC,EAAA,CAAAC,cAAA,iBAC8F;IADlED,EAAA,CAAAE,UAAA,mBAAAC,8FAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;;IAEjDT,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,eACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAIXX,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,mBAAAR,MAAA,CAAAS,0BAAA,CAAAC,MAAA,SAAAV,MAAA,CAAAW,kBAAA,CAAAD,MAAA,yBACF;;;;;;IApBEhB,EAHN,CAAAC,cAAA,cAA6D,cAClB,cACV,cACuD;;IAChFD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eACyD;IAE7DV,EADE,CAAAW,YAAA,EAAM,EACF;;IACNX,EAAA,CAAAC,cAAA,gBAC2I;IADxHD,EAAA,CAAAkB,gBAAA,2BAAAC,4FAAAC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsB,kBAAA,CAAAhB,MAAA,CAAAiB,WAAA,EAAAH,MAAA,MAAAd,MAAA,CAAAiB,WAAA,GAAAH,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAyB;IAACpB,EAAA,CAAAE,UAAA,mBAAAsB,oFAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmB,QAAA,EAAU;IAAA,EAAC;IAAjEzB,EAAA,CAAAW,YAAA,EAC2I;IAC3IX,EAAA,CAAA0B,UAAA,IAAAC,qEAAA,qBAC8F;IAMhG3B,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAAE,kEAAA,kBAAuD;IAI3D5B,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAhBmBX,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAiB,WAAA,CAAyB;IAEnCvB,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiB,WAAA,CAAiB;IAUQvB,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiB,WAAA,CAAiB;;;;;;IAyBjDvB,EADF,CAAAC,cAAA,cAA2E,eACzB;IAAAD,EAAA,CAAAY,MAAA,gCAAK;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAE1DX,EADF,CAAAC,cAAA,cAAoG,iBAG7D;IAAnCD,EAAA,CAAAE,UAAA,mBAAA6B,qFAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;;IACrBjC,EAAA,CAAAC,cAAA,cACsB;IACpBD,EAAA,CAAAU,SAAA,eAEO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;IACTX,EAAA,CAAAC,cAAA,iBAEuC;IAArCD,EAAA,CAAAE,UAAA,mBAAAgC,qFAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6B,WAAA,EAAa;IAAA,EAAC;;IACvBnC,EAAA,CAAAC,cAAA,cACsB;IACpBD,EAAA,CAAAU,SAAA,eAAgG;IAKxGV,EAJM,CAAAW,YAAA,EAAM,EACC,EAEL,EACF;;;;;;IAqBZX,EADF,CAAAC,cAAA,cAA8F,cACrD;;IACrCD,EAAA,CAAAC,cAAA,cAAwG;IACtGD,EAAA,CAAAU,SAAA,eACyD;IAC3DV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAY,MAAA,mEAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAClEX,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAY,MAAA,6GAAiB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACnDX,EAAA,CAAAC,cAAA,iBAC0F;IADlFD,EAAA,CAAAE,UAAA,mBAAAkC,qFAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE7BT,EAAA,CAAAY,MAAA,iCACF;IAEJZ,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;;;;;;IAwCMX,EAAA,CAAAC,cAAA,iBAE8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAoC,wGAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAmC,GAAA;MAAA,MAAAC,MAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAkC,KAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,cAAA,CAAAF,MAAA,CAAmB;IAAA,EAAC;;IAC7BxC,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,eAEO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAyC,wGAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA;MAAA,MAAAJ,MAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAkC,KAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuC,YAAA,CAAAL,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/BxC,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAA4C,wGAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAP,MAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAkC,KAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuC,YAAA,CAAAL,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/BxC,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eAA8F;IAElGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IA2BLX,EAAA,CAAAU,SAAA,eAC0F;;;;;;IAAxFV,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAgD,WAAA,OAAA1C,MAAA,CAAA2C,eAAA,CAAAC,cAAA,IAAAlD,EAAA,CAAAmD,aAAA,CAAkD;;;;;;IAgBpDnD,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAAkD,oHAAAhC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAH,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAiD,SAAA,CAAAL,cAAA,CAAsB;MAAA,OAAAlD,EAAA,CAAAQ,WAAA,CAAEY,MAAA,CAAAoC,eAAA,EAAwB;IAAA,EAAC;;IAC1DxD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAAuD,oHAAArC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAR,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqD,SAAA,CAAAT,cAAA,CAAsB;MAAA,OAAAlD,EAAA,CAAAQ,WAAA,CAAEY,MAAA,CAAAoC,eAAA,EAAwB;IAAA,EAAC;;IAC1DxD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAGTX,EAAA,CAAAC,cAAA,eACoH;IAClHD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,OAAAoC,cAAA,CAAAU,iBAAA,mBAAAV,cAAA,CAAAW,WAAA,CAAA7C,MAAA,MACF;;;;;;IAKAhB,EAAA,CAAAC,cAAA,kBAMiF;IAA/ED,EAAA,CAAAE,UAAA,mBAAA4D,2HAAA;MAAA,MAAAC,KAAA,GAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA,EAAAvB,KAAA;MAAA,MAAAS,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2D,cAAA,CAAAf,cAAA,EAAAa,KAAA,CAA8B;IAAA,EAAC;IACxC/D,EAAA,CAAAU,SAAA,eACiC;;IACnCV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAJPX,EAHA,CAAAkE,WAAA,oBAAAH,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OAAoE,oBAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACA,WAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACT,kBAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACO;IACzB5D,EAAA,CAAA8B,UAAA,+CAAAiC,KAAA,8BAAqC;IAE5E/D,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAgD,WAAA,QAAAmB,YAAA,GAAAnE,EAAA,CAAAmD,aAAA,CAA8B;;;;;IATpCnD,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAA0B,UAAA,IAAA0C,kGAAA,uBAMiF;IAInFpE,EAAA,CAAAW,YAAA,EAAM;;;;IAVyBX,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,YAAAoB,cAAA,CAAAW,WAAA,CAA4B;;;;;;IA/C3D7D,EAFF,CAAAC,cAAA,eAA4F,eAIlD;IAAtCD,EAAA,CAAAE,UAAA,mBAAAmE,wGAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAApB,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2D,cAAA,CAAAf,cAAA,CAA2B;IAAA,EAAC;IACrClD,EAAA,CAAA0B,UAAA,IAAA6C,wFAAA,mBAC0F;IAMtFvE,EAHJ,CAAAC,cAAA,eACwI,eAC9C,eACM;;IAC1FD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,gBACmF;IAI3FV,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAsBNX,EAnBA,CAAA0B,UAAA,IAAA8C,2FAAA,sBAE2E,IAAAC,2FAAA,sBASA,KAAAC,yFAAA,mBASyC;IAGtH1E,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,KAAAiD,yFAAA,mBAA6F;IAY/F3E,EAAA,CAAAW,YAAA,EAAM;;;;;IAtDoDX,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA2C,eAAA,CAAAC,cAAA,EAAkC;IAgB/ElD,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAW,WAAA,CAAA7C,MAAA,KAAwC;IASxChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAW,WAAA,CAAA7C,MAAA,KAAwC;IAU3ChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAW,WAAA,CAAA7C,MAAA,KAAwC;IAO1ChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAW,WAAA,CAAA7C,MAAA,KAAwC;;;;;IAchDhB,EAAA,CAAAC,cAAA,eACoJ;;IAClJD,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAChC;;;;;IAsCEX,EAAA,CAAAC,cAAA,gBACwG;IACtGD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA4E,kBAAA,MAAAC,QAAA,CAAAC,WAAA,MACF;;;;;IANJ9E,EADF,CAAAC,cAAA,eAA6F,iBAC/B;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IAEpEX,EADF,CAAAC,cAAA,eAA8D,eAC1B;IAChCD,EAAA,CAAA0B,UAAA,IAAAqD,0FAAA,oBACwG;IAG1G/E,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;IATuBX,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,YAAAoB,cAAA,CAAA8B,aAAA,CAA4B;IAMnDhF,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4E,kBAAA,aAAA1B,cAAA,CAAA8B,aAAA,CAAAhE,MAAA,qCACF;;;;;IAwBEhB,EAAA,CAAAC,cAAA,qBAA8D;IAC5DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAY;;;;IAFmCX,EAAA,CAAA8B,UAAA,UAAAmD,QAAA,CAAc;IAC3DjF,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA4E,kBAAA,MAAAK,QAAA,CAAAC,KAAA,MACF;;;;;;IAsCJlF,EAFF,CAAAC,cAAA,eACuG,eACzE;IAG1BD,EAFA,CAAAU,SAAA,eACuB,eAGjB;IACRV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,iBAG0F;IAAxFD,EAAA,CAAAE,UAAA,kBAAAiF,gHAAA/D,MAAA;MAAA,MAAAgE,KAAA,GAAApF,EAAA,CAAAI,aAAA,CAAAiF,IAAA,EAAA5C,KAAA;MAAA,MAAAS,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAgF,UAAA,CAAAlE,MAAA,EAAAgE,KAAA,EAAAlC,cAAA,CAAkC;IAAA,EAAC;IAH7ClD,EAAA,CAAAW,YAAA,EAG0F;IAC1FX,EAAA,CAAAC,cAAA,kBAE6F;IAA3FD,EAAA,CAAAE,UAAA,mBAAAqF,kHAAA;MAAA,MAAAC,WAAA,GAAAxF,EAAA,CAAAI,aAAA,CAAAiF,IAAA,EAAA/B,SAAA;MAAA,MAAAJ,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmF,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAxC,cAAA,CAAoC;IAAA,EAAC;;IAC9ClD,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAU,SAAA,eAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAY,MAAA,iCACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;;;;;;;IAnBAX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA8B,UAAA,QAAA0D,WAAA,CAAAG,IAAA,EAAA3F,EAAA,CAAAmD,aAAA,CAAoB;IAOYnD,EAAA,CAAAa,SAAA,GAAsB;IACZb,EADV,CAAA8B,UAAA,UAAA0D,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAvF,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAC+B;IAGxC7F,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAA8B,UAAA,cAAAmE,QAAA,GAAA3F,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA2C;;;;;IAhBhGjG,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAA0B,UAAA,IAAAwE,yFAAA,mBACuG;IAuBzGlG,EAAA,CAAAW,YAAA,EAAM;;;;IAxBqBX,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAA8B,UAAA,YAAAoB,cAAA,CAAAiD,YAAA,CAA6B;;;;;IA6BtDnG,EAFF,CAAAC,cAAA,eAC6G,iBACpD;IAAAD,EAAA,CAAAY,MAAA,qCAAK;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IACpEX,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAU,SAAA,eACmD;;IACnDV,EAAA,CAAAU,SAAA,eAEM;IAEVV,EADE,CAAAW,YAAA,EAAM,EACF;;;;IALAX,EAAA,CAAAa,SAAA,GAAgD;IAAhDb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAgD,WAAA,OAAAE,cAAA,CAAAkD,cAAA,GAAApG,EAAA,CAAAmD,aAAA,CAAgD;;;;;IAOtDnD,EAAA,CAAAC,cAAA,eAE2I;;IACzID,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC9BZ,EAD8B,CAAAW,YAAA,EAAO,EAC/B;;;;;IAkCRX,EAAA,CAAAC,cAAA,eAAiE;;IAC/DD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;;IAmBFX,EAAA,CAAAC,cAAA,uBAEkG;IADhGD,EAAA,CAAAkB,gBAAA,2BAAAmF,qJAAAjF,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAAC,UAAA,GAAAvG,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAJ,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAAtD,EAAA,CAAAsB,kBAAA,CAAA4B,cAAA,CAAAsD,kBAAA,CAAAD,UAAA,GAAAnF,MAAA,MAAA8B,cAAA,CAAAsD,kBAAA,CAAAD,UAAA,IAAAnF,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAoD;IACvCpB,EAAA,CAAAE,UAAA,2BAAAmG,qJAAAjF,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAAC,UAAA,GAAAvG,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAJ,cAAA,GAAAlD,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAmG,sBAAA,CAAArF,MAAA,EAAAmF,UAAA,EAAArD,cAAA,CAAmD;IAAA,EAAC;IACpFlD,EAAA,CAAAW,YAAA,EAAc;;;;;;IAFZX,EAAA,CAAA6B,gBAAA,YAAAqB,cAAA,CAAAsD,kBAAA,CAAAD,UAAA,EAAoD;IAACvG,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,CAAkC;;;;;IAH3F/F,EAAA,CAAAC,cAAA,iBACgG;IAC9FD,EAAA,CAAA0B,UAAA,IAAAgF,+GAAA,2BAEkG;IAElG1G,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAY,MAAA,GAAY;IAC1CZ,EAD0C,CAAAW,YAAA,EAAO,EACzC;;;;;IALQX,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAsD,kBAAA,CAAoC;IAItBxG,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAA2G,iBAAA,CAAAJ,UAAA,CAAY;;;;;IAR5CvG,EAAA,CAAAC,cAAA,eACoF;IAClFD,EAAA,CAAA0B,UAAA,IAAAkF,iGAAA,qBACgG;IAOlG5G,EAAA,CAAAW,YAAA,EAAM;;;;IARsBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAuG,kBAAA,CAAqB;;;;;IAW/C7G,EAAA,CAAAC,cAAA,eAA8B;;IAC5BD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;IA9BRX,EAFF,CAAAC,cAAA,eACkD,eACF;;IAC5CD,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAChDZ,EADgD,CAAAW,YAAA,EAAK,EAC/C;IAcNX,EAZA,CAAA0B,UAAA,IAAAoF,yFAAA,mBACoF,IAAAC,iGAAA,gCAAA/G,EAAA,CAAAgH,sBAAA,CAWtD;IAWhChH,EAAA,CAAAW,YAAA,EAAM;;;;;IAtBDX,EAAA,CAAAa,SAAA,GAA2D;IAAAb,EAA3D,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuG,kBAAA,IAAAvG,MAAA,CAAAuG,kBAAA,CAAA7F,MAAA,KAA2D,aAAAiG,mBAAA,CAAoB;;;;;;IApShFjH,EANR,CAAAC,cAAA,eAAkD,eACG,eAGtB,eACF,aACoB;;IACvCD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAAmD;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC3DZ,EAD2D,CAAAW,YAAA,EAAQ,EAC7D;IAgENX,EA9DA,CAAA0B,UAAA,IAAAwF,kFAAA,oBAA4F,KAAAC,mFAAA,mBA+DwD;IASxJnH,EADE,CAAAW,YAAA,EAAM,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAA2B,gBACF,gBACyB;;IAC5CD,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IACzDZ,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAKFX,EAFJ,CAAAC,cAAA,gBAAuB,gBACF,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,gBAA0F,iBAEO;IAC7FD,EAAA,CAAAY,MAAA,mCACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,kBAGgD;IADtCD,EAAA,CAAAkB,gBAAA,2BAAAkG,6GAAAhG,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAAtD,EAAA,CAAAsB,kBAAA,CAAA4B,cAAA,CAAAoE,YAAA,EAAAlG,MAAA,MAAA8B,cAAA,CAAAoE,YAAA,GAAAlG,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAsC;IAGpDpB,EALI,CAAAW,YAAA,EAGgD,EAC5C,EACF;IAGNX,EAAA,CAAA0B,UAAA,KAAA6F,mFAAA,mBAA6F;IAiBzFvH,EAFJ,CAAAC,cAAA,gBAAmD,gBAC9B,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,gBAAsB,kBAIyE;IADhED,EAAA,CAAAkB,gBAAA,2BAAAsG,6GAAApG,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAAtD,EAAA,CAAAsB,kBAAA,CAAA4B,cAAA,CAAAuE,cAAA,EAAArG,MAAA,MAAA8B,cAAA,CAAAuE,cAAA,GAAArG,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAwC;IAGzEpB,EALI,CAAAW,YAAA,EAG6F,EACzF,EACF;IAGJX,EADF,CAAAC,cAAA,gBAAmB,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,kCAAM;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IACrEX,EAAA,CAAAC,cAAA,sBAI8C;IAH5CD,EAAA,CAAAkB,gBAAA,2BAAAwG,iHAAAtG,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAAtD,EAAA,CAAAsB,kBAAA,CAAA4B,cAAA,CAAAyE,eAAA,EAAAvG,MAAA,MAAA8B,cAAA,CAAAyE,eAAA,GAAAvG,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAyC;IAEzCpB,EAAA,CAAAE,UAAA,4BAAA0H,kHAAA;MAAA5H,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAAuH,mBAAA,CAAA3E,cAAA,CAAgC;IAAA,EAAC;IAEnDlD,EAAA,CAAA0B,UAAA,KAAAoG,yFAAA,yBAA8D;IAQ1E9H,EALU,CAAAW,YAAA,EAAY,EACR,EACF,EACF,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAA2B,gBACF,cACoB;;IACvCD,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IACzDZ,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAGNX,EAAA,CAAAC,cAAA,mBAEiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAA6H,sGAAA;MAAA/H,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAW,aAAA,GAAAhI,EAAA,CAAAiI,WAAA;MAAA,OAAAjI,EAAA,CAAAQ,WAAA,CAASwH,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;;IAC9DlI,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kDAAO;IACfZ,EADe,CAAAW,YAAA,EAAO,EACb;IACTX,EAAA,CAAAC,cAAA,qBAC4C;IADCD,EAAA,CAAAE,UAAA,oBAAAiI,sGAAA/G,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8H,WAAA,CAAAhH,MAAA,EAAA8B,cAAA,CAAgC;IAAA,EAAC;IAAxFlD,EAAA,CAAAW,YAAA,EAC4C;IA2C5CX,EAxCA,CAAA0B,UAAA,KAAA2G,mFAAA,mBAA+F,KAAAC,mFAAA,mBA6Bc,KAAAC,mFAAA,mBAa8B;IAUjJvI,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAAkB,gBACM,gBAC4B;IAC9CD,EAAA,CAAAU,SAAA,gBAAmD;IACrDV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,gBAAkD,iBACM;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAGhEZ,EAHgE,CAAAW,YAAA,EAAO,EAC7D,EACF,EACF;IAGFX,EAFJ,CAAAC,cAAA,gBAAmD,gBACa,gBACd;;IAC5CD,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAC9CZ,EAD8C,CAAAW,YAAA,EAAK,EAC7C;IACNX,EAAA,CAAAC,cAAA,kCAI2C;IADzCD,EAAA,CAAAE,UAAA,6BAAAsI,+HAAApH,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiH,IAAA;MAAA,MAAAnE,cAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAmBF,MAAA,CAAAmI,0BAAA,CAA2BnI,MAAA,CAAAoI,qBAAA,CAAAtH,MAAA,CAA6B,EAAA8B,cAAA,CAAc;IAAA,EAAC;IAE5FlD,EAAA,CAAAW,YAAA,EAAwB;IAGxBX,EAAA,CAAA0B,UAAA,KAAAiH,mFAAA,mBAAiE;IASnE3I,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,KAAAkH,mFAAA,mBACkD;IAmCtD5I,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;;;;;;IAnTQX,EAAA,CAAAa,SAAA,GAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAW,WAAA,IAAAX,cAAA,CAAAW,WAAA,CAAA7C,MAAA,KAAmE;IA8DnEhB,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAA8B,UAAA,UAAAoB,cAAA,CAAAW,WAAA,IAAAX,cAAA,CAAAW,WAAA,CAAA7C,MAAA,OAAsE;IA2BjEhB,EAAA,CAAAa,SAAA,IAA6B;IAA7Bb,EAAA,CAAA8B,UAAA,0BAAAU,MAAA,CAA6B;IAOfxC,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,yBAAAU,MAAA,CAA4B;IAErCxC,EAAA,CAAA6B,gBAAA,YAAAqB,cAAA,CAAAoE,YAAA,CAAsC;IAC9CtH,EAAA,CAAA8B,UAAA,cAAA+G,QAAA,GAAAvI,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAA8C,QAAA,KAAA7C,SAAA,GAAA6C,QAAA,SAA2C;IAK7B7I,EAAA,CAAAa,SAAA,EAAuE;IAAvEb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAA8B,aAAA,IAAA9B,cAAA,CAAA8B,aAAA,CAAAhE,MAAA,KAAuE;IAiBhFhB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA8B,UAAA,4BAAAU,MAAA,CAA+B;IAGfxC,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,2BAAAU,MAAA,CAA8B;IAEtBxC,EAAA,CAAA6B,gBAAA,YAAAqB,cAAA,CAAAuE,cAAA,CAAwC;IACnEzH,EAAA,CAAA8B,UAAA,aAAAoB,cAAA,CAAAyE,eAAA,CAAAmB,KAAA,YAAAC,QAAA,GAAAzI,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAAgD,QAAA,KAAA/C,SAAA,GAAA+C,QAAA,UAAwF;IAKrF/I,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAA8B,UAAA,oBAAAU,MAAA,CAAuB;IAEExC,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAA8B,UAAA,mBAAAU,MAAA,CAAsB;IACpDxC,EAAA,CAAA6B,gBAAA,YAAAqB,cAAA,CAAAyE,eAAA,CAAyC;IAGzC3H,EAAA,CAAA8B,UAAA,cAAAkH,QAAA,GAAA1I,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAAiD,QAAA,KAAAhD,SAAA,GAAAgD,QAAA,SAA2C;IACfhJ,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA2I,cAAA,CAAiB;IAyBnDjJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,CAAkC;IAY9B/F,EAAA,CAAAa,SAAA,GAAqE;IAArEb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAiD,YAAA,IAAAjD,cAAA,CAAAiD,YAAA,CAAAnF,MAAA,KAAqE;IA6BxEhB,EAAA,CAAAa,SAAA,EAAwG;IAAxGb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAkD,cAAA,MAAAlD,cAAA,CAAAiD,YAAA,IAAAjD,cAAA,CAAAiD,YAAA,CAAAnF,MAAA,QAAwG;IAYxGhB,EAAA,CAAAa,SAAA,EAAyG;IAAzGb,EAAA,CAAA8B,UAAA,UAAAoB,cAAA,CAAAkD,cAAA,MAAAlD,cAAA,CAAAiD,YAAA,IAAAjD,cAAA,CAAAiD,YAAA,CAAAnF,MAAA,QAAyG;IAkCvFhB,EAAA,CAAAa,SAAA,IAA6B;IAInCb,EAJM,CAAA8B,UAAA,iBAAAxB,MAAA,CAAA4I,YAAA,CAA6B,6DAA0B,cAAAC,QAAA,GAAA7I,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAAoD,QAAA,KAAAnD,SAAA,GAAAmD,QAAA,SACjC,0BAA0B,YAAAjG,cAAA,CAAAkG,wBAAA,CACrB,0BAER;IAIXpJ,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+I,aAAA,CAAArI,MAAA,OAAgC;IAa9DhB,EAAA,CAAAa,SAAA,EAA6C;IAA7Cb,EAAA,CAAA8B,UAAA,SAAAoB,cAAA,CAAAyE,eAAA,CAAAmB,KAAA,OAA6C;;;;;;IApWxD9I,EAAA,CAAAsJ,uBAAA,GAAsF;IAQ5EtJ,EAPR,CAAAC,cAAA,cAC2H,cAE9B,aAC1C,cACJ,iBAIyD;IAA9FD,EAAA,CAAAE,UAAA,mBAAAqJ,8FAAA;MAAA,MAAArG,cAAA,GAAAlD,EAAA,CAAAI,aAAA,CAAAoJ,GAAA,EAAAlG,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmJ,kBAAA,CAAAvG,cAAA,CAA+B;IAAA,EAAC;;IACzClD,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAU,SAAA,eAAgG;IAEpGV,EADE,CAAAW,YAAA,EAAM,EACC;;IAETX,EAAA,CAAAC,cAAA,cACyG;IACvGD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,eAAoB,cAC8B;IAC9CD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAY,MAAA,IAAiB;IAEtDZ,EAFsD,CAAAW,YAAA,EAAI,EAClD,EACF;IAGJX,EAFF,CAAAC,cAAA,eAAyC,gBAEgC;IACrED,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAGPX,EAAA,CAAAC,cAAA,eAAyC;IAoBvCD,EAlBA,CAAA0B,UAAA,KAAAgI,+EAAA,qBAE8C,KAAAC,+EAAA,qBAUE,KAAAC,+EAAA,qBAQA;IAQxD5J,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAGNX,EAAA,CAAA0B,UAAA,KAAAmI,4EAAA,oBAAkD;IAmUpD7J,EAAA,CAAAW,YAAA,EAAM;;;;;;;;IAvYDX,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA8B,UAAA,sBAAAU,MAAA,CAAyB;IASsBxC,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAA8B,UAAA,UAAAoB,cAAA,CAAA4G,WAAA,2DAAmD;IAE3F9J,EAAA,CAAAa,SAAA,EAA4C;IAA5Cb,EAAA,CAAAkE,WAAA,eAAAhB,cAAA,CAAA4G,WAAA,CAA4C;IAQ9C9J,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4E,kBAAA,MAAApC,MAAA,UACF;IAGIxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4E,kBAAA,MAAA1B,cAAA,CAAA6G,SAAA,MACF;IACiC/J,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAA4E,kBAAA,+BAAApC,MAAA,SAAiB;IAMlDxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4E,kBAAA,OAAA1B,cAAA,CAAAyE,eAAA,kBAAAzE,cAAA,CAAAyE,eAAA,CAAAzC,KAAA,+BACF;IAKWlF,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAA8B,UAAA,YAAA+G,QAAA,GAAAvI,MAAA,CAAAwF,YAAA,kBAAAxF,MAAA,CAAAwF,YAAA,CAAAC,OAAA,cAAA8C,QAAA,KAAA7C,SAAA,GAAA6C,QAAA,UAAuC;IAUvC7I,EAAA,CAAAa,SAAA,EAAa;IAAbb,EAAA,CAAA8B,UAAA,SAAAU,MAAA,KAAa;IAQbxC,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAA8B,UAAA,SAAAU,MAAA,GAAAlC,MAAA,CAAAW,kBAAA,CAAAD,MAAA,KAAyC;IAaxChB,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,UAAAoB,cAAA,CAAA4G,WAAA,CAA8B;;;;;IAyXpD9J,EAAA,CAAAU,SAAA,eAC0F;;;;;;IAAxFV,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAgD,WAAA,OAAA1C,MAAA,CAAA2C,eAAA,CAAA+G,eAAA,IAAAhK,EAAA,CAAAmD,aAAA,CAAkD;;;;;;IAGpDnD,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAA+J,6GAAA;MAAAjK,EAAA,CAAAI,aAAA,CAAA8J,IAAA;MAAA,MAAAF,eAAA,GAAAhK,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6J,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrChK,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAmG;IAEvGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAAkK,6GAAA;MAAApK,EAAA,CAAAI,aAAA,CAAAiK,IAAA;MAAA,MAAAL,eAAA,GAAAhK,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgK,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrChK,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAgG;IAEpGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAMTX,EAFF,CAAAC,cAAA,eACqJ,cAC1G;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAY,MAAA,GACE;IAExCZ,EAFwC,CAAAW,YAAA,EAAO,EACvC,EACF;;;;IAHgCX,EAAA,CAAAa,SAAA,GACE;IADFb,EAAA,CAAAc,kBAAA,MAAAkJ,eAAA,CAAApG,iBAAA,mBAAAoG,eAAA,CAAAnG,WAAA,CAAA7C,MAAA,KACE;;;;;;IAoBpChB,EAAA,CAAAC,cAAA,kBAOkF;IAAhFD,EAAA,CAAAE,UAAA,mBAAAqK,oHAAA;MAAA,MAAAC,KAAA,GAAAxK,EAAA,CAAAI,aAAA,CAAAqK,IAAA,EAAAhI,KAAA;MAAA,MAAAuH,eAAA,GAAAhK,EAAA,CAAAO,aAAA,IAAA+C,SAAA;MAAA,OAAAtD,EAAA,CAAAQ,WAAA,CAAAwJ,eAAA,CAAApG,iBAAA,GAAA4G,KAAA;IAAA,EAA2C;IAC3CxK,EAAA,CAAAU,SAAA,eAAyG;;IAC3GV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAHPX,EAJA,CAAAkE,WAAA,iBAAAsG,KAAA,MAAAR,eAAA,CAAApG,iBAAA,OAAiE,oBAAA4G,KAAA,MAAAR,eAAA,CAAApG,iBAAA,OACG,WAAA4G,KAAA,MAAAR,eAAA,CAAApG,iBAAA,OACT,eAAA4G,KAAA,MAAAR,eAAA,CAAApG,iBAAA,OACI,oBAAA4G,KAAA,MAAAR,eAAA,CAAApG,iBAAA,OACK;IACxB5D,EAAA,CAAA8B,UAAA,mCAAA0I,KAAA,8BAAmC;IACLxK,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAgD,WAAA,QAAA0H,YAAA,GAAA1K,EAAA,CAAAmD,aAAA,CAA8B;;;;;IAT5GnD,EAFF,CAAAC,cAAA,eAC8I,eACtE;IACpED,EAAA,CAAA0B,UAAA,IAAAiJ,2FAAA,uBAOkF;IAItF3K,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAX2BX,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,YAAAkI,eAAA,CAAAnG,WAAA,CAA4B;;;;;;IAtEjE7D,EAAA,CAAAC,cAAA,eAEiG;IAAxDD,EAAvC,CAAAE,UAAA,mBAAA0K,iGAAA;MAAA5K,EAAA,CAAAI,aAAA,CAAAyK,IAAA;MAAA,MAAAb,eAAA,GAAAhK,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAA3J,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAyK,IAAA;MAAA,MAAAb,eAAA,GAAAhK,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAA0K,SAAA,CAAA5J,MAAA,EAAA4I,eAAA,CAA8B;IAAA,EAAC;IAGjFhK,EAAA,CAAAC,cAAA,kBAEkE;IAAhED,EAAA,CAAAE,UAAA,mBAAA+K,oGAAA;MAAAjL,EAAA,CAAAI,aAAA,CAAAyK,IAAA;MAAA,MAAAb,eAAA,GAAAhK,EAAA,CAAAO,aAAA,GAAA+C,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtChK,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAwG;IAE5GV,EADE,CAAAW,YAAA,EAAM,EACC;;IAGTX,EAAA,CAAAC,cAAA,eACqC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAgL,iGAAA9J,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAyK,IAAA;MAAA,OAAA7K,EAAA,CAAAQ,WAAA,CAASY,MAAA,CAAAoC,eAAA,EAAwB;IAAA,EAAC;IAGlCxD,EAAA,CAAAC,cAAA,eAAgF;IAa9ED,EAZA,CAAA0B,UAAA,IAAAyJ,iFAAA,mBAC0F,IAAAC,oFAAA,sBAK5B,IAAAC,oFAAA,sBAQA;IAKhErL,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAA4J,iFAAA,mBACqJ;IAenJtL,EAFF,CAAAC,cAAA,gBACkJ,cACvG;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBACuE;IACzEV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAY,MAAA,IAA0B;IAExDZ,EAFwD,CAAAW,YAAA,EAAO,EACvD,EACF;IAGNX,EAAA,CAAA0B,UAAA,KAAA6J,kFAAA,mBAC8I;IAelJvL,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IA/DsDX,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA2C,eAAA,CAAA+G,eAAA,EAAkC;IAG/EhK,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAkI,eAAA,CAAAnG,WAAA,IAAAmG,eAAA,CAAAnG,WAAA,CAAA7C,MAAA,KAAmE;IAQnEhB,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAkI,eAAA,CAAAnG,WAAA,IAAAmG,eAAA,CAAAnG,WAAA,CAAA7C,MAAA,KAAmE;IAUxEhB,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAkI,eAAA,CAAAnG,WAAA,IAAAmG,eAAA,CAAAnG,WAAA,CAAA7C,MAAA,KAAmE;IAqB3ChB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAA4E,kBAAA,KAAAoF,eAAA,CAAAD,SAAA,MAA0B;IAKlD/J,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAkI,eAAA,CAAAnG,WAAA,IAAAmG,eAAA,CAAAnG,WAAA,CAAA7C,MAAA,KAAmE;;;;;IApE/EhB,EAAA,CAAAsJ,uBAAA,GAA8E;IAC5EtJ,EAAA,CAAA0B,UAAA,IAAA8J,2EAAA,oBAEiG;;;;;IAF3FxL,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAA8B,UAAA,SAAAkI,eAAA,CAAAyB,WAAA,CAA6B;;;;;;IA+F7BzL,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAGNX,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAU,SAAA,gBAAgG;IAClGV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAWRX,EADF,CAAAC,cAAA,cAAkE,kBACkD;IAAnED,EAAA,CAAAE,UAAA,mBAAAwL,qFAAA;MAAA1L,EAAA,CAAAI,aAAA,CAAAuL,IAAA;MAAA,MAAArL,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsL,aAAA,EAAe;IAAA,EAAC;;IACtE5L,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IAGNX,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAY,MAAA,6CACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;;;;IAZqEX,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAuL,YAAA,CAAyB;;;ADpoBxG,OAAM,MAAOC,4CAA6C,SAAQlM,aAAa;EAC7EmM,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,MAAc,EACdC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,aAA2B,EAC3BC,GAAsB,EACtBC,uBAAgD;IAExD,KAAK,CAACb,MAAM,CAAC;IAfL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,uBAAuB,GAAvBA,uBAAuB;IAIjC,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAE/H,KAAK,EAAE,KAAK;MAAE4D,KAAK,EAAE/I,aAAa,CAACmN;IAAG,CAAE,EAC1C;MAAEhI,KAAK,EAAE,KAAK;MAAE4D,KAAK,EAAE/I,aAAa,CAACoN;IAAG,CAAE,CAC3C;IAiBD,KAAAlE,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE5D,KAAK,EAAE;KAClB,EACD;MACE4D,KAAK,EAAE,CAAC;MAAE5D,KAAK,EAAE;KAClB,EAAE;MACD4D,KAAK,EAAE,CAAC;MAAE5D,KAAK,EAAE;KAClB,CAAC;IACJ,KAAA2B,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAEjC,KAAAuG,MAAM,GAAkB,IAAI;IAC5B,KAAAvB,YAAY,GAAY,KAAK;IA+C7B,KAAAwB,aAAa,GAA+B,EAAE;IAC9C,KAAA7G,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAA0C,YAAY,GAAQ,EAAE,CAAC,CAAC;IA0LxB,KAAApD,YAAY,GAA8B,IAAI;IAC9C,KAAAwH,KAAK,GAAY,IAAI;IAiFrB,KAAArM,kBAAkB,GAAkC,EAAE;IACtD,KAAAF,0BAA0B,GAAkC,EAAE;IAC9D,KAAAQ,WAAW,GAAW,EAAE;EAvWxB;EAUA;EACA,IAAIgM,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACP,kBAAkB,CAACQ,IAAI,CAACD,MAAM,IAChDA,MAAM,CAAC1E,KAAK,KAAK,IAAI,CAACgE,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOQ,MAAM,GAAG,QAAQA,MAAM,CAACtI,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAwI,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACV,kBAAkB,CAACW,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAAC1E,KAAK,KAAK6E,UAAU,CAAC,EAAE;MACvE,IAAI,CAACb,iCAAiC,CAACE,WAAW,GAAGW,UAAU;MAC/D;MACA,IAAI,CAACb,iCAAiC,CAACC,SAAS,GAAGY,UAAU;IAC/D;EACF;EAiBSE,QAAQA,CAAA;IACf,IAAI,CAAC5B,KAAK,CAAC6B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,WAAW,GAAGH,MAAM,CAACE,GAAG,CAAC,QAAQ,CAAC;QACxC,MAAMxI,EAAE,GAAGuI,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,MAAMb,MAAM,GAAGe,WAAW,GAAG,CAACA,WAAW,GAAG,IAAI;QAChD,IAAI,CAACC,WAAW,GAAG1I,EAAE;QACrB,IAAI,CAAC0H,MAAM,GAAGA,MAAM;QAGpB,IAAI,IAAI,CAACgB,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACA,IAAI,CAAClC,OAAO,CAACmC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACtC,KAAK,CAACuC,WAAW,CAACT,SAAS,CAACS,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAACd,cAAc,CAACe,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAACjG,KAAU,EAAEkG,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACnG,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOmG,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA7G,WAAWA,CAAC8G,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAChJ,YAAY,CAACnF,MAAM,GAAG,CAAC,EAAE;UACxCmO,YAAY,CAAChJ,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAImK,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBlK,IAAI,EAAEwJ,IAAI,CAACxJ,IAAI,CAACmK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BpK,IAAI,EAAEgK,SAAS;YACfK,SAAS,EAAE,IAAI,CAAC1D,eAAe,CAAC2D,gBAAgB,CAACb,IAAI,CAACxJ,IAAI,CAAC;YAC3DsK,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAChJ,YAAY,CAACgK,IAAI,CAAC;YAC7BzK,EAAE,EAAE,IAAImK,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBlK,IAAI,EAAEwJ,IAAI,CAACxJ,IAAI,CAACmK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BpK,IAAI,EAAEgK,SAAS;YACfK,SAAS,EAAE,IAAI,CAAC1D,eAAe,CAAC2D,gBAAgB,CAACb,IAAI,CAACxJ,IAAI,CAAC;YAC3DsK,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACvG,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEArD,WAAWA,CAAC2K,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAChJ,YAAY,CAACnF,MAAM,EAAE;MACpCmO,YAAY,CAAChJ,YAAY,GAAGgJ,YAAY,CAAChJ,YAAY,CAACkK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC5K,EAAE,IAAI0K,SAAS,CAAC;IAC7F;EACF;EACA9K,UAAUA,CAAC4J,KAAU,EAAEzM,KAAa,EAAE0M,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACyN,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACyN,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACyN,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACvG,KAAK,GAAG,GAAG,GAAGqG,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACuN,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACyN,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAChJ,YAAY,CAAC1D,KAAK,CAAC,CAACyN,KAAK,GAAGS,OAAO;EAClD;EAEA;EACAhN,SAASA,CAACkN,WAAgB;IACxB,IAAIA,WAAW,CAAChN,WAAW,IAAIgN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,EAAE;MACjE6P,WAAW,CAACjN,iBAAiB,GAAG,CAACiN,WAAW,CAACjN,iBAAiB,GAAG,CAAC,IAAIiN,WAAW,CAAChN,WAAW,CAAC7C,MAAM;IACtG;EACF;EAEAuC,SAASA,CAACsN,WAAgB;IACxB,IAAIA,WAAW,CAAChN,WAAW,IAAIgN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,EAAE;MACjE6P,WAAW,CAACjN,iBAAiB,GAAGiN,WAAW,CAACjN,iBAAiB,KAAK,CAAC,GAC/DiN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,GAClC6P,WAAW,CAACjN,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAX,eAAeA,CAAC4N,WAAgB;IAC9B,IAAIA,WAAW,CAAChN,WAAW,IAAIgN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,EAAE;MACjE;MACA,MAAMyB,KAAK,GAAGoO,WAAW,CAACjN,iBAAiB,KAAKoC,SAAS,GAAG6K,WAAW,CAACjN,iBAAiB,GAAG,CAAC;MAC7F,MAAMkN,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACxO,KAAK,EAAEoO,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,CAAC,CAAC;MAEnFkQ,OAAO,CAACC,GAAG,CAAC,qCAAqCN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,SAAS8P,UAAU,UAAUD,WAAW,CAAChN,WAAW,CAACiN,UAAU,CAAC,EAAE,CAAC;MAElJ,OAAOD,WAAW,CAAChN,WAAW,CAACiN,UAAU,CAAC;IAC5C;IACAI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,OAAO,IAAI;EACb;EAEA;EACAlN,cAAcA,CAAC4M,WAAgB,EAAEO,UAAmB;IAClD,IAAIP,WAAW,CAAChN,WAAW,IAAIgN,WAAW,CAAChN,WAAW,CAAC7C,MAAM,GAAG,CAAC,EAAE;MACjE,IAAIoQ,UAAU,KAAKpL,SAAS,EAAE;QAC5B6K,WAAW,CAACjN,iBAAiB,GAAGwN,UAAU;MAC5C;MACAP,WAAW,CAACpF,WAAW,GAAG,IAAI;MAC9B;MACAkD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAhE,eAAeA,CAAC+F,WAAgB;IAC9BA,WAAW,CAACpF,WAAW,GAAG,KAAK;IAC/B;IACAkD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAxE,cAAcA,CAACuG,WAAgB;IAC7B,IAAI,CAAClN,SAAS,CAACkN,WAAW,CAAC;EAC7B;EAEA1G,cAAcA,CAAC0G,WAAgB;IAC7B,IAAI,CAACtN,SAAS,CAACsN,WAAW,CAAC;EAC7B;EAEA;EACA7F,SAASA,CAACkE,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAACpF,WAAW,EAAE;MAC3B,QAAQyD,KAAK,CAACmC,GAAG;QACf,KAAK,WAAW;UACdnC,KAAK,CAACoC,cAAc,EAAE;UACtB,IAAI,CAACnH,cAAc,CAAC0G,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAACoC,cAAc,EAAE;UACtB,IAAI,CAAChH,cAAc,CAACuG,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAACoC,cAAc,EAAE;UACtB,IAAI,CAACxG,eAAe,CAAC+F,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAnI,qBAAqBA,CAAC6I,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACAlJ,0BAA0BA,CAACoJ,kBAA4B,EAAEhB,WAAgB;IACvE;IACAiB,MAAM,CAACC,IAAI,CAAClB,WAAW,CAACxD,aAAa,CAAC,CAAC2E,OAAO,CAACX,GAAG,IAAG;MACnDR,WAAW,CAACxD,aAAa,CAACgE,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAQ,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrCpB,WAAW,CAACxD,aAAa,CAAC4E,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACApB,WAAW,CAACqB,WAAW,GAAG,IAAI,CAAC7I,aAAa,CAACrI,MAAM,GAAG,CAAC,IACrD,IAAI,CAACqI,aAAa,CAAC8I,KAAK,CAAClD,IAAI,IAAI4B,WAAW,CAACxD,aAAa,CAAC4B,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAACmD,6BAA6B,CAACvB,WAAW,CAAC;EACjD;EAEA;EACAwB,qBAAqBA,CAACxB,WAAgB;IACpC,OAAOiB,MAAM,CAACC,IAAI,CAAClB,WAAW,CAACxD,aAAa,CAAC,CAACgD,MAAM,CAACgB,GAAG,IAAIR,WAAW,CAACxD,aAAa,CAACgE,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQe,6BAA6BA,CAACvB,WAAgB;IACpDA,WAAW,CAACzH,wBAAwB,GAAG,IAAI,CAACiJ,qBAAqB,CAACxB,WAAW,CAAC;EAChF;EAEA;EACQyB,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACrR,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC+Q,OAAO,CAACnB,WAAW,IAAG;QAC5C,IAAI,CAACuB,6BAA6B,CAACvB,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIApK,sBAAsBA,CAAC8L,OAAgB,EAAEtD,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAC3I,kBAAkB,CAACyI,IAAI,CAAC,GAAGsD,OAAO;EACjD;EAEAC,kBAAkBA,CAAC3L,kBAA4B,EAAE4L,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMlF,MAAM,IAAI3G,kBAAkB,EAAE;MACvC6L,YAAY,CAAClF,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMmF,WAAW,GAAGF,WAAW,CAAC1C,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIiC,WAAW,EAAE;MAC9B,IAAI9L,kBAAkB,CAAC+L,QAAQ,CAAClC,IAAI,CAAC,EAAE;QACrCgC,YAAY,CAAChC,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOgC,YAAY;EACrB;EAKAG,eAAeA,CAAA;IACb,IAAI,CAACzG,gBAAgB,CAAC0G,mCAAmC,CAAC;MACxDlE,IAAI,EAAE;QACJmE,YAAY,EAAE,IAAI,CAAC3E,WAAW;QAC9BrB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DiG,SAAS,EAAE;;KAEd,CAAC,CAACC,IAAI,CACLzT,GAAG,CAAC0T,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACtN,YAAY,GAAGoN,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAC7F,KAAK,GAAG4F,GAAG,CAACC,OAAO,CAACE,SAAS,GAAG,KAAK,GAAG,IAAI;QAEjD,IAAIH,GAAG,CAACC,OAAO,CAACE,SAAS,EAAE;UACzB,IAAI,CAAChK,aAAa,CAAC2I,OAAO,CAAC/C,IAAI,IAAI,IAAI,CAAC5B,aAAa,CAAC4B,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACpI,kBAAkB,CAACmL,OAAO,CAAC/C,IAAI,IAAI,IAAI,CAACzI,kBAAkB,CAACyI,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAChO,kBAAkB,GAAGiS,GAAG,CAACC,OAAO,CAACE,SAAS,CAAC3B,GAAG,CAAE4B,CAAM,IAAI;YAC7D,OAAO;cACLC,OAAO,EAAE,IAAI,CAACzN,YAAY,EAAEyN,OAAO;cACnCnN,cAAc,EAAEkN,CAAC,CAAClN,cAAc;cAChCvC,WAAW,EAAEyP,CAAC,CAACzP,WAAW,KAAKyP,CAAC,CAACE,gBAAgB,GAAG,CAACF,CAAC,CAACE,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EtD,KAAK,EAAEoD,CAAC,CAACpD,KAAK;cACduD,kBAAkB,EAAEH,CAAC,CAACG,kBAAkB;cACxCC,WAAW,EAAEJ,CAAC,CAACI,WAAW;cAC1B3J,SAAS,EAAEuJ,CAAC,CAACvJ,SAAS;cACtB4J,KAAK,EAAEL,CAAC,CAACK,KAAK;cACdC,KAAK,EAAEN,CAAC,CAACM,KAAK;cACdtM,YAAY,EAAEgM,CAAC,CAAChM,YAAY,GAAGgM,CAAC,CAAChM,YAAY,GAAG,GAAGgM,CAAC,CAACK,KAAK,IAAIL,CAAC,CAACM,KAAK,IAAIN,CAAC,CAACvJ,SAAS,EAAE;cACtF0I,WAAW,EAAEa,CAAC,CAACb,WAAW;cAC1BoB,YAAY,EAAEP,CAAC,CAACO,YAAY;cAC5BpM,cAAc,EAAE6L,CAAC,CAACQ,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGR,CAAC,CAAC7L,cAAc;cACtDqM,OAAO,EAAER,CAAC,CAACQ,OAAO;cAClBC,OAAO,EAAET,CAAC,CAACS,OAAO,IAAI,IAAI;cAAE;cAC5B/O,aAAa,EAAEsO,CAAC,CAACtO,aAAa,IAAI,EAAE;cAAE;cACtCqI,aAAa,EAAEiG,CAAC,CAACU,qBAAqB,CAAChT,MAAM,GAAG,IAAI,CAACiT,0BAA0B,CAAC,IAAI,CAAC5K,aAAa,EAAEiK,CAAC,CAACU,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC3G;cAAa,CAAE;cAAE7G,kBAAkB,EAAE8M,CAAC,CAACb,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3L,kBAAkB,EAAEyM,CAAC,CAACb,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACjM;cAAkB,CAAE;cAC9R0L,WAAW,EAAEoB,CAAC,CAACU,qBAAqB,CAAChT,MAAM,KAAK,IAAI,CAACqI,aAAa,CAACrI,MAAM;cACzEmF,YAAY,EAAE,EAAE;cAAEwB,eAAe,EAAE2L,CAAC,CAACQ,OAAO,GAAG,IAAI,CAAC/E,cAAc,CAACuE,CAAC,CAACQ,OAAO,EAAE,IAAI,CAAC7K,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HrF,iBAAiB,EAAE,CAAC;cACpB6H,WAAW,EAAE,KAAK;cAClB3B,WAAW,EAAE,IAAI;cAAE;cACnBV,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;UAEF;UACA,IAAI,CAAC8K,kBAAkB,EAAE;UAEzB;UACA,IAAI,CAACtH,GAAG,CAACuH,aAAa,EAAE;QAC1B;QACA;QACA,IAAI,CAAC7B,gCAAgC,EAAE;QAEvC;QACA,IAAI,CAAC1F,GAAG,CAACuH,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC,CACH,CAACpG,SAAS,CAAC;MACVqG,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;EACJ;EAEAvM,mBAAmBA,CAACgJ,WAAgB;IAClC,IAAIA,WAAW,CAAClJ,eAAe,IAAIkJ,WAAW,CAAClJ,eAAe,CAACmB,KAAK,KAAK,CAAC,EAAE;MAC1E+H,WAAW,CAACpJ,cAAc,GAAG,CAAC;IAChC;EACF;EACA4M,4BAA4BA,CAAC1O,IAAW;IACtC,KAAK,IAAIsJ,IAAI,IAAItJ,IAAI,EAAE;MACrB,IAAIsJ,IAAI,CAACjC,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOiC,IAAI,CAACqF,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAMAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAO1C,MAAM,CAACC,IAAI,CAACyC,GAAG,CAAC,CAACnE,MAAM,CAACgB,GAAG,IAAImD,GAAG,CAACnD,GAAG,CAAC,CAAC;EACjD;EAEAoD,0BAA0BA,CAACD,GAA4B;IACrD,OAAO1C,MAAM,CAACC,IAAI,CAACyC,GAAG,CAAC,CACpBnE,MAAM,CAACgB,GAAG,IAAImD,GAAG,CAACnD,GAAG,CAAC,CAAC,CACvBqD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAChN,eAAoB,EAAEnB,kBAAuB;IAC1D,IAAImB,eAAe,IAAIA,eAAe,CAACmB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC2L,0BAA0B,CAACjO,kBAAkB,CAAC;IAC5D;EACF;EAEAoO,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAC9E,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI+E,KAAK,CAAC9T,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO8T,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC5O,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACnF,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLgU,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACzO,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpEsP,aAAa,EAAE9O,YAAY,CAAC,CAAC,CAAC,CAAC6J,SAAS,IAAI,IAAI;QAChDkF,QAAQ,EAAE/O,YAAY,CAAC,CAAC,CAAC,CAAC+J,KAAK,CAACtK,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAmP,UAAUA,CAAA;IACR,IAAI,CAAC5I,KAAK,CAAC6I,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMtG,IAAI,IAAI,IAAI,CAACuG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACpG,IAAI,CAAC6E,OAAQ,EAAE;QACzCuB,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACrG,IAAI,CAACxH,cAAe,EAAE;QACvD6N,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIrG,IAAI,CAAC4E,YAAY,IAAI5E,IAAI,CAACxH,cAAc,EAAE;QAC5C,IAAIwH,IAAI,CAACxH,cAAc,GAAGwH,IAAI,CAAC4E,YAAY,IAAI5E,IAAI,CAACxH,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC8E,KAAK,CAACkJ,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGxG,IAAI,CAAC4E,YAAY,GAAG,KAAK5E,IAAI,CAAC3H,YAAY,IAAI,CAAC;QAChG;MACF;MAEA,IAAI,CAACiO,kBAAkB,IAAK,CAACtG,IAAI,CAAC3H,YAAa,EAAE;QAC/CiO,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC9I,KAAK,CAACkJ,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC/I,KAAK,CAACkJ,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAChJ,KAAK,CAACkJ,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC7J,YAAY,GAAG,IAAI;IAExB,IAAI,CAAC2J,mBAAmB,GAAG,IAAI,CAACvU,kBAAkB,CAACyQ,GAAG,CAAC,CAACiE,CAAM,EAAElT,KAAa,KAAI;MAC/E,MAAMwM,IAAI,GAAG;QACX7I,cAAc,EAAEuP,CAAC,CAACvP,cAAc,GAAGuP,CAAC,CAACvP,cAAc,GAAG,IAAI;QAC1D8J,KAAK,EAAEyF,CAAC,CAACxP,YAAY,GAAG,IAAI,CAAC4O,UAAU,CAACY,CAAC,CAACxP,YAAY,CAAC,GAAGH,SAAS;QACnEyN,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACoB,CAAC,CAACtI,aAAa,CAAC;QAC9DqG,WAAW,EAAEiC,CAAC,CAACjC,WAAW,IAAI,CAAC;QAC/B3J,SAAS,EAAE4L,CAAC,CAAC5L,SAAS;QACtBzC,YAAY,EAAEqO,CAAC,CAACrO,YAAY;QAC5BmL,WAAW,EAAEkD,CAAC,CAAChO,eAAe,CAACmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC6L,cAAc,CAACgB,CAAC,CAAChO,eAAe,EAAEgO,CAAC,CAACnP,kBAAkB,CAAC,IAAI,IAAI;QACxHqN,YAAY,EAAE8B,CAAC,CAAC9B,YAAY;QAC5BpM,cAAc,EAAEkO,CAAC,CAAClO,cAAc;QAChCqM,OAAO,EAAE6B,CAAC,CAAChO,eAAe,CAACmB,KAAK;QAChCiL,OAAO,EAAE4B,CAAC,CAAC5B,OAAO,IAAI,IAAI;QAAE;QAC5B/O,aAAa,EAAE2Q,CAAC,CAAC3Q,aAAa,IAAI,IAAI,CAAE;OACzC;MAGD,OAAOiK,IAAI;IACb,CAAC,CAAC;IAGF,IAAI,CAACkG,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5I,KAAK,CAACqJ,aAAa,CAAC5U,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACmL,OAAO,CAAC0J,aAAa,CAAC,IAAI,CAACtJ,KAAK,CAACqJ,aAAa,CAAC;MACpD;MACA,IAAI,CAACE,sBAAsB,EAAE;MAC7B,IAAI,CAACjK,YAAY,GAAG,KAAK;MACzB;IACF;IACA,IAAI,IAAI,CAACyB,KAAK,EAAE;MACd,IAAI,CAACyI,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,oBAAoB,EAAE;IAC7B;EACF;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACC,gBAAgB,GAAG;MACtBlD,YAAY,EAAE,IAAI,CAAC3E,WAAW;MAC9B8H,OAAO,EAAE,IAAI,CAACpQ,YAAY,EAAEyN,OAAO;MACnC4C,SAAS,EAAE,IAAI,CAACX,mBAAmB;MACnCzI,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACX,gBAAgB,CAACgK,oCAAoC,CAAC;MACzDxH,IAAI,EAAE,IAAI,CAACqH;KACZ,CAAC,CAAClI,SAAS,CAAC;MACXsI,IAAI,EAAGnD,GAAG,IAAI;QACZ,IAAI,CAACrH,YAAY,GAAG,KAAK;QACzB,IAAIqH,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACjH,OAAO,CAACmK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAAC/H,MAAM,EAAE;QACf;MACF,CAAC;MACD6F,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvI,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEAkK,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACE,gBAAgB,GAAG;MACtBlD,YAAY,EAAE,IAAI,CAAC3E,WAAW;MAC9B8H,OAAO,EAAE,IAAI,CAACpQ,YAAY,EAAEyN,OAAO;MACnC4C,SAAS,EAAE,IAAI,CAACX,mBAAmB;MACnCzI,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACX,gBAAgB,CAACgK,oCAAoC,CAAC;MACzDxH,IAAI,EAAE,IAAI,CAACqH;KACZ,CAAC,CAAClI,SAAS,CAAC;MACXsI,IAAI,EAAGnD,GAAG,IAAI;QACZ,IAAI,CAACrH,YAAY,GAAG,KAAK;QACzB,IAAIqH,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACjH,OAAO,CAACmK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAAC/H,MAAM,EAAE;QACf;MACF,CAAC;MACD6F,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvI,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEAoI,0BAA0BA,CAACsC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMxH,IAAI,IAAIsH,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAAC/I,IAAI,CAACkJ,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK3H,IAAI,IAAI0H,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAACxH,IAAI,CAAC,GAAG,CAAC,CAACyH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGA7K,aAAaA,CAAA;IACX;IACA,IAAI,CAACa,gBAAgB,CAACqK,mCAAmC,CAAC;MACxDlI,IAAI,EAAE;QACJmE,YAAY,EAAE,IAAI,CAAC3E,WAAW;QAC9B2I,KAAK,EAAE;;KAEV,CAAC,CAAC9D,IAAI,CACLzT,GAAG,CAAC0T,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAM4D,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3C/D,GAAG,CAACC,OAAO,CAACnB,OAAO,CAAEkF,QAAa,IAAI;UACpC,MAAM7F,GAAG,GAAG,GAAG6F,QAAQ,CAACnN,SAAS,IAAImN,QAAQ,CAACvD,KAAK,IAAIuD,QAAQ,CAACtD,KAAK,EAAE;UACvEoD,iBAAiB,CAACG,GAAG,CAAC9F,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF,IAAI,IAAI,CAACpQ,kBAAkB,CAACD,MAAM,KAAK,CAAC,EAAE;UACxC,IAAI,CAACmL,OAAO,CAACmC,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAACkH,mBAAmB,GAAG,IAAI,CAACvU,kBAAkB,CAACyQ,GAAG,CAAC,CAACiE,CAAM,EAAElT,KAAa,KAAI;UAC/E,MAAMwM,IAAI,GAAG;YACX7I,cAAc,EAAEuP,CAAC,CAACvP,cAAc,GAAGuP,CAAC,CAACvP,cAAc,GAAG,IAAI;YAC1D8J,KAAK,EAAEyF,CAAC,CAACxP,YAAY,GAAG,IAAI,CAAC4O,UAAU,CAACY,CAAC,CAACxP,YAAY,CAAC,GAAGH,SAAS;YACnEyN,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACoB,CAAC,CAACtI,aAAa,CAAC;YAC9DqG,WAAW,EAAE,CAAC;YAAE;YAChB3J,SAAS,EAAE4L,CAAC,CAAC5L,SAAS;YACtBzC,YAAY,EAAEqO,CAAC,CAACrO,YAAY;YAC5BmL,WAAW,EAAEkD,CAAC,CAAChO,eAAe,CAACmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC6L,cAAc,CAACgB,CAAC,CAAChO,eAAe,EAAEgO,CAAC,CAACnP,kBAAkB,CAAC,IAAI,IAAI;YACxHqN,YAAY,EAAE8B,CAAC,CAAC9B,YAAY;YAC5BpM,cAAc,EAAEkO,CAAC,CAAClO,cAAc;YAChCqM,OAAO,EAAE6B,CAAC,CAAChO,eAAe,CAACmB,KAAK;YAChCiL,OAAO,EAAE4B,CAAC,CAAC5B,OAAO,IAAI,IAAI;YAAE;YAC5B/O,aAAa,EAAE2Q,CAAC,CAAC3Q,aAAa,IAAI,IAAI,CAAE;WACzC;UAGD,OAAOiK,IAAI;QACb,CAAC,CAAC;QAGF;QACA,IAAI,CAACkG,UAAU,EAAE;QACjB,IAAI,IAAI,CAAC5I,KAAK,CAACqJ,aAAa,CAAC5U,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAACmL,OAAO,CAAC0J,aAAa,CAAC,IAAI,CAACtJ,KAAK,CAACqJ,aAAa,CAAC;UACpD;QACF;QAEA;QACA,MAAMwB,YAAY,GAAqB;UACrCrE,YAAY,EAAE,IAAI,CAAC3E,WAAW;UAC9B8H,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,IAAI,CAACX,mBAAmB;UACnCzI,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;SACnD;QAED,IAAI,CAACX,gBAAgB,CAACgK,oCAAoC,CAAC;UACzDxH,IAAI,EAAEwI;SACP,CAAC,CAACrJ,SAAS,CAACsJ,SAAS,IAAG;UACvB,IAAIA,SAAS,CAACjE,UAAU,IAAI,CAAC,IAAIiE,SAAS,CAAClE,OAAO,EAAE;YAClD,MAAMmE,SAAS,GAAGD,SAAS,CAAClE,OAAO,CAAC+C,OAAO;YAC3C,IAAI,CAAC/J,OAAO,CAACmK,aAAa,CAAC,cAAc,IAAI,CAACrV,kBAAkB,CAACD,MAAM,QAAQ,CAAC;YAEhF;YACA,MAAMuW,kBAAkB,GAAG,IAAI,CAACtL,KAAK,CAACuL,QAAQ,CAAChJ,WAAW;YAC1D,IAAI,CAACtC,MAAM,CAACuL,QAAQ,CAAC,CAAC,yCAAyC,EAAE,IAAI,CAACrJ,WAAW,EAAEkJ,SAAS,CAAC,EAAE;cAC7F9I,WAAW,EAAE+I;aACd,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpL,OAAO,CAACmC,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACP,SAAS,EAAE;EACf;EAEA;EACQ2J,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACtJ,WAAW,EAAE;IAEvB,IAAI,CAACzB,aAAa,CAACgL,4BAA4B,CAAC;MAAEvJ,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC,CAACL,SAAS,CAAC;MAC3FsI,IAAI,EAAGuB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACzE,OAAO,EAAE;UACpB,IAAI,CAACjK,YAAY,GAAG,IAAI,CAAC2O,gCAAgC,CAACD,QAAQ,CAACzE,OAAO,CAAC;QAC7E;MACF,CAAC;MACDiB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,IAAI,CAAC/K,aAAa,IAAI,IAAI,CAACA,aAAa,CAACrI,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAACkI,YAAY,GAAG,IAAI,CAAC4O,kCAAkC,CAAC,IAAI,CAACzO,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQwO,gCAAgCA,CAACE,OAAY;IACnD,MAAM7O,YAAY,GAAQ,EAAE;IAE5B4I,MAAM,CAACiG,OAAO,CAACA,OAAO,CAAC,CAAC/F,OAAO,CAAC,CAAC,CAACgG,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpE/O,YAAY,CAAC8O,QAAQ,CAAC,GAAGC,MAAM,CAACvG,GAAG,CAAEwG,KAAU,KAAM;QACnDtG,IAAI,EAAEsG,KAAK,CAACC,SAAS;QACrBH,QAAQ,EAAEE,KAAK,CAACE,QAAQ;QACxBC,KAAK,EAAEH,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACC,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOzP,YAAY;EACrB;EAEA;EACA4O,kCAAkCA,CAACzO,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACrI,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAMkI,YAAY,GAAQ,EAAE;IAE5BG,aAAa,CAAC2I,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAM2G,aAAa,GAAG3G,SAAS,CAAC4G,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMb,QAAQ,GAAGY,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAAC1P,YAAY,CAAC8O,QAAQ,CAAC,EAAE;QAC3B9O,YAAY,CAAC8O,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMc,WAAW,GAAGC,QAAQ,CAAC9G,SAAS,CAAC+G,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMX,KAAK,GAAGtH,IAAI,CAACkI,IAAI,CAACH,WAAW,GAAG,CAAC,CAAC;MAExC5P,YAAY,CAAC8O,QAAQ,CAAC,CAAC7H,IAAI,CAAC;QAC1ByB,IAAI,EAAEK,SAAS;QACf+F,QAAQ,EAAEA,QAAQ;QAClBK,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAOzP,YAAY;EACzB;EAIAmF,iCAAiCA,CAAA;IAC/B,IAAI,CAAChC,yBAAyB,CAAC6M,8DAA8D,CAAC;MAC5FtK,IAAI,EAAE,IAAI,CAACR;KACZ,CAAC,CAAC6E,IAAI,CACLzT,GAAG,CAAC0T,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC/J,aAAa,GAAG,IAAI,CAACgL,4BAA4B,CAACnB,GAAG,CAACC,OAAO,CAAC;QAEnE;QACA,IAAI,CAACuE,uBAAuB,EAAE;QAE9B,IAAI,CAAC7E,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC9E,SAAS,CAAC;MACVqG,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;EACJ;EACA7F,MAAMA,CAAA;IACJ,IAAI,CAAC7B,aAAa,CAACyD,IAAI,CAAC;MACtBgJ,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChL;KACf,CAAC;IACF,IAAI,CAAC5B,QAAQ,CAAC6M,IAAI,EAAE;EACtB;EAEA;EAEA;;;EAGAC,eAAeA,CAACzI,WAAwC;IACtD;IACA,MAAM0I,WAAW,GAAG,CAAC,CAAC1I,WAAW,CAACvJ,YAAY,IAAIuJ,WAAW,CAACvJ,YAAY,CAACkS,IAAI,EAAE,KAAK,EAAE;IACxF,MAAMC,SAAS,GAAG,CAAC,CAAC5I,WAAW,CAAClJ,eAAe,IAAIkJ,WAAW,CAAClJ,eAAe,CAACmB,KAAK;IACpF,MAAM4Q,gBAAgB,GAAG,CAAC,CAAC7I,WAAW,CAACpJ,cAAc,IAAIoJ,WAAW,CAACpJ,cAAc,GAAG,CAAC;IAEvF;IACA,IAAIkS,aAAa,GAAG,IAAI;IACxB,IAAI9I,WAAW,CAAClJ,eAAe,EAAEmB,KAAK,KAAK,CAAC,EAAE;MAC5C6Q,aAAa,GAAG,CAAC,CAAC9I,WAAW,CAACrK,kBAAkB,IAC9CsL,MAAM,CAAC8H,MAAM,CAAC/I,WAAW,CAACrK,kBAAkB,CAAC,CAACoH,IAAI,CAACiM,QAAQ,IAAIA,QAAQ,CAAC;IAC5E;IAEA,OAAON,WAAW,IAAIE,SAAS,IAAIC,gBAAgB,IAAIC,aAAa;EACtE;EAEA;;;EAGAG,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC7Y,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,OAAO,IAAI,CAACC,kBAAkB,CAACoP,MAAM,CAACpB,IAAI,IAAI,IAAI,CAACqK,eAAe,CAACrK,IAAI,CAAC,CAAC,CAACjO,MAAM;EAClF;EAEA;;;EAGA+Y,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9Y,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,MAAMgZ,SAAS,GAAG,IAAI,CAACF,sBAAsB,EAAE;IAC/C,OAAO/I,IAAI,CAACkJ,KAAK,CAAED,SAAS,GAAG,IAAI,CAAC/Y,kBAAkB,CAACD,MAAM,GAAI,GAAG,CAAC;EACvE;EAEA;;;EAGA6B,YAAYA,CAACJ,KAAa;IACxB,MAAMyX,OAAO,GAAGvL,QAAQ,CAACwL,cAAc,CAAC,aAAa1X,KAAK,EAAE,CAAC;IAC7D,IAAIyX,OAAO,EAAE;MACXA,OAAO,CAACE,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT,CAAC;MAEF;MACAL,OAAO,CAACM,SAAS,CAACrD,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACnEsD,UAAU,CAAC,MAAK;QACdP,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACxE,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEA;;;EAGAC,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAC1Z,kBAAkB,EAAE;IAE9B,MAAM2Z,oBAAoB,GAAG,IAAI,CAAC3Z,kBAAkB,CAAC4Z,SAAS,CAAC5L,IAAI,IAAI,CAAC,IAAI,CAACqK,eAAe,CAACrK,IAAI,CAAC,CAAC;IACnG,IAAI2L,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAC/B,IAAI,CAAC/X,YAAY,CAAC+X,oBAAoB,CAAC;IACzC;EACF;EAEA;;;EAGA9E,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC7U,kBAAkB,EAAE;IAE9B;IACA,MAAM6Z,eAAe,GAAG,IAAI,CAAC7Z,kBAAkB,CAAC4Z,SAAS,CAAC5L,IAAI,IAAI,CAAC,IAAI,CAACqK,eAAe,CAACrK,IAAI,CAAC,CAAC;IAC9F,IAAI6L,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACjY,YAAY,CAACiY,eAAe,CAAC;IACpC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACTC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAEA;;;EAGAC,OAAOA,CAAA;IAEL;IACA,MAAMC,aAAa,GAAGxM,QAAQ,CAACyM,aAAa,CAAC,gBAAgB,CAAC,IAC5DzM,QAAQ,CAACyM,aAAa,CAAC,cAAc,CAAC,IACtCzM,QAAQ,CAACyM,aAAa,CAAC,SAAS,CAAC,IACjCzM,QAAQ,CAACyM,aAAa,CAAC,SAAS,CAAC,IACjCzM,QAAQ,CAACyM,aAAa,CAAC,YAAY,CAAC,IACpCzM,QAAQ,CAACC,IAAI,CAACyM,iBAAiB;IAEjC,IAAIF,aAAa,EAAE;MAChBA,aAA6B,CAACf,cAAc,CAAC;QAC5CC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MAEF;MACAG,UAAU,CAAC,MAAK;QACdO,MAAM,CAACM,QAAQ,CAAC;UACdC,GAAG,EAAE,CAAC,EAAE;UAAE;UACVlB,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MACP;IACF;IAEA;IACA,IAAI,IAAI,CAACpZ,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAE;MACjE,MAAMwa,YAAY,GAAG7M,QAAQ,CAACwL,cAAc,CAAC,aAAa,CAAC;MAC3D,IAAIqB,YAAY,EAAE;QAChBA,YAAY,CAACpB,cAAc,CAAC;UAC1BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QAEF;QACAG,UAAU,CAAC,MAAK;UACdO,MAAM,CAACM,QAAQ,CAAC;YACdC,GAAG,EAAE,CAAC,GAAG;YAAE;YACXlB,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QACP;MACF;IACF;IAEA;IACAW,MAAM,CAACC,QAAQ,CAAC;MAAEM,GAAG,EAAE,CAAC;MAAElB,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEA;;;EAGAoB,cAAcA,CAAA;IAEZ;IACA,MAAMC,MAAM,GAAG/M,QAAQ,CAACyM,aAAa,CAAC,qBAAqB,CAAgB;IAC3E,IAAIM,MAAM,EAAE;MACVA,MAAM,CAAC7M,KAAK,CAAC8M,SAAS,GAAG,aAAa;MACtClB,UAAU,CAAC,MAAK;QACdiB,MAAM,CAAC7M,KAAK,CAAC8M,SAAS,GAAG,EAAE;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACAlB,UAAU,CAAC,MAAK;MACd;MACA,MAAMmB,aAAa,GAAGjN,QAAQ,CAACyM,aAAa,CAAC,gBAAgB,CAAgB;MAC7E,IAAIQ,aAAa,EAAE;QACjBA,aAAa,CAACxB,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UAAE;UACjBC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MAEA;MACA,MAAMsB,cAAc,GAAGlN,QAAQ,CAACyM,aAAa,CAAC,kDAAkD,CAAgB;MAChH,IAAIS,cAAc,EAAE;QAClBA,cAAc,CAACzB,cAAc,CAAC;UAC5BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MAEA;MACA,IAAI,IAAI,CAACxZ,0BAA0B,IAAI,IAAI,CAACA,0BAA0B,CAACC,MAAM,GAAG,CAAC,EAAE;QACjF,MAAM8a,SAAS,GAAG,IAAI,CAAC/a,0BAA0B,CAACC,MAAM,GAAG,CAAC;QAC5D,MAAM+a,WAAW,GAAGpN,QAAQ,CAACwL,cAAc,CAAC,aAAa2B,SAAS,EAAE,CAAC;QACrE,IAAIC,WAAW,EAAE;UACfA,WAAW,CAAC3B,cAAc,CAAC;YACzBC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE;WACT,CAAC;UAEF;UACAE,UAAU,CAAC,MAAK;YACdO,MAAM,CAACM,QAAQ,CAAC;cACdC,GAAG,EAAE,GAAG;cAAE;cACVlB,QAAQ,EAAE;aACX,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;UAEP;QACF;MACF;MAEA;MACAW,MAAM,CAACC,QAAQ,CAAC;QACdM,GAAG,EAAE5M,QAAQ,CAACC,IAAI,CAACoN,YAAY;QAC/B3B,QAAQ,EAAE;OACX,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGA5Q,kBAAkBA,CAACoH,WAAwC;IACzDA,WAAW,CAAC/G,WAAW,GAAG,CAAC+G,WAAW,CAAC/G,WAAW;EACpD;EAEA;;;EAGA7H,SAASA,CAAA;IACP,IAAI,IAAI,CAAChB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC+Q,OAAO,CAAC/C,IAAI,IAAG;QACrCA,IAAI,CAACnF,WAAW,GAAG,KAAK;MAC1B,CAAC,CAAC;MACF,IAAI,CAAC8C,GAAG,CAACuH,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGAhS,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC+Q,OAAO,CAAC/C,IAAI,IAAG;QACrCA,IAAI,CAACnF,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC;MACF,IAAI,CAAC8C,GAAG,CAACuH,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGA8H,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAChb,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC+Q,OAAO,CAAC/C,IAAI,IAAG;QACrCA,IAAI,CAACnF,WAAW,GAAG,IAAI,CAACwP,eAAe,CAACrK,IAAI,CAAC;MAC/C,CAAC,CAAC;MACF,IAAI,CAACrC,GAAG,CAACuH,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGA1S,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACF,WAAW,CAACiY,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACzY,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAChE,CAAC,MAAM;MACL,MAAMib,KAAK,GAAG,IAAI,CAAC3a,WAAW,CAAC4a,WAAW,EAAE,CAAC3C,IAAI,EAAE;MACnD,IAAI,CAACzY,0BAA0B,GAAG,IAAI,CAACE,kBAAkB,CAACoP,MAAM,CAACpB,IAAI,IAAG;QACtE,OACEA,IAAI,CAAClF,SAAS,EAAEoS,WAAW,EAAE,CAACvJ,QAAQ,CAACsJ,KAAK,CAAC,IAC7CjN,IAAI,CAAC3H,YAAY,EAAE6U,WAAW,EAAE,CAACvJ,QAAQ,CAACsJ,KAAK,CAAC;MAEpD,CAAC,CAAC;IACJ;IACA,IAAI,CAACtP,GAAG,CAACuH,aAAa,EAAE;EAC1B;EAEA;;;EAGA1T,WAAWA,CAAA;IACT,IAAI,CAACc,WAAW,GAAG,EAAE;IACrB,IAAI,CAACR,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAC9D,IAAI,CAAC2L,GAAG,CAACuH,aAAa,EAAE;EAC1B;EAEA;;;EAGQD,kBAAkBA,CAAA;IACxB,IAAI,CAACnT,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAC9D,IAAI,IAAI,CAACM,WAAW,CAACiY,IAAI,EAAE,EAAE;MAC3B,IAAI,CAAC/X,QAAQ,EAAE;IACjB;EACF;EAEA;;;EAGAiB,cAAcA,CAACD,KAAa;IAC1B;IACA,IAAI,IAAI,CAACqD,YAAY,EAAEC,OAAO,IAAI,KAAK,EAAE;MACvC,IAAI,CAACoG,OAAO,CAACmC,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA,IAAI7L,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACxB,kBAAkB,CAACD,MAAM,EAAE;MACxD,IAAI,CAACmL,OAAO,CAACmC,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA,MAAMW,IAAI,GAAG,IAAI,CAAChO,kBAAkB,CAACwB,KAAK,CAAC;IAE3C;IACA,MAAM2Z,cAAc,GAAG,CAAC,CAACnN,IAAI,CAACyE,WAAW,IAAIzE,IAAI,CAACyE,WAAW,GAAG,CAAC;IACjE,MAAM2I,cAAc,GAAGD,cAAc,GACjC,aAAanN,IAAI,CAAC3H,YAAY,IAAI2H,IAAI,CAAClF,SAAS,kBAAkB,GAClE,WAAWkF,IAAI,CAAC3H,YAAY,IAAI2H,IAAI,CAAClF,SAAS,KAAK;IAEvD;IACA,IAAIuS,OAAO,CAACD,cAAc,CAAC,EAAE;MAC3B;MACA,IAAID,cAAc,EAAE;QAClB,IAAI,CAAChQ,gBAAgB,CAACmQ,sCAAsC,CAAC;UAAE3N,IAAI,EAAEK,IAAI,CAACyE;QAAW,CAAE,CAAC,CAAC3F,SAAS,CAAC;UACjGsI,IAAI,EAAGuB,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACxE,UAAU,IAAI,CAAC,EAAE;cAC5BlC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyG,QAAQ,CAAC4E,OAAO,CAAC;cAC1C;cACA,IAAI,CAACC,gBAAgB,CAACha,KAAK,EAAEwM,IAAI,EAAEmN,cAAc,CAAC;YACpD,CAAC,MAAM;cACL,IAAI,CAACjQ,OAAO,CAACmC,YAAY,CAAC,SAASsJ,QAAQ,CAAC4E,OAAO,EAAE,CAAC;YACxD;UACF,CAAC;UACDpI,KAAK,EAAGA,KAAK,IAAI;YACflD,OAAO,CAACkD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;YACpC,IAAI,CAACjI,OAAO,CAACmC,YAAY,CAAC,aAAa,CAAC;UAC1C;SACD,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI,CAACmO,gBAAgB,CAACha,KAAK,EAAEwM,IAAI,EAAEmN,cAAc,CAAC;MACpD;IACF;EACF;EAEA;;;EAGQK,gBAAgBA,CAACha,KAAa,EAAEwM,IAAiC,EAAEmN,cAAuB;IAChG;IACA,IAAI,CAACnb,kBAAkB,CAACyb,MAAM,CAACja,KAAK,EAAE,CAAC,CAAC;IAExC;IACA,IAAI,CAACyR,kBAAkB,EAAE;IAEzB;IACA,MAAMyI,cAAc,GAAGP,cAAc,GACjC,SAASnN,IAAI,CAAC3H,YAAY,IAAI2H,IAAI,CAAClF,SAAS,GAAG,GAC/C,SAASkF,IAAI,CAAC3H,YAAY,IAAI2H,IAAI,CAAClF,SAAS,GAAG;IACnD,IAAI,CAACoC,OAAO,CAACmK,aAAa,CAACqG,cAAc,CAAC;IAE1C;IACA,IAAI,CAAC/P,GAAG,CAACuH,aAAa,EAAE;EAC1B;EAEA;;;EAGAyI,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACxO,WAAW,EAAE;MACrB,IAAI,CAACjC,OAAO,CAACmC,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IAEA,IAAI,CAACzB,uBAAuB,CAACgQ,IAAI,CAAC;MAChCzO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B0O,KAAK,EAAE;KACR,CAAC,CAAC/O,SAAS,CAAC6B,MAAM,IAAG;MACpB,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACmN,SAAS,EAAE;QAC/B,IAAI,CAACC,0BAA0B,CAACpN,MAAM,CAACqN,cAAc,EAAErN,MAAM,CAACsN,iBAAiB,CAAC;MAClF;IACF,CAAC,CAAC;EACJ;EAEA;;;EAGQC,qBAAqBA,CAACC,SAA+B;IAC3D,MAAMC,SAAS,GAAa,EAAE;IAE9BnM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEiM,SAAS,CAACpc,MAAM,CAAC;IAEhEoc,SAAS,CAACpL,OAAO,CAAC,CAACkF,QAAQ,EAAEoG,aAAa,KAAI;MAC5CpM,OAAO,CAACC,GAAG,CAAC,MAAMmM,aAAa,GAAG,EAAEpG,QAAQ,CAAC;MAE7C;MACA,IAAIA,QAAQ,CAACqG,cAAc,IAAI/L,KAAK,CAACC,OAAO,CAACyF,QAAQ,CAACqG,cAAc,CAAC,EAAE;QACrErM,OAAO,CAACC,GAAG,CAAC,MAAMmM,aAAa,MAAMpG,QAAQ,CAACqG,cAAc,CAACvc,MAAM,MAAM,CAAC;QAE1EkW,QAAQ,CAACqG,cAAc,CAACvL,OAAO,CAAC,CAACwL,OAAY,EAAEC,YAAY,KAAI;UAC7DvM,OAAO,CAACC,GAAG,CAAC,MAAMsM,YAAY,GAAG,EAAED,OAAO,CAAC;UAE3C;UACA,IAAIA,OAAO,CAACtN,KAAK,EAAE;YACjBmN,SAAS,CAAClN,IAAI,CAACqN,OAAO,CAACtN,KAAK,CAAC;YAC7BgB,OAAO,CAACC,GAAG,CAAC,aAAaqM,OAAO,CAACtN,KAAK,EAAE,CAAC;UAC3C,CAAC,MAAM,IAAIsN,OAAO,CAACE,KAAK,EAAE;YACxB;YACAxM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqM,OAAO,CAACE,KAAK,CAAC;UAClD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLxM,OAAO,CAACC,GAAG,CAAC,MAAMmM,aAAa,SAAS,CAAC;MAC3C;IACF,CAAC,CAAC;IAEFpM,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkM,SAAS,CAAC;IAChE,OAAOA,SAAS,CAACrc,MAAM,GAAG,CAAC,GAAGqc,SAAS,GAAG,IAAI;EAChD;EAEA;;;EAGQL,0BAA0BA,CAACW,MAAyB,EAAEP,SAA+B;IAC3F;IACA,MAAMQ,0BAA0B,GAAG,IAAI,CAACC,6BAA6B,EAAE;IAEvE,IAAID,0BAA0B,CAAC5c,MAAM,GAAG,CAAC,EAAE;MACzC,MAAM8c,cAAc,GAAGF,0BAA0B,CAAClJ,IAAI,CAAC,GAAG,CAAC;MAC3D,MAAM2H,cAAc,GAAG,sBAAsByB,cAAc,2BAA2B;MAEtF,IAAIxB,OAAO,CAACD,cAAc,CAAC,EAAE;QAC3B,IAAI,CAAC0B,eAAe,CAACJ,MAAM,EAAEP,SAAS,EAAEQ,0BAA0B,CAAC;MACrE,CAAC,MAAM;QACL,IAAI,CAACG,eAAe,CAACJ,MAAM,EAAEP,SAAS,CAAC;MACzC;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACW,eAAe,CAACJ,MAAM,EAAEP,SAAS,CAAC;IACzC;EACF;EAEA;;;EAGQS,6BAA6BA,CAAA;IACnC,MAAMG,qBAAqB,GAAG,IAAI/G,GAAG,EAAU;IAE/C,IAAI,IAAI,CAAChW,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI,CAACC,kBAAkB,CAAC+Q,OAAO,CAAC/C,IAAI,IAAG;QACrC,IAAIA,IAAI,CAAC7F,wBAAwB,IAAI6F,IAAI,CAAC7F,wBAAwB,CAACpI,MAAM,GAAG,CAAC,EAAE;UAC7EiO,IAAI,CAAC7F,wBAAwB,CAAC4I,OAAO,CAACC,SAAS,IAAG;YAChD+L,qBAAqB,CAAC7G,GAAG,CAAClF,SAAS,CAAC;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,OAAOT,KAAK,CAACyM,IAAI,CAACD,qBAAqB,CAAC;EAC1C;EAEA;;;;;;EAMQD,eAAeA,CAACJ,MAAyB,EAAEP,SAA+B,EAAEc,sBAAiC;IAEnH,MAAMC,QAAQ,GAAkC,EAAE;IAClDjN,OAAO,CAACC,GAAG,CAACiM,SAAS,CAAC;IAEtB;IACA,MAAMgB,kBAAkB,GAAGhB,SAAS,CAAC1L,GAAG,CAACwF,QAAQ,KAAK;MACpDxD,WAAW,EAAE1N,SAAS;MACtBqY,aAAa,EAAErY,SAAS;MACxBsY,WAAW,EAAEpH,QAAQ,CAACqH,GAAG,IAAIvY,SAAS;MACtClB,WAAW,EAAEoS,QAAQ,CAACpS,WAAW,IAAI;KACtC,CAAC,CAAC;IAEH;IACA6Y,MAAM,CAAC3L,OAAO,CAACwM,KAAK,IAAG;MACrB;MACA,MAAMnR,aAAa,GAA+B,EAAE;MACpD,IAAIjE,wBAAwB,GAAa,EAAE;MAE3C;MACA,IAAI8U,sBAAsB,IAAIA,sBAAsB,CAACld,MAAM,GAAG,CAAC,EAAE;QAC/Dkd,sBAAsB,CAAClM,OAAO,CAACC,SAAS,IAAG;UACzC5E,aAAa,CAAC4E,SAAS,CAAC,GAAG,IAAI;QACjC,CAAC,CAAC;QACF7I,wBAAwB,GAAG,CAAC,GAAG8U,sBAAsB,CAAC;MACxD;MAEA,MAAMO,OAAO,GAAgC;QAC3C;QACA1U,SAAS,EAAEyU,KAAK,CAACzU,SAAS,IAAI,EAAE;QAChCzC,YAAY,EAAEkX,KAAK,CAACzU,SAAS;QAAE;QAC/BtC,cAAc,EAAE,CAAC;QACjBqM,OAAO,EAAE,CAAC;QAAE;QACZrB,WAAW,EAAE,EAAE;QACfsB,OAAO,EAAEyK,KAAK,CAACE,QAAQ;QACvB7K,YAAY,EAAEuK,kBAAkB,CAACpd,MAAM;QACvC0S,WAAW,EAAE,CAAC;QACdtN,cAAc,EAAE,IAAI;QACpB8J,KAAK,EAAElK,SAAS;QAChByN,kBAAkB,EAAE,EAAE;QACtBzO,aAAa,EAAEoZ,kBAAkB;QAEjC;QACAva,WAAW,EAAE,IAAI,CAACsZ,qBAAqB,CAACC,SAAS,CAAC;QAClDlH,OAAO,EAAE,IAAI,CAACpQ,YAAY,EAAEyN,OAAO,IAAI,CAAC;QAExC;QACA5L,eAAe,EAAE,IAAI,CAACsB,cAAc,CAAC,CAAC,CAAC;QAAE;QACzCoE,aAAa,EAAEA,aAAa;QAC5B7G,kBAAkB,EAAE,EAAE;QACtB0L,WAAW,EAAEgM,sBAAsB,GAAG,IAAI,CAAC7U,aAAa,CAACrI,MAAM,GAAG,CAAC,IACjE,IAAI,CAACqI,aAAa,CAAC8I,KAAK,CAAClD,IAAI,IAAI5B,aAAa,CAAC4B,IAAI,CAAC,CAAC,GAAG,KAAK;QAC/D9I,YAAY,EAAE,EAAE;QAChBvC,iBAAiB,EAAE,CAAC;QACpB6H,WAAW,EAAE,KAAK;QAClBrC,wBAAwB,EAAEA,wBAAwB;QAClDU,WAAW,EAAE;OACd;MAEDoH,OAAO,CAACC,GAAG,CAAC,UAAUqN,KAAK,CAACzU,SAAS,GAAG,EAAE0U,OAAO,CAAC;MAClDvN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsN,OAAO,CAAC5a,WAAW,CAAC;MAEhDsa,QAAQ,CAAChO,IAAI,CAACsO,OAAO,CAAC;IACxB,CAAC,CAAC;IAEFvN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgN,QAAQ,CAAC;IAElC;IACA,IAAI,CAACld,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAGkd,QAAQ,CAAC;IAEnE,IAAI,CAACjK,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACtH,GAAG,CAACuH,aAAa,EAAE;IAExB;IACAsG,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7N,GAAG,CAACuH,aAAa,EAAE;MACxBjD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,MAAMwN,aAAa,GAAGT,sBAAsB,IAAIA,sBAAsB,CAACld,MAAM,GAAG,CAAC,GAC7E,WAAWkd,sBAAsB,CAACld,MAAM,QAAQ,GAChD,EAAE;IACN,IAAI,CAACmL,OAAO,CAACmK,aAAa,CAAC,QAAQ6H,QAAQ,CAACnd,MAAM,OAAO2d,aAAa,EAAE,CAAC;EAC3E;;;uCApwCW7S,4CAA4C,EAAA9L,EAAA,CAAA4e,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9e,EAAA,CAAA4e,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhf,EAAA,CAAA4e,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAjf,EAAA,CAAA4e,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAnf,EAAA,CAAA4e,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAArf,EAAA,CAAA4e,iBAAA,CAAAQ,EAAA,CAAAE,wBAAA,GAAAtf,EAAA,CAAA4e,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAxf,EAAA,CAAA4e,iBAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAA1f,EAAA,CAAA4e,iBAAA,CAAAe,EAAA,CAAAC,QAAA,GAAA5f,EAAA,CAAA4e,iBAAA,CAAAQ,EAAA,CAAAS,eAAA,GAAA7f,EAAA,CAAA4e,iBAAA,CAAAkB,EAAA,CAAAC,YAAA,GAAA/f,EAAA,CAAA4e,iBAAA,CAAAQ,EAAA,CAAAY,YAAA,GAAAhgB,EAAA,CAAA4e,iBAAA,CAAA5e,EAAA,CAAAigB,iBAAA,GAAAjgB,EAAA,CAAA4e,iBAAA,CAAAsB,EAAA,CAAAC,uBAAA;IAAA;EAAA;;;YAA5CrU,4CAA4C;MAAAsU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtgB,EAAA,CAAAugB,0BAAA,EAAAvgB,EAAA,CAAAwgB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9CjD9gB,EAHR,CAAAC,cAAA,aAAqE,iBAAgD,wBACnD,aACb,aACJ;UACvCD,EAAA,CAAAU,SAAA,aAAqD;UACrDV,EAAA,CAAAC,cAAA,UAAK;UACHD,EAAA,CAAAU,SAAA,qBAAiC;UAErCV,EADE,CAAAW,YAAA,EAAM,EACF;UAEJX,EADF,CAAAC,cAAA,aAAyC,cAC8C;UACnFD,EAAA,CAAAY,MAAA,IACF;UAGNZ,EAHM,CAAAW,YAAA,EAAO,EACH,EACF,EACS;UAQPX,EANV,CAAAC,cAAA,wBAAqC,eACZ,eAEiD,cACrB,eACJ,eACwC;;UAC7ED,EAAA,CAAAC,cAAA,eAAyF;UACvFD,EAAA,CAAAU,SAAA,gBAEO;UAEXV,EADE,CAAAW,YAAA,EAAM,EACF;;UAEJX,EADF,CAAAC,cAAA,WAAK,cACyC;UAAAD,EAAA,CAAAY,MAAA,IAAkB;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACnEX,EAAA,CAAAC,cAAA,aAAsC;UAAAD,EAAA,CAAAY,MAAA,4FAAc;UAExDZ,EAFwD,CAAAW,YAAA,EAAI,EACpD,EACF;UAKFX,EAFJ,CAAAC,cAAA,cAAyC,eACf,eACyB;UAC7CD,EAAA,CAAAY,MAAA,IACF;UAINZ,EAJM,CAAAW,YAAA,EAAM,EACF,EAEF,EACF;UAGNX,EAAA,CAAA0B,UAAA,KAAAsf,4DAAA,kBAA6D;UAkCrDhhB,EANR,CAAAC,cAAA,eAAiG,eAChC,eAEd,eAER,kBAG4C;UAA7ED,EAAA,CAAAE,UAAA,mBAAA+gB,+EAAA;YAAA,OAASF,GAAA,CAAAnE,oBAAA,EAAsB;UAAA,EAAC;;UAChC5c,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAAgG;UAClGV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAY,MAAA,kCACF;UACFZ,EADE,CAAAW,YAAA,EAAS,EACL;UAGNX,EAAA,CAAA0B,UAAA,KAAAwf,4DAAA,mBAA2E;UAwB7ElhB,EAAA,CAAAW,YAAA,EAAM;;UAKJX,EAFF,CAAAC,cAAA,eAAqC,eAGsF;;UACvHD,EAAA,CAAAC,cAAA,eAAyF;UACvFD,EAAA,CAAAU,SAAA,gBACuE;UACzEV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAY,MAAA,kFACF;UAIRZ,EAJQ,CAAAW,YAAA,EAAM,EACF,EACF,EACF,EACF;UAkBNX,EAfA,CAAA0B,UAAA,KAAAyf,4DAAA,mBAA8F,KAAAC,qEAAA,6BAeR;UA2Y1FphB,EADE,CAAAW,YAAA,EAAM,EACO;;UAMTX,EAHN,CAAAC,cAAA,0BAAiH,eACjE,eACmB,cACpB;;UACvCD,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBACuE;UACzEV,EAAA,CAAAW,YAAA,EAAM;;UACNX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,IAA0C;UAClDZ,EADkD,CAAAW,YAAA,EAAO,EACnD;UAGJX,EADF,CAAAC,cAAA,cAAyC,cACE;UACvCD,EAAA,CAAAU,SAAA,eAAoD;UACpDV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,IAA2C;UAErDZ,EAFqD,CAAAW,YAAA,EAAO,EACpD,EACF;UAENX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAY,MAAA,kFACF;UAKVZ,EALU,CAAAW,YAAA,EAAM,EACF,EACF,EACS,EACT,EACN;UAGNX,EAAA,CAAA0B,UAAA,KAAA2f,qEAAA,2BAA8E;UA4FxErhB,EAJN,CAAAC,cAAA,eAAmE,eAC9B,eAEG,kBAEkE;UAAlGD,EAAA,CAAAE,UAAA,mBAAAohB,+EAAA;YAAA,OAASP,GAAA,CAAArL,QAAA,EAAU;UAAA,EAAC;UAUpB1V,EAPA,CAAA0B,UAAA,KAAA6f,iEAAA,kBAA6G,KAAAC,iEAAA,kBAOZ;UAKjGxhB,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAY,MAAA,IACF;UAEJZ,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;UAGNX,EAAA,CAAA0B,UAAA,KAAA+f,4DAAA,kBAAkE;UAiBhEzhB,EADF,CAAAC,cAAA,eAAoC,kBACwE;UAA3DD,EAAA,CAAAE,UAAA,mBAAAwhB,+EAAA;YAAA,OAASX,GAAA,CAAAxS,MAAA,EAAQ;UAAA,EAAC;;UAC/DvO,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA6G;UAC/GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAY,MAAA,sBACF;UAEJZ,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;UAIJX,EADF,CAAAC,cAAA,eAAoC,kBACgD;UAAjCD,EAAA,CAAAE,UAAA,mBAAAyhB,+EAAA;YAAA,OAASZ,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;;UAClElb,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA2G;UAC7GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAY,MAAA,kCACF;UAEJZ,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;UAIJX,EADF,CAAAC,cAAA,eAAoC,kBACsD;UAAvCD,EAAA,CAAAE,UAAA,mBAAA0hB,+EAAA;YAAA,OAASb,GAAA,CAAAtF,cAAA,EAAgB;UAAA,EAAC;;UACzEzb,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA4G;UAC9GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAY,MAAA,4BACF;UAIRZ,EAJQ,CAAAW,YAAA,EAAM,EACC,EACL,EACF,EACF;;;;;UAluBMX,EAAA,CAAAa,SAAA,IACF;UADEb,EAAA,CAAA4E,kBAAA,MAAAmc,GAAA,CAAAxT,YAAA,MACF;UAmBkDvN,EAAA,CAAAa,SAAA,IAAkB;UAAlBb,EAAA,CAAA2G,iBAAA,CAAAoa,GAAA,CAAAxT,YAAA,CAAkB;UAS5DvN,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAA4E,kBAAA,yBAAAmc,GAAA,CAAA9f,kBAAA,kBAAA8f,GAAA,CAAA9f,kBAAA,CAAAD,MAAA,+BACF;UAOkBhB,EAAA,CAAAa,SAAA,EAAmC;UAAnCb,EAAA,CAAA8B,UAAA,SAAAif,GAAA,CAAA9f,kBAAA,CAAAD,MAAA,KAAmC;UAoChBhB,EAAA,CAAAa,SAAA,GAA2C;UAA3Cb,EAAA,CAAA8B,UAAA,cAAA+f,OAAA,GAAAd,GAAA,CAAAjb,YAAA,kBAAAib,GAAA,CAAAjb,YAAA,CAAAC,OAAA,cAAA8b,OAAA,KAAA7b,SAAA,GAAA6b,OAAA,SAA2C;UAS1C7hB,EAAA,CAAAa,SAAA,GAAmC;UAAnCb,EAAA,CAAA8B,UAAA,SAAAif,GAAA,CAAA9f,kBAAA,CAAAD,MAAA,KAAmC;UA2C3EhB,EAAA,CAAAa,SAAA,GAA4D;UAA5Db,EAAA,CAAA8B,UAAA,SAAAif,GAAA,CAAAxf,WAAA,IAAAwf,GAAA,CAAAhgB,0BAAA,CAAAC,MAAA,OAA4D;UAe5BhB,EAAA,CAAAa,SAAA,EAA+B;UAA/Bb,EAAA,CAAA8B,UAAA,YAAAif,GAAA,CAAAhgB,0BAAA,CAA+B;UAsZ3Df,EAAA,CAAAa,SAAA,GAA0C;UAA1Cb,EAAA,CAAA4E,kBAAA,YAAAmc,GAAA,CAAA9f,kBAAA,CAAAD,MAAA,yCAA0C;UAMxChB,EAAA,CAAAa,SAAA,GAA2C;UAA3Cb,EAAA,CAAA4E,kBAAA,kBAAAmc,GAAA,CAAA9f,kBAAA,CAAAD,MAAA,6BAA2C;UAczBhB,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAA8B,UAAA,YAAAif,GAAA,CAAA9f,kBAAA,CAAuB;UA4FcjB,EAAA,CAAAa,SAAA,GAAoC;UAApCb,EAAA,CAAAkE,WAAA,kBAAA6c,GAAA,CAAAlV,YAAA,CAAoC;UAClF7L,EAAA,CAAA8B,UAAA,eAAAmE,QAAA,GAAA8a,GAAA,CAAAjb,YAAA,kBAAAib,GAAA,CAAAjb,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,aAAA8a,GAAA,CAAAlV,YAAA,CAA+D;UAG9E7L,EAAA,CAAAa,SAAA,EAAkB;UAAlBb,EAAA,CAAA8B,UAAA,SAAAif,GAAA,CAAAlV,YAAA,CAAkB;UAOlB7L,EAAA,CAAAa,SAAA,EAAmB;UAAnBb,EAAA,CAAA8B,UAAA,UAAAif,GAAA,CAAAlV,YAAA,CAAmB;UAMvB7L,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAA4E,kBAAA,MAAAmc,GAAA,CAAAlV,YAAA,6DACF;UAKiC7L,EAAA,CAAAa,SAAA,EAA2B;UAA3Bb,EAAA,CAAA8B,UAAA,SAAAif,GAAA,CAAAjb,YAAA,kBAAAib,GAAA,CAAAjb,YAAA,CAAAC,OAAA,CAA2B;UAiBI/F,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAA8B,UAAA,aAAAif,GAAA,CAAAlV,YAAA,CAAyB;;;qBDxpBrFxM,YAAY,EAAAsgB,EAAA,CAAAmC,OAAA,EAAAnC,EAAA,CAAAoC,IAAA,EAAEziB,WAAW,EAAA0iB,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,OAAA,EAAE1iB,YAAY,EAAA2iB,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEpjB,eAAe,EAAAqjB,GAAA,CAAAC,yBAAA,EAAE1jB,gBAAgB,EAAEO,eAAe;MAAAojB,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}