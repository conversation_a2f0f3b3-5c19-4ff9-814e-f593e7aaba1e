{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbSpinnerModule } from '@nebular/theme';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"nb-spinner\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 46);\n  }\n  if (rf & 2) {\n    const image_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r6), i0.ɵɵsanitizeUrl)(\"alt\", image_r6.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_Template_div_click_0_listener() {\n      const image_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectImageFromAvailable(image_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"div\", 39);\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_img_3_Template, 1, 2, \"img\", 40)(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_div_4_Template, 2, 0, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42)(6, \"div\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\", 24);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"button\", 45);\n    i0.ɵɵelement(12, \"i\", 31);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const image_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"border-primary\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(image_r6.size));\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 53)(1, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_li_9_Template_button_click_1_listener() {\n      const page_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.goToPage(page_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"active\", page_r9 === ctx_r1.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r9);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"nav\")(2, \"ul\", 52)(3, \"li\", 53)(4, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(1));\n    });\n    i0.ɵɵelement(5, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 53)(7, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage - 1));\n    });\n    i0.ɵɵelement(8, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_li_9_Template, 3, 3, \"li\", 57);\n    i0.ɵɵelementStart(10, \"li\", 53)(11, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage + 1));\n    });\n    i0.ɵɵelement(12, \"i\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 53)(14, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.totalPages));\n    });\n    i0.ɵɵelement(15, \"i\", 59);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 46);\n  }\n  if (rf & 2) {\n    const image_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r11), i0.ɵɵsanitizeUrl)(\"alt\", image_r11.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template_div_click_0_listener() {\n      const image_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectImageFromChosen(image_r11));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"div\", 44)(3, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template_button_click_3_listener($event) {\n      const image_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedImage(image_r11, $event));\n    });\n    i0.ɵɵelement(4, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 39);\n    i0.ɵɵtemplate(6, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_img_6_Template, 1, 2, \"img\", 40)(7, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_div_7_Template, 2, 0, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 42)(9, \"div\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\", 24);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const image_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"border-success\", (ctx_r1.selectedChosenImage == null ? null : ctx_r1.selectedChosenImage.id) === image_r11.id);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r11.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(image_r11.size));\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5C1A\\u672A\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22)(3, \"h6\", 23);\n    i0.ɵɵtext(4, \"\\u53EF\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 24);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵtemplate(8, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_Template, 13, 7, \"div\", 26)(9, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_9_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template, 16, 13, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 29)(12, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectAllVisible());\n    });\n    i0.ɵɵelement(13, \"i\", 31)(14, \"br\");\n    i0.ɵɵelementStart(15, \"small\");\n    i0.ɵɵtext(16, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectSingle());\n    });\n    i0.ɵɵelement(18, \"i\", 31)(19, \"br\");\n    i0.ɵɵelementStart(20, \"small\");\n    i0.ɵɵtext(21, \"\\u9078\\u64C7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deselectSingle());\n    });\n    i0.ɵɵelement(23, \"i\", 34)(24, \"br\");\n    i0.ɵɵelementStart(25, \"small\");\n    i0.ɵɵtext(26, \"\\u79FB\\u9664\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deselectAll());\n    });\n    i0.ɵɵelement(28, \"i\", 34)(29, \"br\");\n    i0.ɵɵelementStart(30, \"small\");\n    i0.ɵɵtext(31, \"\\u5168\\u79FB\\u9664\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"h6\", 23);\n    i0.ɵɵtext(35, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"small\", 24);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 25);\n    i0.ɵɵtemplate(39, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template, 13, 7, \"div\", 36)(40, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_40_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r1.totalRecords, \" \\u7B46\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pagedAvailableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pagedAvailableImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalPages > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.pagedAvailableImages.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedAvailableImage === null);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedChosenImage === null);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r1.tempSelectedImages.length, \" \\u7B46\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.tempSelectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tempSelectedImages.length === 0);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"label\", 7);\n    i0.ɵɵtext(7, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-select\", 8);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChanged($event));\n    });\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"label\", 7);\n    i0.ɵɵtext(12, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchTermChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(14, ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template, 4, 0, \"div\", 11)(15, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template, 41, 11, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 13)(17, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r12 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onCancelBinding();\n      return i0.ɵɵresetView(ref_r12.close());\n    });\n    i0.ɵɵtext(18, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r12 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onConfirmBinding();\n      return i0.ɵɵresetView(ref_r12.close());\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r1.materialName || \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.tempSelectedImages.length, \") \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"nb-spinner\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 74);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(ctx_r1.previewingImage), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u53EF\\u9810\\u89BD\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 63)(1, \"nb-card-header\", 64)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 65)(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 34);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 66);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template, 4, 0, \"div\", 67)(13, ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template, 1, 2, \"img\", 68)(14, ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template, 4, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 64)(16, \"div\", 70);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 65);\n    i0.ɵɵtemplate(19, ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template, 2, 1, \"button\", 71);\n    i0.ɵɵelementStart(20, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ref_r12 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r12.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.previewingImage && ctx_r1.getImageUrl(ctx_r1.previewingImage));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && (!ctx_r1.previewingImage || !ctx_r1.getImageUrl(ctx_r1.previewingImage)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_nb_card_0_Template, 21, 8, \"nb-card\", 1)(1, ImagePreviewComponent_ng_template_0_nb_card_1_Template, 22, 9, \"nb-card\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBindingInterface);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showBindingInterface);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService, pictureService, messageService) {\n    this.dialogService = dialogService;\n    this.pictureService = pictureService;\n    this.messageService = messageService;\n    this.isVisible = false;\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    // 新增綁定功能相關輸入參數\n    this.showBindingInterface = false;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    // 新增綁定功能相關輸出事件\n    this.confirmImageBinding = new EventEmitter();\n    this.categoryChange = new EventEmitter();\n    // 內部屬性，不再依賴外部傳入\n    this.images = [];\n    this.availableImages = [];\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    this.isLoading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 5; // 改為一頁5筆\n    this.totalRecords = 0;\n    this.totalPages = 0;\n    this.pagedAvailableImages = [];\n    // 綁定模式相關屬性\n    this.categoryOptions = [{\n      value: 1,\n      label: '建材圖片'\n    }, {\n      value: 2,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = 1;\n    this.tempSelectedImages = []; // 臨時選擇的圖片\n    // picklist 選擇狀態\n    this.selectedAvailableImage = null;\n    this.selectedChosenImage = null;\n  }\n  ngOnInit() {\n    this.loadImages();\n  }\n  ngOnChanges() {\n    // 當輸入參數變化時重新載入圖片\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\n      this.loadImages();\n    }\n  }\n  // 載入圖片的主要方法\n  loadImages() {\n    if (!this.buildCaseId && !this.materialId) {\n      return;\n    }\n    this.isLoading = true;\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n  }\n  // 載入可選擇的圖片\n  loadAvailableImages() {\n    if (!this.buildCaseId) {\n      this.availableImages = [];\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        cPictureType: this.pictureType,\n        PageIndex: this.currentPage,\n        PageSize: this.pageSize,\n        CName: this.searchTerm || undefined\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          // 轉換 API 回應為 ImageItem 格式\n          const newImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n          this.totalRecords = res.TotalItems || 0;\n          this.totalPages = Math.ceil(this.totalRecords / this.pageSize);\n          // 排除已選擇的圖片\n          const selectedIds = this.selectedImages.map(img => img.id);\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\n          // 設定分頁顯示\n          this.updatePagedImages();\n          // 合併可選和已選圖片作為完整圖片列表\n          this.images = [...this.availableImages, ...this.selectedImages];\n          // 設定初始預覽圖片\n          this.setInitialPreviewImage();\n        } else {\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\n        this.isLoading = false;\n      }\n    });\n  }\n  // 載入已選擇的圖片（如果有 materialId）\n  loadSelectedImages() {\n    if (!this.buildCaseId || !this.materialId) {\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CMaterialId: this.materialId,\n        cPictureType: this.pictureType,\n        PageIndex: 1,\n        PageSize: 999 // 載入所有已選圖片\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n        }\n      },\n      error: error => {\n        console.error('載入已選擇圖片失敗:', error);\n      }\n    });\n  }\n  // 設定初始預覽圖片\n  setInitialPreviewImage() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    } else {\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    // 如果還沒有圖片，先載入圖片\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    // 開啟對話框\n    const template = imagePreviewRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 使用 CGuid 取得圖片資料的方法\n  getPictureByGuid(guid) {\n    if (!guid) {\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\n      return;\n    }\n    this.pictureService.apiPictureGetPictureGuidGet({\n      guid: guid\n    }).subscribe({\n      next: response => {\n        // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\n        console.log('取得圖片資料:', response);\n      },\n      error: error => {\n        this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\n      }\n    });\n  }\n  // 獲取圖片 URL，優先使用 CGuid\n  getImageUrl(imageItem) {\n    if (imageItem.guid && !imageItem.fullUrl) {\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\n      this.getPictureByGuid(imageItem.guid);\n    }\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\n  }\n  // 綁定模式相關方法\n  onCategoryChanged(category) {\n    this.selectedCategory = category;\n    this.categoryChange.emit(category);\n    if (this.buildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 開啟綁定界面\n  openBindingInterface(imageBindingRef) {\n    this.showBindingInterface = true;\n    this.tempSelectedImages = [...this.selectedImages];\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    const template = imageBindingRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 確認圖片綁定\n  onConfirmBinding() {\n    this.confirmImageBinding.emit(this.tempSelectedImages);\n    this.showBindingInterface = false;\n  }\n  // 取消綁定\n  onCancelBinding() {\n    this.tempSelectedImages = [];\n    this.showBindingInterface = false;\n    this.onClose();\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n    } else {\n      this.tempSelectedImages.push(image);\n    }\n  }\n  // 檢查圖片是否被選中\n  isImageTempSelected(image) {\n    return this.tempSelectedImages.some(img => img.id === image.id);\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.PictureService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\",\n        buildCaseId: \"buildCaseId\",\n        materialId: \"materialId\",\n        pictureType: \"pictureType\",\n        searchTerm: \"searchTerm\",\n        showBindingInterface: \"showBindingInterface\",\n        materialName: \"materialName\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\",\n        confirmImageBinding: \"confirmImageBinding\",\n        categoryChange: \"categoryChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [\"class\", \"w-[95vw] max-w-[1200px] h-[85vh]\", 4, \"ngIf\"], [\"class\", \"w-[800px] h-[600px]\", 4, \"ngIf\"], [1, \"w-[95vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\"], [1, \"d-flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [1, \"form-label\"], [\"placeholder\", \"\\u9078\\u64C7\\u5716\\u7247\\u985E\\u5225\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"flex-1 d-flex gap-3\", \"style\", \"min-height: 0;\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"text-center\", \"py-4\"], [\"size\", \"large\"], [1, \"mt-2\"], [1, \"flex-1\", \"d-flex\", \"gap-3\", 2, \"min-height\", \"0\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"text-muted\"], [1, \"flex-1\", 2, \"overflow-y\", \"auto\"], [\"class\", \"border rounded p-2 mb-2 cursor-pointer hover-bg-light\", 3, \"border-primary\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"mt-3 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"100px\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"border rounded p-2 mb-2 cursor-pointer hover-bg-light\", 3, \"border-success\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"rounded\", \"p-2\", \"mb-2\", \"cursor-pointer\", \"hover-bg-light\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\", \"gap-3\"], [1, \"flex-shrink-0\", 2, \"width\", \"60px\", \"height\", \"60px\"], [\"class\", \"w-100 h-100 object-cover rounded\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"fw-medium\", \"text-truncate\", 3, \"title\"], [1, \"flex-shrink-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\"], [1, \"w-100\", \"h-100\", \"object-cover\", \"rounded\", 3, \"src\", \"alt\"], [1, \"w-100\", \"h-100\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"bg-light\", \"rounded\"], [1, \"fas\", \"fa-image\", \"text-muted\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"fas\", \"fa-images\", \"fa-2x\", \"mb-2\"], [1, \"mt-3\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-angle-right\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-link\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"mb-2\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"gap-2\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-center\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbSelectComponent, i1.NbOptionComponent, NbSpinnerModule, i1.NbSpinnerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbSpinnerModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ctx_r1", "getImageUrl", "image_r6", "ɵɵsanitizeUrl", "name", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_Template_div_click_0_listener", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "selectImageFromAvailable", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_img_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_div_4_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "formatFileSize", "size", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_li_9_Template_button_click_1_listener", "page_r9", "_r8", "goToPage", "currentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_4_listener", "_r7", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_7_listener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_li_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_11_listener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template_button_click_14_listener", "totalPages", "getVisiblePages", "image_r11", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template_div_click_0_listener", "_r10", "selectImageFromChosen", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template_button_click_3_listener", "$event", "removeSelectedImage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_img_6_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_div_7_Template", "selectedChosenImage", "id", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_8_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_10_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_12_listener", "_r4", "selectAllVisible", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_17_listener", "selectSingle", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_22_listener", "deselectSingle", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template_button_click_27_listener", "deselectAll", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_39_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_40_Template", "totalRecords", "pagedAvailableImages", "length", "selectedAvailableImage", "tempSelectedImages", "ɵɵtwoWayListener", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener", "_r1", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "onCategoryChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener", "searchTerm", "onSearchTermChange", "ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_17_listener", "ref_r12", "dialogRef", "onCancelBinding", "close", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener", "onConfirmBinding", "materialName", "ɵɵtwoWayProperty", "categoryOptions", "isLoading", "previewingImage", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener", "_r14", "onToggleImageSelection", "isImageSelected", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener", "_r13", "onPreviousImage", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener", "onNextImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener", "onClose", "currentPreviewIndex", "images", "ɵɵtextInterpolate2", "showSelectionToggle", "ImagePreviewComponent_ng_template_0_nb_card_0_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template", "showBindingInterface", "ImagePreviewComponent", "constructor", "dialogService", "pictureService", "messageService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "confirmImageBinding", "categoryChange", "availableImages", "pageSize", "ngOnInit", "loadImages", "ngOnChanges", "buildCaseId", "materialId", "pictureType", "undefined", "loadAvailableImages", "loadSelectedImages", "apiPictureGetPictureListPost$Json", "body", "CBuildCaseId", "cPictureType", "PageIndex", "PageSize", "CName", "subscribe", "next", "res", "StatusCode", "newImages", "Entries", "map", "picture", "CId", "CPictureCode", "thumbnailUrl", "CFile", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "TotalItems", "Math", "ceil", "selectedIds", "img", "filter", "image", "includes", "updatePagedImages", "setInitialPreviewImage", "showErrorMSG", "Message", "error", "message", "CMaterialId", "console", "max", "min", "emit", "some", "selected", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "getPictureByGuid", "apiPictureGetPictureGuidGet", "response", "log", "imageItem", "category", "openBindingInterface", "imageBindingRef", "toggleImageSelection", "index", "findIndex", "splice", "push", "isImageTempSelected", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "PictureService", "i3", "MessageService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "NbSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService, NbSpinnerModule } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\nimport { PictureService } from 'src/services/api/services';\r\nimport { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbSpinnerModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit, OnChanges {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n\r\n  // 新增輸入參數來接收圖片載入所需的資訊\r\n  @Input() buildCaseId?: number;\r\n  @Input() materialId?: number;\r\n  @Input() pictureType?: number;\r\n  @Input() searchTerm?: string;\r\n\r\n  // 新增綁定功能相關輸入參數\r\n  @Input() showBindingInterface: boolean = false;\r\n  @Input() materialName?: string;\r\n\r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  // 新增綁定功能相關輸出事件\r\n  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();\r\n  @Output() categoryChange = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  // 內部屬性，不再依賴外部傳入\r\n  images: ImageItem[] = [];\r\n  availableImages: ImageItem[] = [];\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n  isLoading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 5; // 改為一頁5筆\r\n  totalRecords: number = 0;\r\n  totalPages: number = 0;\r\n  pagedAvailableImages: ImageItem[] = [];\r\n\r\n  // 綁定模式相關屬性\r\n  categoryOptions = [\r\n    { value: 1, label: '建材圖片' },\r\n    { value: 2, label: '示意圖片' }\r\n  ];\r\n  selectedCategory: number = 1;\r\n  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片\r\n  \r\n  // picklist 選擇狀態\r\n  selectedAvailableImage: ImageItem | null = null;\r\n  selectedChosenImage: ImageItem | null = null;\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private pictureService: PictureService,\r\n    private messageService: MessageService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadImages();\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // 當輸入參數變化時重新載入圖片\r\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 載入圖片的主要方法\r\n  loadImages(): void {\r\n    if (!this.buildCaseId && !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n  // 載入可選擇的圖片\r\n  loadAvailableImages(): void {\r\n    if (!this.buildCaseId) {\r\n      this.availableImages = [];\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: this.currentPage,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchTerm || undefined\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 轉換 API 回應為 ImageItem 格式\r\n          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n\r\n          this.totalRecords = res.TotalItems || 0;\r\n          this.totalPages = Math.ceil(this.totalRecords / this.pageSize);\r\n\r\n          // 排除已選擇的圖片\r\n          const selectedIds = this.selectedImages.map(img => img.id);\r\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\r\n          \r\n          // 設定分頁顯示\r\n          this.updatePagedImages();\r\n\r\n          // 合併可選和已選圖片作為完整圖片列表\r\n          this.images = [...this.availableImages, ...this.selectedImages];\r\n\r\n          // 設定初始預覽圖片\r\n          this.setInitialPreviewImage();\r\n        } else {\r\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 載入已選擇的圖片（如果有 materialId）\r\n  loadSelectedImages(): void {\r\n    if (!this.buildCaseId || !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CMaterialId: this.materialId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: 1,\r\n        PageSize: 999 // 載入所有已選圖片\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入已選擇圖片失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 設定初始預覽圖片\r\n  setInitialPreviewImage(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    } else {\r\n      this.previewingImage = null;\r\n      this.currentPreviewIndex = 0;\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    // 如果還沒有圖片，先載入圖片\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    // 開啟對話框\r\n    const template = imagePreviewRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 使用 CGuid 取得圖片資料的方法\r\n  getPictureByGuid(guid: string): void {\r\n    if (!guid) {\r\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\r\n      .subscribe({\r\n        next: (response) => {\r\n          // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\r\n          console.log('取得圖片資料:', response);\r\n        },\r\n        error: (error) => {\r\n          this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 獲取圖片 URL，優先使用 CGuid\r\n  getImageUrl(imageItem: ImageItem): string {\r\n    if (imageItem.guid && !imageItem.fullUrl) {\r\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\r\n      this.getPictureByGuid(imageItem.guid);\r\n    }\r\n\r\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\r\n  }\r\n\r\n  // 綁定模式相關方法\r\n  onCategoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.categoryChange.emit(category);\r\n    if (this.buildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 開啟綁定界面\r\n  openBindingInterface(imageBindingRef?: TemplateRef<any>) {\r\n    this.showBindingInterface = true;\r\n    this.tempSelectedImages = [...this.selectedImages];\r\n\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    const template = imageBindingRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 確認圖片綁定\r\n  onConfirmBinding() {\r\n    this.confirmImageBinding.emit(this.tempSelectedImages);\r\n    this.showBindingInterface = false;\r\n  }\r\n\r\n  // 取消綁定\r\n  onCancelBinding() {\r\n    this.tempSelectedImages = [];\r\n    this.showBindingInterface = false;\r\n    this.onClose();\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n    } else {\r\n      this.tempSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否被選中\r\n  isImageTempSelected(image: ImageItem): boolean {\r\n    return this.tempSelectedImages.some(img => img.id === image.id);\r\n  }\r\n}\r\n", "<!-- 圖片預覽/綁定對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <!-- 綁定模式 - Picklist 風格 -->\r\n  <nb-card *ngIf=\"showBindingInterface\" class=\"w-[95vw] max-w-[1200px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ materialName || '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"d-flex flex-column\" style=\"height: calc(100% - 120px); overflow: hidden;\">\r\n      \r\n      <!-- 控制區 -->\r\n      <div class=\"d-flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">圖片類別</label>\r\n          <nb-select [(selected)]=\"selectedCategory\" placeholder=\"選擇圖片類別\"\r\n            (selectedChange)=\"onCategoryChanged($event)\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"searchTerm\" \r\n            (ngModelChange)=\"onSearchTermChange($event)\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center py-4\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n\r\n      <!-- Picklist 主體 -->\r\n      <div *ngIf=\"!isLoading\" class=\"flex-1 d-flex gap-3\" style=\"min-height: 0;\">\r\n        \r\n        <!-- 可選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">可選擇圖片</h6>\r\n            <small class=\"text-muted\">共 {{ totalRecords }} 筆</small>\r\n          </div>\r\n          \r\n          <!-- 可選擇列表 -->\r\n          <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n            <div *ngFor=\"let image of pagedAvailableImages\" class=\"border rounded p-2 mb-2 cursor-pointer hover-bg-light\"\r\n              [class.border-primary]=\"false\" (click)=\"selectImageFromAvailable(image)\">\r\n              <div class=\"d-flex align-items-center gap-3\">\r\n                <!-- 圖片縮圖 -->\r\n                <div class=\"flex-shrink-0\" style=\"width: 60px; height: 60px;\">\r\n                  <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                    class=\"w-100 h-100 object-cover rounded\" />\r\n                  <div *ngIf=\"!getImageUrl(image)\" class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                    <i class=\"fas fa-image text-muted\"></i>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- 圖片資訊 -->\r\n                <div class=\"flex-1 min-w-0\">\r\n                  <div class=\"fw-medium text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                  <small class=\"text-muted\">{{ formatFileSize(image.size) }}</small>\r\n                </div>\r\n                \r\n                <!-- 選擇按鈕 -->\r\n                <div class=\"flex-shrink-0\">\r\n                  <button class=\"btn btn-outline-primary btn-sm\" type=\"button\">\r\n                    <i class=\"fas fa-chevron-right\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"pagedAvailableImages.length === 0\" class=\"text-center py-4 text-muted\">\r\n              <i class=\"fas fa-images fa-2x mb-2\"></i>\r\n              <div>找不到圖片</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 分頁控制 -->\r\n          <div *ngIf=\"totalPages > 1\" class=\"mt-3 d-flex justify-content-center\">\r\n            <nav>\r\n              <ul class=\"pagination pagination-sm mb-0\">\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">\r\n                    <i class=\"fas fa-angle-double-left\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">\r\n                    <i class=\"fas fa-angle-left\"></i>\r\n                  </button>\r\n                </li>\r\n                <li *ngFor=\"let page of getVisiblePages()\" class=\"page-item\" [class.active]=\"page === currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\">\r\n                    <i class=\"fas fa-angle-right\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\">\r\n                    <i class=\"fas fa-angle-double-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 控制按鈕區 -->\r\n        <div class=\"d-flex flex-column justify-content-center gap-2\" style=\"width: 100px;\">\r\n          <button class=\"btn btn-primary btn-sm\" (click)=\"selectAllVisible()\" [disabled]=\"pagedAvailableImages.length === 0\">\r\n            <i class=\"fas fa-chevron-right\"></i><br>\r\n            <small>全選</small>\r\n          </button>\r\n          <button class=\"btn btn-outline-primary btn-sm\" (click)=\"selectSingle()\" [disabled]=\"selectedAvailableImage === null\">\r\n            <i class=\"fas fa-chevron-right\"></i><br>\r\n            <small>選擇</small>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"deselectSingle()\" [disabled]=\"selectedChosenImage === null\">\r\n            <i class=\"fas fa-chevron-left\"></i><br>\r\n            <small>移除</small>\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"deselectAll()\" [disabled]=\"tempSelectedImages.length === 0\">\r\n            <i class=\"fas fa-chevron-left\"></i><br>\r\n            <small>全移除</small>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 已選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">已選擇圖片</h6>\r\n            <small class=\"text-muted\">共 {{ tempSelectedImages.length }} 筆</small>\r\n          </div>\r\n          \r\n          <!-- 已選擇列表 -->\r\n          <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n            <div *ngFor=\"let image of tempSelectedImages; let i = index\" \r\n              class=\"border rounded p-2 mb-2 cursor-pointer hover-bg-light\"\r\n              [class.border-success]=\"selectedChosenImage?.id === image.id\"\r\n              (click)=\"selectImageFromChosen(image)\">\r\n              <div class=\"d-flex align-items-center gap-3\">\r\n                <!-- 移除按鈕 -->\r\n                <div class=\"flex-shrink-0\">\r\n                  <button class=\"btn btn-outline-danger btn-sm\" type=\"button\" (click)=\"removeSelectedImage(image, $event)\">\r\n                    <i class=\"fas fa-chevron-left\"></i>\r\n                  </button>\r\n                </div>\r\n                \r\n                <!-- 圖片縮圖 -->\r\n                <div class=\"flex-shrink-0\" style=\"width: 60px; height: 60px;\">\r\n                  <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                    class=\"w-100 h-100 object-cover rounded\" />\r\n                  <div *ngIf=\"!getImageUrl(image)\" class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                    <i class=\"fas fa-image text-muted\"></i>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- 圖片資訊 -->\r\n                <div class=\"flex-1 min-w-0\">\r\n                  <div class=\"fw-medium text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                  <small class=\"text-muted\">{{ formatFileSize(image.size) }}</small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"tempSelectedImages.length === 0\" class=\"text-center py-4 text-muted\">\r\n              <i class=\"fas fa-folder-open fa-2x mb-2\"></i>\r\n              <div>尚未選擇圖片</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onCancelBinding(); ref.close()\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onConfirmBinding(); ref.close()\" \r\n        [disabled]=\"tempSelectedImages.length === 0\">\r\n        確定選擇 ({{ tempSelectedImages.length }})\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n\r\n  <!-- 預覽模式 -->\r\n  <nb-card *ngIf=\"!showBindingInterface\" class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n      \r\n      <!-- 圖片顯示 -->\r\n      <img *ngIf=\"!isLoading && previewingImage && getImageUrl(previewingImage)\" \r\n        [src]=\"getImageUrl(previewingImage)\"\r\n        [alt]=\"previewingImage.name\" \r\n        class=\"max-w-full max-h-full object-contain\" />\r\n      \r\n      <!-- 無圖片狀態 -->\r\n      <div *ngIf=\"!isLoading && (!previewingImage || !getImageUrl(previewingImage))\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>無可預覽的圖片</div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAmD,eAAe;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAA0BC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;ICY1DC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAWNT,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAmBMH,EAAA,CAAAU,SAAA,cAC6C;;;;;IADcV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAb,EAAA,CAAAc,aAAA,CAA0B,QAAAD,QAAA,CAAAE,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IATZH,EAAA,CAAAC,cAAA,cAC2E;IAA1CD,EAAA,CAAAgB,UAAA,mBAAAC,yFAAA;MAAA,MAAAJ,QAAA,GAAAb,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAY,wBAAA,CAAAV,QAAA,CAA+B;IAAA,EAAC;IAGtEb,EAFF,CAAAC,cAAA,cAA6C,cAEmB;IAG5DD,EAFA,CAAAwB,UAAA,IAAAC,yEAAA,kBAC6C,IAAAC,yEAAA,kBAC0E;IAGzH1B,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,cACgC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChFH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAC5DF,EAD4D,CAAAG,YAAA,EAAQ,EAC9D;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBACoC;IAC3DD,EAAA,CAAAU,SAAA,aAAoC;IAI5CV,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IAxBJH,EAAA,CAAA2B,WAAA,yBAA8B;IAIpB3B,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAwB;IAExBb,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAyB;IAOMb,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAS,QAAA,CAAAE,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA4B,iBAAA,CAAAf,QAAA,CAAAE,IAAA,CAAgB;IAChDf,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA4B,iBAAA,CAAAjB,MAAA,CAAAkB,cAAA,CAAAhB,QAAA,CAAAiB,IAAA,EAAgC;;;;;IAahE9B,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAU,SAAA,YAAwC;IACxCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IACZF,EADY,CAAAG,YAAA,EAAM,EACZ;;;;;;IAkBAH,EADF,CAAAC,cAAA,aAAmG,iBAC9C;IAAzBD,EAAA,CAAAgB,UAAA,mBAAAe,kGAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAkB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuB,QAAA,CAAAF,OAAA,CAAc;IAAA,EAAC;IAAChC,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAA2B,WAAA,WAAAK,OAAA,KAAArB,MAAA,CAAAwB,WAAA,CAAqC;IAC7CnC,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAA4B,iBAAA,CAAAI,OAAA,CAAU;;;;;;IAV7DhC,EAJR,CAAAC,cAAA,cAAuE,UAChE,aACuC,aACmB,iBACsB;IAArDD,EAAA,CAAAgB,UAAA,mBAAAoB,6FAAA;MAAApC,EAAA,CAAAkB,aAAA,CAAAmB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuB,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7ClC,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,aAA2D,iBACoC;IAAnED,EAAA,CAAAgB,UAAA,mBAAAsB,6FAAA;MAAAtC,EAAA,CAAAkB,aAAA,CAAAmB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAAwB,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAC3DnC,EAAA,CAAAU,SAAA,YAAiC;IAErCV,EADE,CAAAG,YAAA,EAAS,EACN;IACLH,EAAA,CAAAwB,UAAA,IAAAe,yEAAA,iBAAmG;IAIjGvC,EADF,CAAAC,cAAA,cAAoE,kBACoC;IAA5ED,EAAA,CAAAgB,UAAA,mBAAAwB,8FAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAmB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAAwB,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAC3DnC,EAAA,CAAAU,SAAA,aAAkC;IAEtCV,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,cAAoE,kBAC+B;IAAvED,EAAA,CAAAgB,UAAA,mBAAAyB,8FAAA;MAAAzC,EAAA,CAAAkB,aAAA,CAAAmB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAA+B,UAAA,CAAoB;IAAA,EAAC;IACtD1C,EAAA,CAAAU,SAAA,aAAyC;IAKnDV,EAJQ,CAAAG,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAzBsBH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA2B,WAAA,aAAAhB,MAAA,CAAAwB,WAAA,OAAoC;IACRnC,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwB,WAAA,OAA8B;IAI1DnC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA2B,WAAA,aAAAhB,MAAA,CAAAwB,WAAA,OAAoC;IACMnC,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwB,WAAA,OAA8B;IAIzEnC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAgC,eAAA,GAAoB;IAGnB3C,EAAA,CAAAO,SAAA,EAA6C;IAA7CP,EAAA,CAAA2B,WAAA,aAAAhB,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAA6C;IACH1C,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAAuC;IAIjF1C,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAA2B,WAAA,aAAAhB,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAA6C;IACR1C,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAAuC;;;;;IAoDhG1C,EAAA,CAAAU,SAAA,cAC6C;;;;;IADcV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAgC,SAAA,GAAA5C,EAAA,CAAAc,aAAA,CAA0B,QAAA8B,SAAA,CAAA7B,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IAlBZH,EAAA,CAAAC,cAAA,cAGyC;IAAvCD,EAAA,CAAAgB,UAAA,mBAAA6B,0FAAA;MAAA,MAAAD,SAAA,GAAA5C,EAAA,CAAAkB,aAAA,CAAA4B,IAAA,EAAA1B,SAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAoC,qBAAA,CAAAH,SAAA,CAA4B;IAAA,EAAC;IAIlC5C,EAHJ,CAAAC,cAAA,cAA6C,cAEhB,iBACgF;IAA7CD,EAAA,CAAAgB,UAAA,mBAAAgC,6FAAAC,MAAA;MAAA,MAAAL,SAAA,GAAA5C,EAAA,CAAAkB,aAAA,CAAA4B,IAAA,EAAA1B,SAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuC,mBAAA,CAAAN,SAAA,EAAAK,MAAA,CAAkC;IAAA,EAAC;IACtGjD,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,cAA8D;IAG5DD,EAFA,CAAAwB,UAAA,IAAA2B,0EAAA,kBAC6C,IAAAC,0EAAA,kBAC0E;IAGzHpD,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,cACgC;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChFH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAGhEF,EAHgE,CAAAG,YAAA,EAAQ,EAC9D,EACF,EACF;;;;;IAzBJH,EAAA,CAAA2B,WAAA,oBAAAhB,MAAA,CAAA0C,mBAAA,kBAAA1C,MAAA,CAAA0C,mBAAA,CAAAC,EAAA,MAAAV,SAAA,CAAAU,EAAA,CAA6D;IAYnDtD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAAgC,SAAA,EAAwB;IAExB5C,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAAgC,SAAA,EAAyB;IAOM5C,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAwC,SAAA,CAAA7B,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA4B,iBAAA,CAAAgB,SAAA,CAAA7B,IAAA,CAAgB;IAChDf,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA4B,iBAAA,CAAAjB,MAAA,CAAAkB,cAAA,CAAAe,SAAA,CAAAd,IAAA,EAAgC;;;;;IAMhE9B,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAU,SAAA,YAA6C;IAC7CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAtINH,EALN,CAAAC,cAAA,cAA2E,cAGf,cACY,aACjD;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAClDF,EADkD,CAAAG,YAAA,EAAQ,EACpD;IAGNH,EAAA,CAAAC,cAAA,cAA8C;IA6B5CD,EA5BA,CAAAwB,UAAA,IAAA+B,mEAAA,mBAC2E,IAAAC,mEAAA,kBA2BQ;IAIrFxD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAwB,UAAA,KAAAiC,oEAAA,oBAAuE;IA6BzEzD,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmF,kBACkC;IAA5ED,EAAA,CAAAgB,UAAA,mBAAA0C,uFAAA;MAAA1D,EAAA,CAAAkB,aAAA,CAAAyC,GAAA;MAAA,MAAAhD,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAiD,gBAAA,EAAkB;IAAA,EAAC;IAC7B5D,EAApC,CAAAU,SAAA,aAAoC,UAAI;IACxCV,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACXF,EADW,CAAAG,YAAA,EAAQ,EACV;IACTH,EAAA,CAAAC,cAAA,kBAAqH;IAAtED,EAAA,CAAAgB,UAAA,mBAAA6C,uFAAA;MAAA7D,EAAA,CAAAkB,aAAA,CAAAyC,GAAA;MAAA,MAAAhD,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAmD,YAAA,EAAc;IAAA,EAAC;IACjC9D,EAApC,CAAAU,SAAA,aAAoC,UAAI;IACxCV,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACXF,EADW,CAAAG,YAAA,EAAQ,EACV;IACTH,EAAA,CAAAC,cAAA,kBAAsH;IAArED,EAAA,CAAAgB,UAAA,mBAAA+C,uFAAA;MAAA/D,EAAA,CAAAkB,aAAA,CAAAyC,GAAA;MAAA,MAAAhD,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAqD,cAAA,EAAgB;IAAA,EAAC;IACtChE,EAAnC,CAAAU,SAAA,aAAmC,UAAI;IACvCV,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACXF,EADW,CAAAG,YAAA,EAAQ,EACV;IACTH,EAAA,CAAAC,cAAA,kBAA8G;IAArED,EAAA,CAAAgB,UAAA,mBAAAiD,uFAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAyC,GAAA;MAAA,MAAAhD,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAuD,WAAA,EAAa;IAAA,EAAC;IAC3BlE,EAAnC,CAAAU,SAAA,aAAmC,UAAI;IACvCV,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEdF,EAFc,CAAAG,YAAA,EAAQ,EACX,EACL;IAKFH,EAFJ,CAAAC,cAAA,eAA0D,eACY,cACjD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAC/DF,EAD+D,CAAAG,YAAA,EAAQ,EACjE;IAGNH,EAAA,CAAAC,cAAA,eAA8C;IA+B5CD,EA9BA,CAAAwB,UAAA,KAAA2C,oEAAA,mBAGyC,KAAAC,oEAAA,kBA2BwC;IAMvFpE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAxI0BH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,kBAAA,YAAAG,MAAA,CAAA0D,YAAA,YAAsB;IAKzBrE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA2D,oBAAA,CAAuB;IA4BxCtE,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA2D,oBAAA,CAAAC,MAAA,OAAuC;IAOzCvE,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA+B,UAAA,KAAoB;IAiC0C1C,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA2D,oBAAA,CAAAC,MAAA,OAA8C;IAI1CvE,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA6D,sBAAA,UAA4C;IAIxCxE,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0C,mBAAA,UAAyC;IAIpDrD,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,kBAAA,CAAAF,MAAA,OAA4C;IAUjFvE,EAAA,CAAAO,SAAA,IAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,YAAAG,MAAA,CAAA8D,kBAAA,CAAAF,MAAA,YAAmC;IAKtCvE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA8D,kBAAA,CAAuB;IA8BxCzE,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA8D,kBAAA,CAAAF,MAAA,OAAqC;;;;;;IAtKnDvE,EADF,CAAAC,cAAA,iBAA+E,qBAC7D;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,sBAA+F,aAGhD,aACvB,eACQ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,mBAC+C;IADpCD,EAAA,CAAA0E,gBAAA,4BAAAC,2FAAA1B,MAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAjE,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAA6E,kBAAA,CAAAlE,MAAA,CAAAmE,gBAAA,EAAA7B,MAAA,MAAAtC,MAAA,CAAAmE,gBAAA,GAAA7B,MAAA;MAAA,OAAAjD,EAAA,CAAAsB,WAAA,CAAA2B,MAAA;IAAA,EAA+B;IACxCjD,EAAA,CAAAgB,UAAA,4BAAA2D,2FAAA1B,MAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAjE,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAkBX,MAAA,CAAAoE,iBAAA,CAAA9B,MAAA,CAAyB;IAAA,EAAC;IAC5CjD,EAAA,CAAAwB,UAAA,IAAAwD,kEAAA,uBAAyE;IAI7EhF,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,iBACiD;IADeD,EAAA,CAAA0E,gBAAA,2BAAAO,uFAAAhC,MAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAjE,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAA6E,kBAAA,CAAAlE,MAAA,CAAAuE,UAAA,EAAAjC,MAAA,MAAAtC,MAAA,CAAAuE,UAAA,GAAAjC,MAAA;MAAA,OAAAjD,EAAA,CAAAsB,WAAA,CAAA2B,MAAA;IAAA,EAAwB;IACtFjD,EAAA,CAAAgB,UAAA,2BAAAiE,uFAAAhC,MAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAjE,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAiBX,MAAA,CAAAwE,kBAAA,CAAAlC,MAAA,CAA0B;IAAA,EAAC;IAElDjD,EAHI,CAAAG,YAAA,EACiD,EAC7C,EACF;IASNH,EANA,CAAAwB,UAAA,KAAA4D,6DAAA,kBAAgD,KAAAC,6DAAA,oBAM2B;IA+I7ErF,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,0BAAuD,kBACsB;IAAzCD,EAAA,CAAAgB,UAAA,mBAAAsE,gFAAA;MAAAtF,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAW,OAAA,GAAAvF,EAAA,CAAAqB,aAAA,GAAAmE,SAAA;MAAA,MAAA7E,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAASV,MAAA,CAAA8E,eAAA,EAAiB;MAAA,OAAAzF,EAAA,CAAAsB,WAAA,CAAEiE,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC1F,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtFH,EAAA,CAAAC,cAAA,kBAC+C;IADfD,EAAA,CAAAgB,UAAA,mBAAA2E,gFAAA;MAAA3F,EAAA,CAAAkB,aAAA,CAAA0D,GAAA;MAAA,MAAAW,OAAA,GAAAvF,EAAA,CAAAqB,aAAA,GAAAmE,SAAA;MAAA,MAAA7E,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAASV,MAAA,CAAAiF,gBAAA,EAAkB;MAAA,OAAA5F,EAAA,CAAAsB,WAAA,CAAEiE,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEvE1F,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;IApLNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAG,MAAA,CAAAkF,YAAA,gDACF;IAOiB7F,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAA8F,gBAAA,aAAAnF,MAAA,CAAAmE,gBAAA,CAA+B;IAEV9E,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAoF,eAAA,CAAkB;IAOc/F,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAA8F,gBAAA,YAAAnF,MAAA,CAAAuE,UAAA,CAAwB;IAMtFlF,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAqF,SAAA,CAAe;IAMfhG,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAqF,SAAA,CAAgB;IAmJpBhG,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,kBAAA,CAAAF,MAAA,OAA4C;IAC5CvE,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA8D,kBAAA,CAAAF,MAAA,OACF;;;;;IAqBAvE,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAGNH,EAAA,CAAAU,SAAA,cAGiD;;;;IAD/CV,EADA,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAsF,eAAA,GAAAjG,EAAA,CAAAc,aAAA,CAAoC,QAAAH,MAAA,CAAAsF,eAAA,CAAAlF,IAAA,CACR;;;;;IAI9Bf,EAAA,CAAAC,cAAA,cAAiH;IAC/GD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACdF,EADc,CAAAG,YAAA,EAAM,EACd;;;;;;IAQJH,EAAA,CAAAC,cAAA,iBAA2G;IAAnCD,EAAA,CAAAgB,UAAA,mBAAAkF,yFAAA;MAAAlG,EAAA,CAAAkB,aAAA,CAAAiF,IAAA;MAAA,MAAAxF,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAAyF,sBAAA,EAAwB;IAAA,EAAC;IACxGpG,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,MAAA,CAAAsF,eAAA,IAAAtF,MAAA,CAAA0F,eAAA,CAAA1F,MAAA,CAAAsF,eAAA,uEACF;;;;;;IAvCFjG,EAFJ,CAAAC,cAAA,kBAAmE,yBACS,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,iBACyF;IAA5BD,EAAA,CAAAgB,UAAA,mBAAAsF,+EAAA;MAAAtG,EAAA,CAAAkB,aAAA,CAAAqF,IAAA;MAAA,MAAA5F,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAA6F,eAAA,EAAiB;IAAA,EAAC;IAC9GxG,EAAA,CAAAU,SAAA,YAAmC;IAACV,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC0B;IAAxBD,EAAA,CAAAgB,UAAA,mBAAAyF,+EAAA;MAAAzG,EAAA,CAAAkB,aAAA,CAAAqF,IAAA;MAAA,MAAA5F,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASX,MAAA,CAAA+F,WAAA,EAAa;IAAA,EAAC;IACvB1G,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAU,SAAA,aAAoC;IAG9CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAkG;IAchGD,EAZA,CAAAwB,UAAA,KAAAmF,6DAAA,kBAA2C,KAAAC,6DAAA,kBASM,KAAAC,6DAAA,kBAGgE;IAInH7G,EAAA,CAAAG,YAAA,EAAe;IAGbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAwB,UAAA,KAAAsF,gEAAA,qBAA2G;IAG3G9G,EAAA,CAAAC,cAAA,kBAAuE;IAAjCD,EAAA,CAAAgB,UAAA,mBAAA+F,gFAAA;MAAA/G,EAAA,CAAAkB,aAAA,CAAAqF,IAAA;MAAA,MAAAhB,OAAA,GAAAvF,EAAA,CAAAqB,aAAA,GAAAmE,SAAA;MAAA,MAAA7E,MAAA,GAAAX,EAAA,CAAAqB,aAAA;MAASV,MAAA,CAAAqG,OAAA,EAAS;MAAA,OAAAhH,EAAA,CAAAsB,WAAA,CAAEiE,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC1F,EAAA,CAAAE,MAAA,oBAAE;IAG/EF,EAH+E,CAAAG,YAAA,EAAS,EAC9E,EACS,EACT;;;;IA3CAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAAsF,eAAA,kBAAAtF,MAAA,CAAAsF,eAAA,CAAAlF,IAAA,KAAkC;IAESf,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAsG,mBAAA,MAAqC;IAGrCjH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAsG,mBAAA,IAAAtG,MAAA,CAAAuG,MAAA,CAAA3C,MAAA,KAAqD;IAShGvE,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAqF,SAAA,CAAe;IAMfhG,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAqF,SAAA,IAAArF,MAAA,CAAAsF,eAAA,IAAAtF,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAsF,eAAA,EAAmE;IAMnEjG,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAqF,SAAA,MAAArF,MAAA,CAAAsF,eAAA,KAAAtF,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAsF,eAAA,GAAuE;IAQ3EjG,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAmH,kBAAA,MAAAxG,MAAA,CAAAsG,mBAAA,aAAAtG,MAAA,CAAAuG,MAAA,CAAA3C,MAAA,MACF;IAEWvE,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyG,mBAAA,CAAyB;;;;;IAvCxCpH,EAzLA,CAAAwB,UAAA,IAAA6F,sDAAA,sBAA+E,IAAAC,sDAAA,sBAyLZ;;;;IAzLzDtH,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA4G,oBAAA,CAA0B;IAyL1BvH,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAA4G,oBAAA,CAA2B;;;ADlKvC,OAAM,MAAOC,qBAAqB;EAqDhCC,YACUC,aAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAvDf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAX,mBAAmB,GAAY,IAAI;IAQ5C;IACS,KAAAG,oBAAoB,GAAY,KAAK;IAGpC,KAAAS,oBAAoB,GAAG,IAAIpI,YAAY,EAAa;IACpD,KAAA8F,KAAK,GAAG,IAAI9F,YAAY,EAAQ;IAChC,KAAAqI,aAAa,GAAG,IAAIrI,YAAY,EAAU;IAC1C,KAAAsI,SAAS,GAAG,IAAItI,YAAY,EAAU;IAEhD;IACU,KAAAuI,mBAAmB,GAAG,IAAIvI,YAAY,EAAe;IACrD,KAAAwI,cAAc,GAAG,IAAIxI,YAAY,EAAU;IAIrD;IACA,KAAAsH,MAAM,GAAgB,EAAE;IACxB,KAAAmB,eAAe,GAAgB,EAAE;IACjC,KAAApC,eAAe,GAAqB,IAAI;IACxC,KAAAgB,mBAAmB,GAAW,CAAC;IAC/B,KAAAjB,SAAS,GAAY,KAAK;IAE1B;IACA,KAAA7D,WAAW,GAAW,CAAC;IACvB,KAAAmG,QAAQ,GAAW,CAAC,CAAC,CAAC;IACtB,KAAAjE,YAAY,GAAW,CAAC;IACxB,KAAA3B,UAAU,GAAW,CAAC;IACtB,KAAA4B,oBAAoB,GAAgB,EAAE;IAEtC;IACA,KAAAyB,eAAe,GAAG,CAChB;MAAEzF,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IACD,KAAAqE,gBAAgB,GAAW,CAAC;IAC5B,KAAAL,kBAAkB,GAAgB,EAAE,CAAC,CAAC;IAEtC;IACA,KAAAD,sBAAsB,GAAqB,IAAI;IAC/C,KAAAnB,mBAAmB,GAAqB,IAAI;EAMxC;EAEJkF,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MACzE,IAAI,CAACL,UAAU,EAAE;IACnB;EACF;EAEA;EACAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAAC3C,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC8C,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;MACrB,IAAI,CAACL,eAAe,GAAG,EAAE;MACzB;IACF;IAEA,IAAI,CAACV,cAAc,CAACqB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9BS,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,IAAI,CAACjH,WAAW;QAC3BkH,QAAQ,EAAE,IAAI,CAACf,QAAQ;QACvBgB,KAAK,EAAE,IAAI,CAACpE,UAAU,IAAI2D;;KAE7B,CAAC,CAACU,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YACvExG,EAAE,EAAEwG,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBhJ,IAAI,EAAE+I,OAAO,CAACE,YAAY,IAAI,EAAE;YAChClI,IAAI,EAAE,CAAC;YACPmI,YAAY,EAAEH,OAAO,CAACI,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEL,OAAO,CAACI,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEN,OAAO,CAACO,SAAS,GAAG,IAAIC,IAAI,CAACR,OAAO,CAACO,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAET,OAAO,CAACU;WACf,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACnG,YAAY,GAAGoF,GAAG,CAACgB,UAAU,IAAI,CAAC;UACvC,IAAI,CAAC/H,UAAU,GAAGgI,IAAI,CAACC,IAAI,CAAC,IAAI,CAACtG,YAAY,GAAG,IAAI,CAACiE,QAAQ,CAAC;UAE9D;UACA,MAAMsC,WAAW,GAAG,IAAI,CAAC9C,cAAc,CAAC+B,GAAG,CAACgB,GAAG,IAAIA,GAAG,CAACvH,EAAE,CAAC;UAC1D,IAAI,CAAC+E,eAAe,GAAGsB,SAAS,CAACmB,MAAM,CAACC,KAAK,IAAI,CAACH,WAAW,CAACI,QAAQ,CAACD,KAAK,CAACzH,EAAE,CAAC,CAAC;UAEjF;UACA,IAAI,CAAC2H,iBAAiB,EAAE;UAExB;UACA,IAAI,CAAC/D,MAAM,GAAG,CAAC,GAAG,IAAI,CAACmB,eAAe,EAAE,GAAG,IAAI,CAACP,cAAc,CAAC;UAE/D;UACA,IAAI,CAACoD,sBAAsB,EAAE;QAC/B,CAAC,MAAM;UACL,IAAI,CAACtD,cAAc,CAACuD,YAAY,CAAC1B,GAAG,CAAC2B,OAAO,IAAI,QAAQ,CAAC;QAC3D;QACA,IAAI,CAACpF,SAAS,GAAG,KAAK;MACxB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,UAAU,IAAIE,KAAK,CAACC,OAAO,IAAI,MAAM,CAAC,CAAC;QACxE,IAAI,CAACtF,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACA+C,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACL,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAAChB,cAAc,CAACqB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9B6C,WAAW,EAAE,IAAI,CAAC5C,UAAU;QAC5BQ,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACE,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC5B,cAAc,GAAG2B,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YAC3ExG,EAAE,EAAEwG,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBhJ,IAAI,EAAE+I,OAAO,CAACE,YAAY,IAAI,EAAE;YAChClI,IAAI,EAAE,CAAC;YACPmI,YAAY,EAAEH,OAAO,CAACI,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEL,OAAO,CAACI,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEN,OAAO,CAACO,SAAS,GAAG,IAAIC,IAAI,CAACR,OAAO,CAACO,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAET,OAAO,CAACU;WACf,CAAC,CAAC,IAAI,EAAE;QACX;MACF,CAAC;MACDa,KAAK,EAAGA,KAAK,IAAI;QACfG,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACAH,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAChE,MAAM,CAAC3C,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC0C,mBAAmB,GAAGyD,IAAI,CAACe,GAAG,CAAC,CAAC,EAAEf,IAAI,CAACgB,GAAG,CAAC,IAAI,CAAC3D,iBAAiB,EAAE,IAAI,CAACb,MAAM,CAAC3C,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAAC0B,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAChB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgB,mBAAmB,GAAG,CAAC;IAC9B;EACF;EAEAT,eAAeA,CAAA;IACb,IAAI,IAAI,CAACS,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACgB,aAAa,CAAC0D,IAAI,CAAC,IAAI,CAAC1E,mBAAmB,CAAC;IACnD;EACF;EAEAP,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAAC3C,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAAC0C,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACiB,SAAS,CAACyD,IAAI,CAAC,IAAI,CAAC1E,mBAAmB,CAAC;IAC/C;EACF;EAEAb,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACH,eAAe,EAAE;MACxB,IAAI,CAAC+B,oBAAoB,CAAC2D,IAAI,CAAC,IAAI,CAAC1F,eAAe,CAAC;IACtD;EACF;EAEAI,eAAeA,CAAC0E,KAAgB;IAC9B,OAAO,IAAI,CAACjD,cAAc,CAAC8D,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACvI,EAAE,KAAKyH,KAAK,CAACzH,EAAE,CAAC;EACvE;EAEA0D,OAAOA,CAAA;IACL,IAAI,CAACtB,KAAK,CAACiG,IAAI,EAAE;EACnB;EAEA;EACAG,WAAWA,CAACC,eAAkC;IAC5C;IACA,IAAI,IAAI,CAAC7E,MAAM,CAAC3C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACiE,UAAU,EAAE;IACnB;IAEA;IACA,MAAMwD,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;IACrD,IAAI,CAACvE,aAAa,CAACwE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAG,gBAAgBA,CAAC5B,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAC3C,cAAc,CAACuD,YAAY,CAAC,cAAc,CAAC;MAChD;IACF;IAEA,IAAI,CAACxD,cAAc,CAACyE,2BAA2B,CAAC;MAAE7B,IAAI,EAAEA;IAAI,CAAE,CAAC,CAC5DhB,SAAS,CAAC;MACTC,IAAI,EAAG6C,QAAQ,IAAI;QACjB;QACAb,OAAO,CAACc,GAAG,CAAC,SAAS,EAAED,QAAQ,CAAC;MAClC,CAAC;MACDhB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,WAAWE,KAAK,CAACC,OAAO,IAAI,MAAM,EAAE,CAAC;MACxE;KACD,CAAC;EACN;EAEA;EACA1K,WAAWA,CAAC2L,SAAoB;IAC9B,IAAIA,SAAS,CAAChC,IAAI,IAAI,CAACgC,SAAS,CAACpC,OAAO,EAAE;MACxC;MACA,IAAI,CAACgC,gBAAgB,CAACI,SAAS,CAAChC,IAAI,CAAC;IACvC;IAEA,OAAOgC,SAAS,CAACpC,OAAO,IAAIoC,SAAS,CAACtC,YAAY,IAAI,EAAE;EAC1D;EAEA;EACAlF,iBAAiBA,CAACyH,QAAgB;IAChC,IAAI,CAAC1H,gBAAgB,GAAG0H,QAAQ;IAChC,IAAI,CAACpE,cAAc,CAACuD,IAAI,CAACa,QAAQ,CAAC;IAClC,IAAI,IAAI,CAAC9D,WAAW,EAAE;MACpB,IAAI,CAACF,UAAU,EAAE;IACnB;EACF;EAEA;EACAiE,oBAAoBA,CAACC,eAAkC;IACrD,IAAI,CAACnF,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC9C,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACqD,cAAc,CAAC;IAElD,IAAI,IAAI,CAACZ,MAAM,CAAC3C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACiE,UAAU,EAAE;IACnB;IAEA,MAAMwD,QAAQ,GAAGU,eAAe,IAAI,IAAI,CAACT,YAAY;IACrD,IAAI,CAACvE,aAAa,CAACwE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACApG,gBAAgBA,CAAA;IACd,IAAI,CAACuC,mBAAmB,CAACwD,IAAI,CAAC,IAAI,CAAClH,kBAAkB,CAAC;IACtD,IAAI,CAAC8C,oBAAoB,GAAG,KAAK;EACnC;EAEA;EACA9B,eAAeA,CAAA;IACb,IAAI,CAAChB,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC8C,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACP,OAAO,EAAE;EAChB;EAEA;EACA2F,oBAAoBA,CAAC5B,KAAgB;IACnC,MAAM6B,KAAK,GAAG,IAAI,CAACnI,kBAAkB,CAACoI,SAAS,CAAChC,GAAG,IAAIA,GAAG,CAACvH,EAAE,KAAKyH,KAAK,CAACzH,EAAE,CAAC;IAC3E,IAAIsJ,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACnI,kBAAkB,CAACqI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACnI,kBAAkB,CAACsI,IAAI,CAAChC,KAAK,CAAC;IACrC;EACF;EAEA;EACAiC,mBAAmBA,CAACjC,KAAgB;IAClC,OAAO,IAAI,CAACtG,kBAAkB,CAACmH,IAAI,CAACf,GAAG,IAAIA,GAAG,CAACvH,EAAE,KAAKyH,KAAK,CAACzH,EAAE,CAAC;EACjE;;;uCA7SWkE,qBAAqB,EAAAxH,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAnN,EAAA,CAAAiN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArN,EAAA,CAAAiN,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArB/F,qBAAqB;MAAAgG,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCzBlC3N,EAAA,CAAAwB,UAAA,IAAAqM,4CAAA,gCAAA7N,EAAA,CAAA8N,sBAAA,CAA0D;;;qBDuB9CjO,YAAY,EAAAkO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElO,YAAY,EAAAmO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAnB,EAAA,CAAAoB,eAAA,EAAApB,EAAA,CAAAqB,mBAAA,EAAArB,EAAA,CAAAsB,qBAAA,EAAAtB,EAAA,CAAAuB,qBAAA,EAAAvB,EAAA,CAAAwB,iBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAE7O,eAAe,EAAAoN,EAAA,CAAA0B,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}