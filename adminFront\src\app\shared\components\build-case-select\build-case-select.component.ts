import { Component, EventEmitter, Input, OnInit, Output, OnDestroy, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { NbSelectModule, NbOptionModule } from '@nebular/theme';
import { NgFor, NgIf } from '@angular/common';
import { BuildCaseService } from 'src/services/api/services';
import { BuildCaseGetListReponse } from 'src/services/api/models';

@Component({
  selector: 'app-build-case-select',
  standalone: true,
  imports: [
    NbSelectModule,
    NbOptionModule,
    FormsModule,
    NgFor,
    NgIf
  ],
  templateUrl: './build-case-select.component.html',
  styleUrls: ['./build-case-select.component.scss']
})
export class BuildCaseSelectComponent implements OnInit {

  @Input() selectedValue: number | null | undefined = null;
  @Input() placeholder: string = '請選擇建案';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() cssClass: string = '';
  private readonly storageKey: string = 'selectedBuildCase'; // localStorage key

  @Output() selectedValueChange = new EventEmitter<number | null | undefined>();
  @Output() valueChange = new EventEmitter<number | null | undefined>();
  @Output() selectionChange = new EventEmitter<BuildCaseGetListReponse | null>();
  @Output() buildCaseListLoaded = new EventEmitter<BuildCaseGetListReponse[]>();

  buildCaseList: BuildCaseGetListReponse[] = [];
  loading: boolean = false;

  private destroyRef = inject(DestroyRef);

  constructor(
    private buildCaseService: BuildCaseService
  ) { }

  ngOnInit(): void {
    console.log('BuildCaseSelectComponent ngOnInit - 初始selectedValue:', this.selectedValue);
    // 優先載入記憶的值，只有在沒有記憶值時才使用傳入的值
    const storedValue = this.getStoredValue();
    if (storedValue !== null) {
      this.selectedValue = storedValue;
      console.log('載入localStorage後的selectedValue:', this.selectedValue);
    }
    this.loadBuildCaseList();
  }

  private loadBuildCaseList(): void {
    this.loading = true;
    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (res) => {
          if (res.Entries) {
            this.buildCaseList = res.Entries;
            this.buildCaseListLoaded.emit(this.buildCaseList);

            // 驗證localStorage載入的值是否有效
            if (this.selectedValue !== null && this.selectedValue !== undefined) {
              const foundBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);
              if (!foundBuildCase) {
                // 如果localStorage中的值在建案列表中不存在，重設為null但保留localStorage記錄
                console.log('localStorage中的建案ID在列表中不存在，重設為null:', this.selectedValue);
                this.selectedValue = null;
              } else {
                console.log('驗證通過，localStorage中的建案ID有效:', this.selectedValue);
                // 重新設定選中值以觸發UI更新並發送事件
                const currentValue = this.selectedValue;
                const selectedBuildCase = foundBuildCase;
                this.selectedValue = null;
                setTimeout(() => {
                  this.selectedValue = currentValue;
                  this.selectedValueChange.emit(this.selectedValue);
                  this.valueChange.emit(this.selectedValue);
                  this.selectionChange.emit(selectedBuildCase);
                }, 0);
              }
            }

            // 如果沒有選中值且有建案資料，自動選擇第一個
            if ((this.selectedValue === null || this.selectedValue === undefined) && this.buildCaseList.length > 0) {
              this.selectedValue = this.buildCaseList[0].cID!;
              this.selectedValueChange.emit(this.selectedValue);
              this.valueChange.emit(this.selectedValue);
              this.selectionChange.emit(this.buildCaseList[0]);

              // 檢查localStorage是否有記憶值，如果有但無效，則更新為當前選擇
              const storedValue = this.getStoredValue();
              if (storedValue !== null) {
                // 有記憶值但無效，更新為當前有效選擇
                this.saveToLocalStorage(this.selectedValue);
                console.log('更新無效的localStorage記憶值為:', this.selectedValue);
              }
              // 如果從未有記憶值，則不保存（等用戶手動選擇才開始記憶）
            } else if (this.selectedValue !== null && this.selectedValue !== undefined) {
              // 如果已有選中值，觸發完整事件通知
              const selectedBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);
              if (selectedBuildCase) {
                this.selectedValueChange.emit(this.selectedValue);
                this.valueChange.emit(this.selectedValue);
                this.selectionChange.emit(selectedBuildCase);
              }
            }
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('載入建案列表失敗:', error);
          this.loading = false;
        }
      });
  }

  onSelectionChange(value: number | null | undefined): void {
    this.selectedValue = value;
    this.selectedValueChange.emit(value);
    this.valueChange.emit(value);

    // 儲存到localStorage
    this.saveToLocalStorage(value);

    if (value !== null && value !== undefined) {
      const selectedBuildCase = this.buildCaseList.find(item => item.cID === value);
      this.selectionChange.emit(selectedBuildCase || null);
    } else {
      this.selectionChange.emit(null);
    }
  }

  // 取得選中的建案資料
  getSelectedBuildCase(): BuildCaseGetListReponse | null {
    if (this.selectedValue === null || this.selectedValue === undefined) {
      return null;
    }
    return this.buildCaseList.find(item => item.cID === this.selectedValue) || null;
  }

  // 重新載入建案列表
  reload(): void {
    this.loadBuildCaseList();
  }

  // 獲取localStorage中儲存的值
  private getStoredValue(): number | null {
    try {
      const storedValue = localStorage.getItem(this.storageKey);
      console.log('從localStorage讀取值:', storedValue);
      if (storedValue !== null) {
        const parsedValue = parseInt(storedValue, 10);
        if (!isNaN(parsedValue)) {
          console.log('成功從localStorage載入建案選擇:', parsedValue);
          return parsedValue;
        }
      } else {
        console.log('localStorage中沒有儲存的建案選擇');
      }
    } catch (error) {
      console.warn('無法從localStorage載入建案選擇:', error);
    }
    return null;
  }

  // 儲存選擇的建案到localStorage
  private saveToLocalStorage(value: number | null | undefined): void {
    try {
      if (value !== null && value !== undefined) {
        localStorage.setItem(this.storageKey, value.toString());
        console.log('儲存建案選擇到localStorage:', value);
      } else {
        localStorage.removeItem(this.storageKey);
        console.log('清除localStorage中的建案選擇');
      }
    } catch (error) {
      console.warn('無法儲存建案選擇到localStorage:', error);
    }
  }

  // 清除localStorage中的選擇
  clearStoredSelection(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('無法清除localStorage中的建案選擇:', error);
    }
  }
}
