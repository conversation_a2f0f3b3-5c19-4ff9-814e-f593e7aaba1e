<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]"> 可設定單筆或批次匯入設定各區域及方案對應之建材。
    </h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建案</label>
          <div class="w-[78%]">
            <app-build-case-select [(selectedValue)]="selectedBuildCaseId"
              (selectionChange)="onBuildCaseSelectionChange($event)" placeholder="請選擇建案" class="w-full">
            </app-build-case-select>
          </div>
        </div>
      </div>
      <!-- <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2  w-[22%]">建材類別</label>
          <nb-select placeholder="建材類別" [(ngModel)]="materialOptionsId" class="w-full">
            <nb-option *ngFor="let option of materialOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
      </div> -->
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材選項名稱 </label>
          <input type="text" nbInput placeholder="建材選項名稱" [(ngModel)]="CSelectName" class="w-full" maxlength="50">
        </div>
      </div>
      <!-- 啟用建材代號欄位 -->
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材代號</label>
          <input type="text" nbInput placeholder="建材代號" [(ngModel)]="CMaterialCode" class="w-full" maxlength="20">
        </div>
      </div>
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full">
          <nb-checkbox status="basic" class="flex" style="flex:auto" [(checked)]="filterMapping"
            (change)="changeFilter()">
            只顯示缺少建材圖片或示意圖片的建材
          </nb-checkbox>
          <button *ngIf="isExcelExport" class="btn btn-success mr-2" (click)="exportExelMaterialList()">匯出 <i
              class="fas fa-file-download"></i></button>
          <button *ngIf="isRead" class="btn btn-info mr-2 text-white ml-2" (click)="search()">
            查詢 <i class="fas fa-search"></i></button>
          <button *ngIf="isCreate" class="btn btn-info mx-1 ml-2 mr-2" (click)="addNew(dialog)">單筆新增 <i
              class="fas fa-plus"></i></button>
          <button class="btn btn-info mx-1" *ngIf="isExcelImport" (click)="inputFile.click()"> 批次匯入 </button>
          <input class="hidden" type="file" accept=".xls, .xlsx" #inputFile (change)="detectFileExcel($event)">
          <button class="btn btn-success ml-2" (click)="exportExelMaterialTemplate()">下載範例檔案 <i
              class="fas fa-file-download"></i></button>
        </div>
      </div>
    </div>
    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="min-width: 1200px; background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <th scope="col" class="col-1">項次</th>
            <th scope="col" class="col-1">建材代號</th>
            <th scope="col" class="col-1">選項名稱</th>
            <th scope="col" class="col-3">建材說明</th>
            <th scope="col" class="col-1">已綁定圖片</th>
            <th scope="col" class="col-1">價格</th>
            <th scope="col" class="col-1">狀態</th>
            <th scope="col" class="col-1">操作</th>
          </tr>
        </thead>
        <tbody *ngIf="materialList != null && materialList.length > 0">
          <tr *ngFor="let item of materialList ; let i = index">
            <td>{{ item.CId}}</td>
            <td>{{ item.CMaterialCode || '待設定' }}</td>
            <td [style]="!item.CIsMapping ? 'color: red' : ''">{{ item.CSelectName}}</td>
            <td>{{ item.CDescription}}</td>
            <td>
              <div class="d-flex align-items-center">
                <span *ngIf="item.CSelectPictureId && item.CSelectPictureId.length > 0"
                  class="badge badge-success mr-2">{{ item.CSelectPictureId.length }}</span>
                <span *ngIf="!item.CSelectPictureId || item.CSelectPictureId.length === 0"
                  class="badge badge-danger mr-2">0</span>
                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>
              </div>
            </td>
            <td>{{ item.CPrice}}</td>
            <td>
              <span class="badge" [class]="item.CStatus === 1 ? 'badge-success' : 'badge-secondary'">
                {{ getStatusLabel(item.CStatus || 0) }}
              </span>
            </td>
            <td class="w-32">
              <button class="btn btn-outline-primary btn-sm m-1" (click)="onSelectedMaterial(item, dialog)"
                *ngIf="isRead">編輯</button> <button class="btn btn-outline-info btn-sm m-1"
                (click)="bindImageForMaterial(item)" *ngIf="isRead" [title]="'為 ' + item.CSelectName + ' 綁定圖片'">
                <i class="fas fa-images"></i> 綁定
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(CollectionSize)]="totalRecords" [(PageSize)]="pageSize" [(Page)]="pageIndex"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<ng-template #dialog let-dialog let-ref="dialogRef">
  <nb-card class="w-[700px]">
    <nb-card-header>
      建材管理 > 新增建材
    </nb-card-header>
    <nb-card-body class="px-4">
      <h5 class="text-base">請輸入下方內容新增建材。</h5>
      <div class="w-full mt-3">
        <!-- 啟用建材代號欄位 -->
        <div class="flex items-center">
          <label class="required-field w-[150px]">建材代號</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="20"
            [(ngModel)]="selectedMaterial.CMaterialCode" />
        </div>
        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">建材選項名稱</label>
          <input type="text" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" maxlength="50"
            [(ngModel)]="selectedMaterial.CSelectName" />
        </div>

        <div class="flex items-center mt-3">
          <label class="w-[150px]">建材說明</label>
          <textarea nbInput [(ngModel)]="selectedMaterial.CDescription" [rows]="4"
            class="resize-none w-full !max-w-full p-2 rounded text-[13px]"></textarea>
        </div>
        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">價格</label>
          <input type="number" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" min="0" step="0.01"
            [(ngModel)]="selectedMaterial.CPrice" placeholder="0" required />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">狀態</label>
          <nb-select [(ngModel)]="selectedMaterial.CStatus" class="w-full">
            <nb-option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>

        <!-- 圖片綁定按鈕 -->
        <div class="flex items-center mt-4 pt-3 border-t border-gray-200">
          <label class="w-[150px]">圖片綁定</label>
          <div class="flex gap-2 w-full">
            <button type="button" class="btn btn-outline-info btn-sm" (click)="openImageBinder()" [title]="'為建材綁定圖片'">
              <i class="fas fa-images mr-2"></i>選擇圖片
            </button>
            <div class="text-sm text-gray-600 flex items-center" *ngIf="selectedMaterial.CMaterialCode">
              <i class="fas fa-check-circle text-green-500 mr-2"></i>
              已設定建材代號: {{ selectedMaterial.CMaterialCode }}
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-danger btn-sm mr-4" (click)="onClose(ref)">關閉</button>
      <button class="btn btn-success btn-sm" (click)="onSubmit(ref)">儲存</button>
    </nb-card-footer>
  </nb-card>
</ng-template>


<!-- 圖片預覽元件 -->
<app-image-preview #imagePreviewComponent [buildCaseId]="selectedBuildCaseId" [materialId]="selectedMaterial?.CId"
  [pictureType]="selectedCategory" [materialName]="selectedMaterial?.CSelectName || undefined"
  [showBindingInterface]="true" [showSelectionToggle]="true" (confirmImageBinding)="onConfirmImageBinding($event)"
  (categoryChange)="categoryChanged($event)">
</app-image-preview>