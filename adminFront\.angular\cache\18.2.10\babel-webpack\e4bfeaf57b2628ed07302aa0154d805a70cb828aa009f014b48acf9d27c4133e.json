{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbSpinnerModule } from '@nebular/theme';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"nb-spinner\", 19);\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 35);\n  }\n  if (rf & 2) {\n    const image_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r5), i0.ɵɵsanitizeUrl)(\"alt\", image_r5.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_img_3_Template, 1, 2, \"img\", 28)(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_div_4_Template, 4, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30)(6, \"div\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_Template_button_click_9_listener() {\n      const image_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleImageSelection(image_r5));\n    });\n    i0.ɵɵelement(10, \"i\", 34);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const image_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"border-primary\", ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r1.isImageTempSelected(image_r5))(\"btn-outline-primary\", !ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-check\", ctx_r1.isImageTempSelected(image_r5))(\"fa-plus\", !ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isImageTempSelected(image_r5) ? \"\\u5DF2\\u9078\" : \"\\u9078\\u53D6\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵtemplate(2, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_Template, 12, 15, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template, 4, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableImages.length === 0);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"label\", 7);\n    i0.ɵɵtext(7, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-select\", 8);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChanged($event));\n    });\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"label\", 7);\n    i0.ɵɵtext(12, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(14, ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template, 4, 0, \"div\", 11)(15, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template, 4, 2, \"div\", 12);\n    i0.ɵɵelementStart(16, \"div\", 13);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"nb-card-footer\", 14)(19, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r6 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onCancelBinding();\n      return i0.ɵɵresetView(ref_r6.close());\n    });\n    i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r6 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onConfirmBinding();\n      return i0.ɵɵresetView(ref_r6.close());\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r1.materialName || \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7 \", ctx_r1.tempSelectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.tempSelectedImages.length, \") \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"nb-spinner\", 19);\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(ctx_r1.previewingImage), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u53EF\\u9810\\u89BD\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 41)(1, \"nb-card-header\", 42)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 45);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 47);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template, 4, 0, \"div\", 48)(13, ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template, 1, 2, \"img\", 49)(14, ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template, 4, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 42)(16, \"div\", 51);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 43);\n    i0.ɵɵtemplate(19, ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template, 2, 1, \"button\", 52);\n    i0.ɵɵelementStart(20, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ref_r6 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r6.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.previewingImage && ctx_r1.getImageUrl(ctx_r1.previewingImage));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && (!ctx_r1.previewingImage || !ctx_r1.getImageUrl(ctx_r1.previewingImage)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_nb_card_0_Template, 23, 9, \"nb-card\", 1)(1, ImagePreviewComponent_ng_template_0_nb_card_1_Template, 22, 9, \"nb-card\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBindingInterface);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showBindingInterface);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService, pictureService, messageService) {\n    this.dialogService = dialogService;\n    this.pictureService = pictureService;\n    this.messageService = messageService;\n    this.isVisible = false;\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    // 新增綁定功能相關輸入參數\n    this.showBindingInterface = false;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    // 新增綁定功能相關輸出事件\n    this.confirmImageBinding = new EventEmitter();\n    this.categoryChange = new EventEmitter();\n    // 內部屬性，不再依賴外部傳入\n    this.images = [];\n    this.availableImages = [];\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    this.isLoading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalRecords = 0;\n    // 綁定模式相關屬性\n    this.categoryOptions = [{\n      value: 1,\n      label: '建材圖片'\n    }, {\n      value: 2,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = 1;\n    this.tempSelectedImages = []; // 臨時選擇的圖片\n  }\n  ngOnInit() {\n    this.loadImages();\n  }\n  ngOnChanges() {\n    // 當輸入參數變化時重新載入圖片\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\n      this.loadImages();\n    }\n  }\n  // 載入圖片的主要方法\n  loadImages() {\n    if (!this.buildCaseId && !this.materialId) {\n      return;\n    }\n    this.isLoading = true;\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n  }\n  // 載入可選擇的圖片\n  loadAvailableImages() {\n    if (!this.buildCaseId) {\n      this.availableImages = [];\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        cPictureType: this.pictureType,\n        PageIndex: this.currentPage,\n        PageSize: this.pageSize,\n        CName: this.searchTerm || undefined\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          // 轉換 API 回應為 ImageItem 格式\n          const newImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n          this.totalRecords = res.TotalItems || 0;\n          // 排除已選擇的圖片\n          const selectedIds = this.selectedImages.map(img => img.id);\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\n          // 合併可選和已選圖片作為完整圖片列表\n          this.images = [...this.availableImages, ...this.selectedImages];\n          // 設定初始預覽圖片\n          this.setInitialPreviewImage();\n        } else {\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\n        this.isLoading = false;\n      }\n    });\n  }\n  // 載入已選擇的圖片（如果有 materialId）\n  loadSelectedImages() {\n    if (!this.buildCaseId || !this.materialId) {\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CMaterialId: this.materialId,\n        cPictureType: this.pictureType,\n        PageIndex: 1,\n        PageSize: 999 // 載入所有已選圖片\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n        }\n      },\n      error: error => {\n        console.error('載入已選擇圖片失敗:', error);\n      }\n    });\n  }\n  // 設定初始預覽圖片\n  setInitialPreviewImage() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    } else {\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    // 如果還沒有圖片，先載入圖片\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    // 開啟對話框\n    const template = imagePreviewRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 使用 CGuid 取得圖片資料的方法\n  getPictureByGuid(guid) {\n    if (!guid) {\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\n      return;\n    }\n    this.pictureService.apiPictureGetPictureGuidGet({\n      guid: guid\n    }).subscribe({\n      next: response => {\n        // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\n        console.log('取得圖片資料:', response);\n      },\n      error: error => {\n        this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\n      }\n    });\n  }\n  // 獲取圖片 URL，優先使用 CGuid\n  getImageUrl(imageItem) {\n    if (imageItem.guid && !imageItem.fullUrl) {\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\n      this.getPictureByGuid(imageItem.guid);\n    }\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\n  }\n  // 綁定模式相關方法\n  onCategoryChanged(category) {\n    this.selectedCategory = category;\n    this.categoryChange.emit(category);\n    if (this.buildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 開啟綁定界面\n  openBindingInterface(imageBindingRef) {\n    this.showBindingInterface = true;\n    this.tempSelectedImages = [...this.selectedImages];\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    const template = imageBindingRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 確認圖片綁定\n  onConfirmBinding() {\n    this.confirmImageBinding.emit(this.tempSelectedImages);\n    this.showBindingInterface = false;\n  }\n  // 取消綁定\n  onCancelBinding() {\n    this.tempSelectedImages = [];\n    this.showBindingInterface = false;\n    this.onClose();\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n    } else {\n      this.tempSelectedImages.push(image);\n    }\n  }\n  // 檢查圖片是否被選中\n  isImageTempSelected(image) {\n    return this.tempSelectedImages.some(img => img.id === image.id);\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.PictureService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\",\n        buildCaseId: \"buildCaseId\",\n        materialId: \"materialId\",\n        pictureType: \"pictureType\",\n        searchTerm: \"searchTerm\",\n        showBindingInterface: \"showBindingInterface\",\n        materialName: \"materialName\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\",\n        confirmImageBinding: \"confirmImageBinding\",\n        categoryChange: \"categoryChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [\"class\", \"w-[90vw] max-w-[1000px] h-[80vh]\", 4, \"ngIf\"], [\"class\", \"w-[800px] h-[600px]\", 4, \"ngIf\"], [1, \"w-[90vw]\", \"max-w-[1000px]\", \"h-[80vh]\"], [1, \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\"], [1, \"d-flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [1, \"form-label\"], [\"placeholder\", \"\\u9078\\u64C7\\u5716\\u7247\\u985E\\u5225\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"flex-1\", \"style\", \"overflow-y: auto;\", 4, \"ngIf\"], [1, \"flex-shrink-0\", \"mt-3\", \"text-center\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"text-center\", \"py-4\"], [\"size\", \"large\"], [1, \"mt-2\"], [1, \"flex-1\", 2, \"overflow-y\", \"auto\"], [1, \"row\"], [\"class\", \"col-md-3 col-sm-4 col-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-5 text-muted\", 4, \"ngIf\"], [1, \"col-md-3\", \"col-sm-4\", \"col-6\", \"mb-3\"], [1, \"card\", \"h-100\"], [1, \"card-img-top\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"120px\", \"background-color\", \"#f8f9fa\"], [\"class\", \"img-fluid\", \"style\", \"max-height: 100%; object-fit: contain;\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-muted text-center\", 4, \"ngIf\"], [1, \"card-body\", \"p-2\"], [1, \"small\", \"text-truncate\", 3, \"title\"], [1, \"text-center\", \"mt-2\"], [1, \"btn\", \"btn-sm\", 3, \"click\"], [1, \"fas\"], [1, \"img-fluid\", 2, \"max-height\", \"100%\", \"object-fit\", \"contain\", 3, \"src\", \"alt\"], [1, \"text-muted\", \"text-center\"], [1, \"fas\", \"fa-image\", \"fa-2x\", \"mb-2\"], [1, \"small\"], [1, \"text-center\", \"py-5\", \"text-muted\"], [1, \"fas\", \"fa-images\", \"fa-3x\", \"mb-3\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-center\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbSelectComponent, i1.NbOptionComponent, NbSpinnerModule, i1.NbSpinnerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImltYWdlLXByZXZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsZ0JBQWdCO0FBQWhCLGFBQUE7QUFFRTtFQUNFLGVBQUE7QUFDSjtBQUVFO0VBQ0UsZ0JBQUE7QUFBSjtBQUdFO0VBQ0UsbUJBQUE7QUFESjtBQUlFO0VBQ0UsY0FBQTtBQUZKO0FBS0U7RUFDRSxjQUFBO0FBSEo7QUFNRTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7QUFKSjtBQU9FO0VBQ0Usc0JBQUE7QUFMSjtBQVFFO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtBQU5KOztBQVVBLG9CQUFBO0FBRUU7RUFDRSxhQUFBO0FBUko7QUFXRTtFQUNFLDhCQUFBO0FBVEo7QUFZRTtFQUNFLHVCQUFBO0FBVko7QUFhRTtFQUNFLG1CQUFBO0FBWEo7QUFjRTtFQUNFLG1CQUFBO0FBWko7QUFlRTtFQUNFLFVBQUE7QUFiSiIsImZpbGUiOiJpbWFnZS1wcmV2aWV3LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyog5ZyW54mH6aCQ6Ka95YWD5Lu25qij5byPICovXHJcbi5pbWFnZS1wcmV2aWV3LWNvbnRhaW5lciB7XHJcbiAgLm1heC13LWZ1bGwge1xyXG4gICAgbWF4LXdpZHRoOiAxMDAlO1xyXG4gIH1cclxuICBcclxuICAubWF4LWgtZnVsbCB7XHJcbiAgICBtYXgtaGVpZ2h0OiAxMDAlO1xyXG4gIH1cclxuICBcclxuICAub2JqZWN0LWNvbnRhaW4ge1xyXG4gICAgb2JqZWN0LWZpdDogY29udGFpbjtcclxuICB9XHJcbiAgXHJcbiAgLnRleHQtZ3JheS00MDAge1xyXG4gICAgY29sb3I6ICM5Y2EzYWY7XHJcbiAgfVxyXG4gIFxyXG4gIC50ZXh0LWdyYXktNjAwIHtcclxuICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gIH1cclxuICBcclxuICAudGV4dC00eGwge1xyXG4gICAgZm9udC1zaXplOiAyLjI1cmVtO1xyXG4gICAgbGluZS1oZWlnaHQ6IDIuNXJlbTtcclxuICB9XHJcbiAgXHJcbiAgLm1iLTMge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuICB9XHJcbiAgXHJcbiAgLnRleHQtc20ge1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xyXG4gIH1cclxufVxyXG5cclxuLyog6KaG6JOLIE5lYnVsYXIg6aCQ6Kit5qij5byPICovXHJcbm5iLWNhcmQge1xyXG4gIC5kLWZsZXgge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICB9XHJcbiAgXHJcbiAgLmp1c3RpZnktY29udGVudC1iZXR3ZWVuIHtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICB9XHJcbiAgXHJcbiAgLmp1c3RpZnktY29udGVudC1jZW50ZXIge1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgfVxyXG4gIFxyXG4gIC5hbGlnbi1pdGVtcy1jZW50ZXIge1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB9XHJcbiAgXHJcbiAgLmdhcC0yID4gKiArICoge1xyXG4gICAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcclxuICB9XHJcbiAgXHJcbiAgLnAtMCB7XHJcbiAgICBwYWRkaW5nOiAwO1xyXG4gIH1cclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbSpinnerModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ctx_r1", "getImageUrl", "image_r5", "ɵɵsanitizeUrl", "name", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_img_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_div_4_Template", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_Template_button_click_9_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "toggleImageSelection", "ɵɵclassProp", "isImageTempSelected", "ɵɵtextInterpolate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_2_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template", "availableImages", "length", "ɵɵtwoWayListener", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "onCategoryChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener", "searchTerm", "ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener", "ref_r6", "dialogRef", "onCancelBinding", "close", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_21_listener", "onConfirmBinding", "materialName", "ɵɵtwoWayProperty", "categoryOptions", "isLoading", "tempSelectedImages", "previewingImage", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener", "_r8", "onToggleImageSelection", "isImageSelected", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener", "_r7", "onPreviousImage", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener", "onNextImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener", "onClose", "currentPreviewIndex", "images", "ɵɵtextInterpolate2", "showSelectionToggle", "ImagePreviewComponent_ng_template_0_nb_card_0_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template", "showBindingInterface", "ImagePreviewComponent", "constructor", "dialogService", "pictureService", "messageService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "confirmImageBinding", "categoryChange", "currentPage", "pageSize", "totalRecords", "ngOnInit", "loadImages", "ngOnChanges", "buildCaseId", "materialId", "pictureType", "undefined", "loadAvailableImages", "loadSelectedImages", "apiPictureGetPictureListPost$Json", "body", "CBuildCaseId", "cPictureType", "PageIndex", "PageSize", "CName", "subscribe", "next", "res", "StatusCode", "newImages", "Entries", "map", "picture", "id", "CId", "CPictureCode", "size", "thumbnailUrl", "CFile", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "TotalItems", "selectedIds", "img", "filter", "image", "includes", "setInitialPreviewImage", "showErrorMSG", "Message", "error", "message", "CMaterialId", "console", "Math", "max", "min", "emit", "some", "selected", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "getPictureByGuid", "apiPictureGetPictureGuidGet", "response", "log", "imageItem", "category", "openBindingInterface", "imageBindingRef", "index", "findIndex", "splice", "push", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "PictureService", "i3", "MessageService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "NbSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService, NbSpinnerModule } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\nimport { PictureService } from 'src/services/api/services';\r\nimport { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbSpinnerModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit, OnChanges {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n  \r\n  // 新增輸入參數來接收圖片載入所需的資訊\r\n  @Input() buildCaseId?: number;\r\n  @Input() materialId?: number;\r\n  @Input() pictureType?: number;\r\n  @Input() searchTerm?: string;\r\n  \r\n  // 新增綁定功能相關輸入參數\r\n  @Input() showBindingInterface: boolean = false;\r\n  @Input() materialName?: string;\r\n  \r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n  \r\n  // 新增綁定功能相關輸出事件\r\n  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();\r\n  @Output() categoryChange = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  // 內部屬性，不再依賴外部傳入\r\n  images: ImageItem[] = [];\r\n  availableImages: ImageItem[] = [];\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n  isLoading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 50;\r\n  totalRecords: number = 0;\r\n  \r\n  // 綁定模式相關屬性\r\n  categoryOptions = [\r\n    { value: 1, label: '建材圖片' },\r\n    { value: 2, label: '示意圖片' }\r\n  ];\r\n  selectedCategory: number = 1;\r\n  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private pictureService: PictureService,\r\n    private messageService: MessageService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadImages();\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // 當輸入參數變化時重新載入圖片\r\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 載入圖片的主要方法\r\n  loadImages(): void {\r\n    if (!this.buildCaseId && !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n  // 載入可選擇的圖片\r\n  loadAvailableImages(): void {\r\n    if (!this.buildCaseId) {\r\n      this.availableImages = [];\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: this.currentPage,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchTerm || undefined\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 轉換 API 回應為 ImageItem 格式\r\n          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n\r\n          this.totalRecords = res.TotalItems || 0;\r\n          \r\n          // 排除已選擇的圖片\r\n          const selectedIds = this.selectedImages.map(img => img.id);\r\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\r\n          \r\n          // 合併可選和已選圖片作為完整圖片列表\r\n          this.images = [...this.availableImages, ...this.selectedImages];\r\n          \r\n          // 設定初始預覽圖片\r\n          this.setInitialPreviewImage();\r\n        } else {\r\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 載入已選擇的圖片（如果有 materialId）\r\n  loadSelectedImages(): void {\r\n    if (!this.buildCaseId || !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CMaterialId: this.materialId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: 1,\r\n        PageSize: 999 // 載入所有已選圖片\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入已選擇圖片失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 設定初始預覽圖片\r\n  setInitialPreviewImage(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    } else {\r\n      this.previewingImage = null;\r\n      this.currentPreviewIndex = 0;\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    // 如果還沒有圖片，先載入圖片\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n    \r\n    // 開啟對話框\r\n    const template = imagePreviewRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 使用 CGuid 取得圖片資料的方法\r\n  getPictureByGuid(guid: string): void {\r\n    if (!guid) {\r\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\r\n      .subscribe({\r\n        next: (response) => {\r\n          // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\r\n          console.log('取得圖片資料:', response);\r\n        },\r\n        error: (error) => {\r\n          this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 獲取圖片 URL，優先使用 CGuid\r\n  getImageUrl(imageItem: ImageItem): string {\r\n    if (imageItem.guid && !imageItem.fullUrl) {\r\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\r\n      this.getPictureByGuid(imageItem.guid);\r\n    }\r\n    \r\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\r\n  }\r\n\r\n  // 綁定模式相關方法\r\n  onCategoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.categoryChange.emit(category);\r\n    if (this.buildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 開啟綁定界面\r\n  openBindingInterface(imageBindingRef?: TemplateRef<any>) {\r\n    this.showBindingInterface = true;\r\n    this.tempSelectedImages = [...this.selectedImages];\r\n    \r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n    \r\n    const template = imageBindingRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 確認圖片綁定\r\n  onConfirmBinding() {\r\n    this.confirmImageBinding.emit(this.tempSelectedImages);\r\n    this.showBindingInterface = false;\r\n  }\r\n\r\n  // 取消綁定\r\n  onCancelBinding() {\r\n    this.tempSelectedImages = [];\r\n    this.showBindingInterface = false;\r\n    this.onClose();\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n    } else {\r\n      this.tempSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否被選中\r\n  isImageTempSelected(image: ImageItem): boolean {\r\n    return this.tempSelectedImages.some(img => img.id === image.id);\r\n  }\r\n}", "<!-- 圖片預覽/綁定對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <!-- 綁定模式 -->\r\n  <nb-card *ngIf=\"showBindingInterface\" class=\"w-[90vw] max-w-[1000px] h-[80vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ materialName || '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"d-flex flex-column\" style=\"height: calc(100% - 120px); overflow: hidden;\">\r\n      \r\n      <!-- 控制區 -->\r\n      <div class=\"d-flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">圖片類別</label>\r\n          <nb-select [(selected)]=\"selectedCategory\" placeholder=\"選擇圖片類別\"\r\n            (selectedChange)=\"onCategoryChanged($event)\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"searchTerm\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center py-4\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n\r\n      <!-- 圖片網格 -->\r\n      <div *ngIf=\"!isLoading\" class=\"flex-1\" style=\"overflow-y: auto;\">\r\n        <div class=\"row\">\r\n          <div *ngFor=\"let image of availableImages\" class=\"col-md-3 col-sm-4 col-6 mb-3\">\r\n            <div class=\"card h-100\" [class.border-primary]=\"isImageTempSelected(image)\">\r\n              <!-- 圖片預覽 -->\r\n              <div class=\"card-img-top d-flex align-items-center justify-content-center\" style=\"height: 120px; background-color: #f8f9fa;\">\r\n                <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                  class=\"img-fluid\" style=\"max-height: 100%; object-fit: contain;\" />\r\n                <div *ngIf=\"!getImageUrl(image)\" class=\"text-muted text-center\">\r\n                  <i class=\"fas fa-image fa-2x mb-2\"></i>\r\n                  <div class=\"small\">無預覽</div>\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- 圖片資訊 -->\r\n              <div class=\"card-body p-2\">\r\n                <div class=\"small text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                <div class=\"text-center mt-2\">\r\n                  <button class=\"btn btn-sm\" \r\n                    [class.btn-primary]=\"isImageTempSelected(image)\"\r\n                    [class.btn-outline-primary]=\"!isImageTempSelected(image)\"\r\n                    (click)=\"toggleImageSelection(image)\">\r\n                    <i class=\"fas\" [class.fa-check]=\"isImageTempSelected(image)\" [class.fa-plus]=\"!isImageTempSelected(image)\"></i>\r\n                    {{ isImageTempSelected(image) ? '已選' : '選取' }}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div *ngIf=\"availableImages.length === 0\" class=\"text-center py-5 text-muted\">\r\n          <i class=\"fas fa-images fa-3x mb-3\"></i>\r\n          <div>找不到圖片</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇狀態 -->\r\n      <div class=\"flex-shrink-0 mt-3 text-center\">\r\n        已選擇 {{ tempSelectedImages.length }} 張圖片\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onCancelBinding(); ref.close()\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onConfirmBinding(); ref.close()\" \r\n        [disabled]=\"tempSelectedImages.length === 0\">\r\n        確定選擇 ({{ tempSelectedImages.length }})\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n\r\n  <!-- 預覽模式 -->\r\n  <nb-card *ngIf=\"!showBindingInterface\" class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n      \r\n      <!-- 圖片顯示 -->\r\n      <img *ngIf=\"!isLoading && previewingImage && getImageUrl(previewingImage)\" \r\n        [src]=\"getImageUrl(previewingImage)\"\r\n        [alt]=\"previewingImage.name\" \r\n        class=\"max-w-full max-h-full object-contain\" />\r\n      \r\n      <!-- 無圖片狀態 -->\r\n      <div *ngIf=\"!isLoading && (!previewingImage || !getImageUrl(previewingImage))\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>無可預覽的圖片</div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAmD,eAAe;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAA0BC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;ICY1DC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAUNT,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IASIH,EAAA,CAAAU,SAAA,cACqE;;;;;IADVV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAb,EAAA,CAAAc,aAAA,CAA0B,QAAAD,QAAA,CAAAE,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAU,SAAA,YAAuC;IACvCV,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IACxBF,EADwB,CAAAG,YAAA,EAAM,EACxB;;;;;;IANRH,EAHJ,CAAAC,cAAA,cAAgF,cACF,cAEmD;IAG3HD,EAFA,CAAAgB,UAAA,IAAAC,yEAAA,kBACqE,IAAAC,yEAAA,kBACL;IAIlElB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA2B,cAC6B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE1EH,EADF,CAAAC,cAAA,cAA8B,iBAIY;IAAtCD,EAAA,CAAAmB,UAAA,mBAAAC,4FAAA;MAAA,MAAAP,QAAA,GAAAb,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAe,oBAAA,CAAAb,QAAA,CAA2B;IAAA,EAAC;IACrCb,EAAA,CAAAU,SAAA,aAA+G;IAC/GV,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;;IAzBoBH,EAAA,CAAAO,SAAA,EAAmD;IAAnDP,EAAA,CAAA2B,WAAA,mBAAAhB,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,EAAmD;IAGjEb,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAwB;IAExBb,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAyB;IAQEb,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAS,QAAA,CAAAE,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA6B,iBAAA,CAAAhB,QAAA,CAAAE,IAAA,CAAgB;IAGlEf,EAAA,CAAAO,SAAA,GAAgD;IAChDP,EADA,CAAA2B,WAAA,gBAAAhB,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,EAAgD,yBAAAF,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,EACS;IAE1Cb,EAAA,CAAAO,SAAA,EAA6C;IAACP,EAA9C,CAAA2B,WAAA,aAAAhB,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,EAA6C,aAAAF,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,EAA8C;IAC1Gb,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,MAAA,CAAAiB,mBAAA,CAAAf,QAAA,yCACF;;;;;IAQVb,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAU,SAAA,YAAwC;IACxCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IACZF,EADY,CAAAG,YAAA,EAAM,EACZ;;;;;IAlCNH,EADF,CAAAC,cAAA,cAAiE,cAC9C;IACfD,EAAA,CAAAgB,UAAA,IAAAc,mEAAA,oBAAgF;IA2BlF9B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgB,UAAA,IAAAe,mEAAA,kBAA8E;IAIhF/B,EAAA,CAAAG,YAAA,EAAM;;;;IAlCqBH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAqB,eAAA,CAAkB;IA8BrChC,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAqB,eAAA,CAAAC,MAAA,OAAkC;;;;;;IA7D5CjC,EADF,CAAAC,cAAA,iBAA+E,qBAC7D;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,sBAA+F,aAGhD,aACvB,eACQ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,mBAC+C;IADpCD,EAAA,CAAAkC,gBAAA,4BAAAC,2FAAAC,MAAA;MAAApC,EAAA,CAAAqB,aAAA,CAAAgB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAsC,kBAAA,CAAA3B,MAAA,CAAA4B,gBAAA,EAAAH,MAAA,MAAAzB,MAAA,CAAA4B,gBAAA,GAAAH,MAAA;MAAA,OAAApC,EAAA,CAAAyB,WAAA,CAAAW,MAAA;IAAA,EAA+B;IACxCpC,EAAA,CAAAmB,UAAA,4BAAAgB,2FAAAC,MAAA;MAAApC,EAAA,CAAAqB,aAAA,CAAAgB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAkBd,MAAA,CAAA6B,iBAAA,CAAAJ,MAAA,CAAyB;IAAA,EAAC;IAC5CpC,EAAA,CAAAgB,UAAA,IAAAyB,kEAAA,uBAAyE;IAI7EzC,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,iBAA2F;IAA3BD,EAAA,CAAAkC,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAApC,EAAA,CAAAqB,aAAA,CAAAgB,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAsC,kBAAA,CAAA3B,MAAA,CAAAgC,UAAA,EAAAP,MAAA,MAAAzB,MAAA,CAAAgC,UAAA,GAAAP,MAAA;MAAA,OAAApC,EAAA,CAAAyB,WAAA,CAAAW,MAAA;IAAA,EAAwB;IAE5FpC,EAFI,CAAAG,YAAA,EAA2F,EACvF,EACF;IASNH,EANA,CAAAgB,UAAA,KAAA4B,6DAAA,kBAAgD,KAAAC,6DAAA,kBAMiB;IAuCjE7C,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAuD,kBACsB;IAAzCD,EAAA,CAAAmB,UAAA,mBAAA2B,gFAAA;MAAA9C,EAAA,CAAAqB,aAAA,CAAAgB,GAAA;MAAA,MAAAU,MAAA,GAAA/C,EAAA,CAAAwB,aAAA,GAAAwB,SAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAsC,eAAA,EAAiB;MAAA,OAAAjD,EAAA,CAAAyB,WAAA,CAAEsB,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAClD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtFH,EAAA,CAAAC,cAAA,kBAC+C;IADfD,EAAA,CAAAmB,UAAA,mBAAAgC,gFAAA;MAAAnD,EAAA,CAAAqB,aAAA,CAAAgB,GAAA;MAAA,MAAAU,MAAA,GAAA/C,EAAA,CAAAwB,aAAA,GAAAwB,SAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAyC,gBAAA,EAAkB;MAAA,OAAApD,EAAA,CAAAyB,WAAA,CAAEsB,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEvElD,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;IA9ENH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAG,MAAA,CAAA0C,YAAA,gDACF;IAOiBrD,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAsD,gBAAA,aAAA3C,MAAA,CAAA4B,gBAAA,CAA+B;IAEVvC,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA4C,eAAA,CAAkB;IAOcvD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAsD,gBAAA,YAAA3C,MAAA,CAAAgC,UAAA,CAAwB;IAKtF3C,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA6C,SAAA,CAAe;IAMfxD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAA6C,SAAA,CAAgB;IAwCpBxD,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAG,MAAA,CAAA8C,kBAAA,CAAAxB,MAAA,yBACF;IAKEjC,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8C,kBAAA,CAAAxB,MAAA,OAA4C;IAC5CjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA8C,kBAAA,CAAAxB,MAAA,OACF;;;;;IAqBAjC,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAGNH,EAAA,CAAAU,SAAA,cAGiD;;;;IAD/CV,EADA,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA+C,eAAA,GAAA1D,EAAA,CAAAc,aAAA,CAAoC,QAAAH,MAAA,CAAA+C,eAAA,CAAA3C,IAAA,CACR;;;;;IAI9Bf,EAAA,CAAAC,cAAA,cAAiH;IAC/GD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACdF,EADc,CAAAG,YAAA,EAAM,EACd;;;;;;IAQJH,EAAA,CAAAC,cAAA,iBAA2G;IAAnCD,EAAA,CAAAmB,UAAA,mBAAAwC,yFAAA;MAAA3D,EAAA,CAAAqB,aAAA,CAAAuC,GAAA;MAAA,MAAAjD,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAkD,sBAAA,EAAwB;IAAA,EAAC;IACxG7D,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,MAAA,CAAA+C,eAAA,IAAA/C,MAAA,CAAAmD,eAAA,CAAAnD,MAAA,CAAA+C,eAAA,uEACF;;;;;;IAvCF1D,EAFJ,CAAAC,cAAA,kBAAmE,yBACS,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,iBACyF;IAA5BD,EAAA,CAAAmB,UAAA,mBAAA4C,+EAAA;MAAA/D,EAAA,CAAAqB,aAAA,CAAA2C,GAAA;MAAA,MAAArD,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsD,eAAA,EAAiB;IAAA,EAAC;IAC9GjE,EAAA,CAAAU,SAAA,YAAmC;IAACV,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC0B;IAAxBD,EAAA,CAAAmB,UAAA,mBAAA+C,+EAAA;MAAAlE,EAAA,CAAAqB,aAAA,CAAA2C,GAAA;MAAA,MAAArD,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAwD,WAAA,EAAa;IAAA,EAAC;IACvBnE,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAU,SAAA,aAAoC;IAG9CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAkG;IAchGD,EAZA,CAAAgB,UAAA,KAAAoD,6DAAA,kBAA2C,KAAAC,6DAAA,kBASM,KAAAC,6DAAA,kBAGgE;IAInHtE,EAAA,CAAAG,YAAA,EAAe;IAGbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,UAAA,KAAAuD,gEAAA,qBAA2G;IAG3GvE,EAAA,CAAAC,cAAA,kBAAuE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAqD,gFAAA;MAAAxE,EAAA,CAAAqB,aAAA,CAAA2C,GAAA;MAAA,MAAAjB,MAAA,GAAA/C,EAAA,CAAAwB,aAAA,GAAAwB,SAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAA8D,OAAA,EAAS;MAAA,OAAAzE,EAAA,CAAAyB,WAAA,CAAEsB,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAClD,EAAA,CAAAE,MAAA,oBAAE;IAG/EF,EAH+E,CAAAG,YAAA,EAAS,EAC9E,EACS,EACT;;;;IA3CAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA+C,eAAA,kBAAA/C,MAAA,CAAA+C,eAAA,CAAA3C,IAAA,KAAkC;IAESf,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+D,mBAAA,MAAqC;IAGrC1E,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+D,mBAAA,IAAA/D,MAAA,CAAAgE,MAAA,CAAA1C,MAAA,KAAqD;IAShGjC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA6C,SAAA,CAAe;IAMfxD,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAA6C,SAAA,IAAA7C,MAAA,CAAA+C,eAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA+C,eAAA,EAAmE;IAMnE1D,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAA6C,SAAA,MAAA7C,MAAA,CAAA+C,eAAA,KAAA/C,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA+C,eAAA,GAAuE;IAQ3E1D,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA4E,kBAAA,MAAAjE,MAAA,CAAA+D,mBAAA,aAAA/D,MAAA,CAAAgE,MAAA,CAAA1C,MAAA,MACF;IAEWjC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAkE,mBAAA,CAAyB;;;;;IAvCxC7E,EAnFA,CAAAgB,UAAA,IAAA8D,sDAAA,sBAA+E,IAAAC,sDAAA,sBAmFZ;;;;IAnFzD/E,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAqE,oBAAA,CAA0B;IAmF1BhF,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAqE,oBAAA,CAA2B;;;AD5DvC,OAAM,MAAOC,qBAAqB;EA+ChCC,YACUC,aAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAjDf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAX,mBAAmB,GAAY,IAAI;IAQ5C;IACS,KAAAG,oBAAoB,GAAY,KAAK;IAGpC,KAAAS,oBAAoB,GAAG,IAAI7F,YAAY,EAAa;IACpD,KAAAsD,KAAK,GAAG,IAAItD,YAAY,EAAQ;IAChC,KAAA8F,aAAa,GAAG,IAAI9F,YAAY,EAAU;IAC1C,KAAA+F,SAAS,GAAG,IAAI/F,YAAY,EAAU;IAEhD;IACU,KAAAgG,mBAAmB,GAAG,IAAIhG,YAAY,EAAe;IACrD,KAAAiG,cAAc,GAAG,IAAIjG,YAAY,EAAU;IAIrD;IACA,KAAA+E,MAAM,GAAgB,EAAE;IACxB,KAAA3C,eAAe,GAAgB,EAAE;IACjC,KAAA0B,eAAe,GAAqB,IAAI;IACxC,KAAAgB,mBAAmB,GAAW,CAAC;IAC/B,KAAAlB,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAsC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,CAAC;IAExB;IACA,KAAAzC,eAAe,GAAG,CAChB;MAAEjD,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IACD,KAAA8B,gBAAgB,GAAW,CAAC;IAC5B,KAAAkB,kBAAkB,GAAgB,EAAE,CAAC,CAAC;EAMnC;EAEHwC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MACzE,IAAI,CAACL,UAAU,EAAE;IACnB;EACF;EAEA;EACAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgD,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;MACrB,IAAI,CAACpE,eAAe,GAAG,EAAE;MACzB;IACF;IAEA,IAAI,CAACoD,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9BS,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,IAAI,CAAChB,WAAW;QAC3BiB,QAAQ,EAAE,IAAI,CAAChB,QAAQ;QACvBiB,KAAK,EAAE,IAAI,CAACrE,UAAU,IAAI4D;;KAE7B,CAAC,CAACU,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YACvEC,EAAE,EAAED,OAAO,CAACE,GAAG,IAAI,CAAC;YACpB3G,IAAI,EAAEyG,OAAO,CAACG,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEP,OAAO,CAACM,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEX,OAAO,CAACY;WACf,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACpC,YAAY,GAAGmB,GAAG,CAACkB,UAAU,IAAI,CAAC;UAEvC;UACA,MAAMC,WAAW,GAAG,IAAI,CAAC/C,cAAc,CAACgC,GAAG,CAACgB,GAAG,IAAIA,GAAG,CAACd,EAAE,CAAC;UAC1D,IAAI,CAACzF,eAAe,GAAGqF,SAAS,CAACmB,MAAM,CAACC,KAAK,IAAI,CAACH,WAAW,CAACI,QAAQ,CAACD,KAAK,CAAChB,EAAE,CAAC,CAAC;UAEjF;UACA,IAAI,CAAC9C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC3C,eAAe,EAAE,GAAG,IAAI,CAACuD,cAAc,CAAC;UAE/D;UACA,IAAI,CAACoD,sBAAsB,EAAE;QAC/B,CAAC,MAAM;UACL,IAAI,CAACtD,cAAc,CAACuD,YAAY,CAACzB,GAAG,CAAC0B,OAAO,IAAI,QAAQ,CAAC;QAC3D;QACA,IAAI,CAACrF,SAAS,GAAG,KAAK;MACxB,CAAC;MACDsF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,UAAU,IAAIE,KAAK,CAACC,OAAO,IAAI,MAAM,CAAC,CAAC;QACxE,IAAI,CAACvF,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACAiD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACL,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAACjB,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9B4C,WAAW,EAAE,IAAI,CAAC3C,UAAU;QAC5BQ,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACE,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7B,cAAc,GAAG4B,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YAC3EC,EAAE,EAAED,OAAO,CAACE,GAAG,IAAI,CAAC;YACpB3G,IAAI,EAAEyG,OAAO,CAACG,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEP,OAAO,CAACM,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEX,OAAO,CAACY;WACf,CAAC,CAAC,IAAI,EAAE;QACX;MACF,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfG,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACAH,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAChE,MAAM,CAAC1C,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACyC,mBAAmB,GAAGwE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC5D,iBAAiB,EAAE,IAAI,CAACb,MAAM,CAAC1C,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACyB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAChB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgB,mBAAmB,GAAG,CAAC;IAC9B;EACF;EAEAT,eAAeA,CAAA;IACb,IAAI,IAAI,CAACS,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACgB,aAAa,CAAC2D,IAAI,CAAC,IAAI,CAAC3E,mBAAmB,CAAC;IACnD;EACF;EAEAP,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACyC,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACiB,SAAS,CAAC0D,IAAI,CAAC,IAAI,CAAC3E,mBAAmB,CAAC;IAC/C;EACF;EAEAb,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACH,eAAe,EAAE;MACxB,IAAI,CAAC+B,oBAAoB,CAAC4D,IAAI,CAAC,IAAI,CAAC3F,eAAe,CAAC;IACtD;EACF;EAEAI,eAAeA,CAAC2E,KAAgB;IAC9B,OAAO,IAAI,CAAClD,cAAc,CAAC+D,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAC9B,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;EACvE;EAEAhD,OAAOA,CAAA;IACL,IAAI,CAACvB,KAAK,CAACmG,IAAI,EAAE;EACnB;EAEA;EACAG,WAAWA,CAACC,eAAkC;IAC5C;IACA,IAAI,IAAI,CAAC9E,MAAM,CAAC1C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACiE,UAAU,EAAE;IACnB;IAEA;IACA,MAAMwD,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;IACrD,IAAI,CAACxE,aAAa,CAACyE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAG,gBAAgBA,CAAC1B,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAC9C,cAAc,CAACuD,YAAY,CAAC,cAAc,CAAC;MAChD;IACF;IAEA,IAAI,CAACxD,cAAc,CAAC0E,2BAA2B,CAAC;MAAE3B,IAAI,EAAEA;IAAI,CAAE,CAAC,CAC5DlB,SAAS,CAAC;MACTC,IAAI,EAAG6C,QAAQ,IAAI;QACjB;QACAd,OAAO,CAACe,GAAG,CAAC,SAAS,EAAED,QAAQ,CAAC;MAClC,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,WAAWE,KAAK,CAACC,OAAO,IAAI,MAAM,EAAE,CAAC;MACxE;KACD,CAAC;EACN;EAEA;EACAnI,WAAWA,CAACqJ,SAAoB;IAC9B,IAAIA,SAAS,CAAC9B,IAAI,IAAI,CAAC8B,SAAS,CAAClC,OAAO,EAAE;MACxC;MACA,IAAI,CAAC8B,gBAAgB,CAACI,SAAS,CAAC9B,IAAI,CAAC;IACvC;IAEA,OAAO8B,SAAS,CAAClC,OAAO,IAAIkC,SAAS,CAACpC,YAAY,IAAI,EAAE;EAC1D;EAEA;EACArF,iBAAiBA,CAAC0H,QAAgB;IAChC,IAAI,CAAC3H,gBAAgB,GAAG2H,QAAQ;IAChC,IAAI,CAACrE,cAAc,CAACwD,IAAI,CAACa,QAAQ,CAAC;IAClC,IAAI,IAAI,CAAC9D,WAAW,EAAE;MACpB,IAAI,CAACF,UAAU,EAAE;IACnB;EACF;EAEA;EACAiE,oBAAoBA,CAACC,eAAkC;IACrD,IAAI,CAACpF,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACvB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC8B,cAAc,CAAC;IAElD,IAAI,IAAI,CAACZ,MAAM,CAAC1C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACiE,UAAU,EAAE;IACnB;IAEA,MAAMwD,QAAQ,GAAGU,eAAe,IAAI,IAAI,CAACT,YAAY;IACrD,IAAI,CAACxE,aAAa,CAACyE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAtG,gBAAgBA,CAAA;IACd,IAAI,CAACwC,mBAAmB,CAACyD,IAAI,CAAC,IAAI,CAAC5F,kBAAkB,CAAC;IACtD,IAAI,CAACuB,oBAAoB,GAAG,KAAK;EACnC;EAEA;EACA/B,eAAeA,CAAA;IACb,IAAI,CAACQ,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACuB,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACP,OAAO,EAAE;EAChB;EAEA;EACA/C,oBAAoBA,CAAC+G,KAAgB;IACnC,MAAM4B,KAAK,GAAG,IAAI,CAAC5G,kBAAkB,CAAC6G,SAAS,CAAC/B,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;IAC3E,IAAI4C,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC5G,kBAAkB,CAAC8G,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC5G,kBAAkB,CAAC+G,IAAI,CAAC/B,KAAK,CAAC;IACrC;EACF;EAEA;EACA7G,mBAAmBA,CAAC6G,KAAgB;IAClC,OAAO,IAAI,CAAChF,kBAAkB,CAAC6F,IAAI,CAACf,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;EACjE;;;uCAnSWxC,qBAAqB,EAAAjF,EAAA,CAAAyK,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA3K,EAAA,CAAAyK,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7K,EAAA,CAAAyK,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArB9F,qBAAqB;MAAA+F,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCzBlCnL,EAAA,CAAAgB,UAAA,IAAAqK,4CAAA,gCAAArL,EAAA,CAAAsL,sBAAA,CAA0D;;;qBDuB9CzL,YAAY,EAAA0L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1L,YAAY,EAAA2L,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAnB,EAAA,CAAAoB,eAAA,EAAApB,EAAA,CAAAqB,mBAAA,EAAArB,EAAA,CAAAsB,qBAAA,EAAAtB,EAAA,CAAAuB,qBAAA,EAAAvB,EAAA,CAAAwB,iBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAErM,eAAe,EAAA4K,EAAA,CAAA0B,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}