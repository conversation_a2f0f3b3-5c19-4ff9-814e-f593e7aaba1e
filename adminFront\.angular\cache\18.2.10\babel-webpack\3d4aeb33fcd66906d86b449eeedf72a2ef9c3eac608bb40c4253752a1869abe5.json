{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbSpinnerModule } from '@nebular/theme';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"nb-spinner\", 19);\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n  if (rf & 2) {\n    const image_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isImageTempSelected(image_r5) ? \"scale(1.05)\" : \"scale(1)\");\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r5), i0.ɵɵsanitizeUrl)(\"alt\", image_r5.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"i\", 40);\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵtext(4, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template_div_click_1_listener() {\n      const image_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleImageSelection(image_r5));\n    });\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_img_3_Template, 1, 4, \"img\", 30)(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_4_Template, 5, 0, \"div\", 31)(5, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_5_Template, 2, 0, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"div\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"span\", 36);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const image_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"ring-2\", ctx_r1.isImageTempSelected(image_r5))(\"ring-primary\", ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-success\", ctx_r1.isImageTempSelected(image_r5))(\"bg-light\", !ctx_r1.isImageTempSelected(image_r5))(\"text-dark\", !ctx_r1.isImageTempSelected(image_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isImageTempSelected(image_r5) ? \"\\u5DF2\\u9078\" : \"\\u9EDE\\u64CA\\u9078\\u53D6\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵelementStart(2, \"h5\", 46);\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 47);\n    i0.ɵɵtext(5, \"\\u8ACB\\u5617\\u8A66\\u8ABF\\u6574\\u641C\\u5C0B\\u689D\\u4EF6\\u6216\\u9078\\u64C7\\u4E0D\\u540C\\u7684\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_li_12_Template_button_click_1_listener() {\n      const page_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.goToPage(page_r8));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"active\", page_r8 === ctx_r1.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r8);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 35)(2, \"div\", 49);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nav\", 50)(5, \"ul\", 51)(6, \"li\", 52)(7, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(1));\n    });\n    i0.ɵɵelement(8, \"i\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 52)(10, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage - 1));\n    });\n    i0.ɵɵelement(11, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_li_12_Template, 3, 3, \"li\", 56);\n    i0.ɵɵelementStart(13, \"li\", 52)(14, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage + 1));\n    });\n    i0.ɵɵelement(15, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\", 52)(17, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.totalPages));\n    });\n    i0.ɵɵelement(18, \"i\", 58);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.currentPage - 1) * ctx_r1.itemsPerPage + 1, \" - \", ctx_r1.Math.min(ctx_r1.currentPage * ctx_r1.itemsPerPage, ctx_r1.availableImages.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r1.availableImages.length, \" \\u7B46\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template, 12, 16, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_4_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template, 19, 16, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableImages.length > 0);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"label\", 7);\n    i0.ɵɵtext(7, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-select\", 8);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChanged($event));\n    });\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"label\", 7);\n    i0.ɵɵtext(12, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_input_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchChanged());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(14, ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template, 4, 0, \"div\", 11)(15, ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template, 6, 3, \"div\", 12);\n    i0.ɵɵelementStart(16, \"div\", 13);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"nb-card-footer\", 14)(19, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r9 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onCancelBinding();\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r9 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onConfirmBinding();\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r1.materialName || \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7 \", ctx_r1.tempSelectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.tempSelectedImages.length, \") \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"nb-spinner\", 19);\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 72);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(ctx_r1.previewingImage), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"i\", 74);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u53EF\\u9810\\u89BD\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 60)(1, \"nb-card-header\", 35)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 61)(5, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 63);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 65);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template, 4, 0, \"div\", 66)(13, ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template, 1, 2, \"img\", 67)(14, ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 35)(16, \"div\", 69);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 61);\n    i0.ɵɵtemplate(19, ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template, 2, 1, \"button\", 70);\n    i0.ɵɵelementStart(20, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ref_r9 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.previewingImage && ctx_r1.getImageUrl(ctx_r1.previewingImage));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && (!ctx_r1.previewingImage || !ctx_r1.getImageUrl(ctx_r1.previewingImage)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_nb_card_0_Template, 23, 9, \"nb-card\", 1)(1, ImagePreviewComponent_ng_template_0_nb_card_1_Template, 22, 9, \"nb-card\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBindingInterface);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showBindingInterface);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService, pictureService, messageService) {\n    this.dialogService = dialogService;\n    this.pictureService = pictureService;\n    this.messageService = messageService;\n    this.isVisible = false;\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    // 新增綁定功能相關輸入參數\n    this.showBindingInterface = false;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    // 新增綁定功能相關輸出事件\n    this.confirmImageBinding = new EventEmitter();\n    this.categoryChange = new EventEmitter();\n    // 內部屬性，不再依賴外部傳入\n    this.images = [];\n    this.availableImages = [];\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    this.isLoading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalRecords = 0;\n    // 前端分頁相關（每頁20個項目）\n    this.itemsPerPage = 20;\n    this.paginatedImages = [];\n    this.Math = Math; // 在模板中使用 Math 對象\n    // 綁定模式相關屬性\n    this.categoryOptions = [{\n      value: 1,\n      label: '建材圖片'\n    }, {\n      value: 2,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = 1;\n    this.tempSelectedImages = []; // 臨時選擇的圖片\n  }\n  ngOnInit() {\n    this.loadImages();\n    this.updatePaginatedImages();\n  }\n  ngOnChanges() {\n    // 當輸入參數變化時重新載入圖片\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\n      this.loadImages();\n    }\n  }\n  // 載入圖片的主要方法\n  loadImages() {\n    if (!this.buildCaseId && !this.materialId) {\n      return;\n    }\n    this.isLoading = true;\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n  }\n  // 載入可選擇的圖片\n  loadAvailableImages() {\n    if (!this.buildCaseId) {\n      this.availableImages = [];\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        cPictureType: this.pictureType,\n        PageIndex: this.currentPage,\n        PageSize: this.pageSize,\n        CName: this.searchTerm || undefined\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          // 轉換 API 回應為 ImageItem 格式\n          const newImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n          this.totalRecords = res.TotalItems || 0;\n          // 排除已選擇的圖片\n          const selectedIds = this.selectedImages.map(img => img.id);\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\n          // 合併可選和已選圖片作為完整圖片列表\n          this.images = [...this.availableImages, ...this.selectedImages];\n          // 更新分頁顯示\n          this.updatePaginatedImages();\n          // 設定初始預覽圖片\n          this.setInitialPreviewImage();\n        } else {\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\n        this.isLoading = false;\n      }\n    });\n  }\n  // 載入已選擇的圖片（如果有 materialId）\n  loadSelectedImages() {\n    if (!this.buildCaseId || !this.materialId) {\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CMaterialId: this.materialId,\n        cPictureType: this.pictureType,\n        PageIndex: 1,\n        PageSize: 999 // 載入所有已選圖片\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n        }\n      },\n      error: error => {\n        console.error('載入已選擇圖片失敗:', error);\n      }\n    });\n  }\n  // 設定初始預覽圖片\n  setInitialPreviewImage() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    } else {\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    // 如果還沒有圖片，先載入圖片\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    // 開啟對話框\n    const template = imagePreviewRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 使用 CGuid 取得圖片資料的方法\n  getPictureByGuid(guid) {\n    if (!guid) {\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\n      return;\n    }\n    this.pictureService.apiPictureGetPictureGuidGet({\n      guid: guid\n    }).subscribe({\n      next: response => {\n        // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\n        console.log('取得圖片資料:', response);\n      },\n      error: error => {\n        this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\n      }\n    });\n  }\n  // 獲取圖片 URL，優先使用 CGuid\n  getImageUrl(imageItem) {\n    if (imageItem.guid && !imageItem.fullUrl) {\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\n      this.getPictureByGuid(imageItem.guid);\n    }\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\n  }\n  // 綁定模式相關方法\n  onCategoryChanged(category) {\n    this.selectedCategory = category;\n    this.currentPage = 1; // 重置到第一頁\n    this.categoryChange.emit(category);\n    if (this.buildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 開啟綁定界面\n  openBindingInterface(imageBindingRef) {\n    this.showBindingInterface = true;\n    this.tempSelectedImages = [...this.selectedImages];\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    const template = imageBindingRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 確認圖片綁定\n  onConfirmBinding() {\n    this.confirmImageBinding.emit(this.tempSelectedImages);\n    this.showBindingInterface = false;\n  }\n  // 取消綁定\n  onCancelBinding() {\n    this.tempSelectedImages = [];\n    this.showBindingInterface = false;\n    this.onClose();\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n    } else {\n      this.tempSelectedImages.push(image);\n    }\n  }\n  // 檢查圖片是否被選中\n  isImageTempSelected(image) {\n    return this.tempSelectedImages.some(img => img.id === image.id);\n  }\n  // 分頁相關方法\n  get totalPages() {\n    return Math.ceil(this.availableImages.length / this.itemsPerPage);\n  }\n  // 更新分頁顯示的圖片\n  updatePaginatedImages() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    this.paginatedImages = this.availableImages.slice(startIndex, endIndex);\n  }\n  // 跳轉到指定頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {\n      this.currentPage = page;\n      this.updatePaginatedImages();\n      // 滾動到頂部\n      const scrollContainer = document.querySelector('.flex-1[style*=\"overflow-y: auto\"]');\n      if (scrollContainer) {\n        scrollContainer.scrollTop = 0;\n      }\n    }\n  }\n  // 獲取可見的頁碼\n  getVisiblePages() {\n    const totalPages = this.totalPages;\n    const currentPage = this.currentPage;\n    const maxVisiblePages = 5;\n    if (totalPages <= maxVisiblePages) {\n      return Array.from({\n        length: totalPages\n      }, (_, i) => i + 1);\n    }\n    const halfVisible = Math.floor(maxVisiblePages / 2);\n    let startPage = Math.max(1, currentPage - halfVisible);\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n    // 調整開始頁面以確保顯示足夠的頁碼\n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\n    }\n    return Array.from({\n      length: endPage - startPage + 1\n    }, (_, i) => startPage + i);\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.PictureService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\",\n        buildCaseId: \"buildCaseId\",\n        materialId: \"materialId\",\n        pictureType: \"pictureType\",\n        searchTerm: \"searchTerm\",\n        showBindingInterface: \"showBindingInterface\",\n        materialName: \"materialName\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\",\n        confirmImageBinding: \"confirmImageBinding\",\n        categoryChange: \"categoryChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [\"class\", \"w-[90vw] max-w-[1000px] h-[80vh]\", 4, \"ngIf\"], [\"class\", \"w-[800px] h-[600px]\", 4, \"ngIf\"], [1, \"w-[90vw]\", \"max-w-[1000px]\", \"h-[80vh]\"], [1, \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\"], [1, \"d-flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [1, \"form-label\"], [\"placeholder\", \"\\u9078\\u64C7\\u5716\\u7247\\u985E\\u5225\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"flex-1 d-flex flex-column\", \"style\", \"overflow: hidden;\", 4, \"ngIf\"], [1, \"flex-shrink-0\", \"mt-3\", \"text-center\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"text-center\", \"py-4\"], [\"size\", \"large\"], [1, \"mt-2\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", 2, \"overflow\", \"hidden\"], [1, \"flex-1\", 2, \"overflow-y\", \"auto\"], [1, \"row\", \"g-3\"], [\"class\", \"col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"mt-3 pt-3 border-top\", 4, \"ngIf\"], [1, \"col-xl-2\", \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"col-6\"], [1, \"card\", \"h-100\", \"shadow-sm\", \"border-0\", 2, \"transition\", \"all 0.2s ease-in-out\", \"cursor\", \"pointer\", 3, \"click\"], [1, \"position-relative\", \"overflow-hidden\", 2, \"height\", \"140px\", \"background\", \"linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)\"], [\"class\", \"img-fluid w-100 h-100\", \"style\", \"object-fit: cover; transition: transform 0.2s ease-in-out;\", 3, \"src\", \"alt\", \"transform\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center justify-content-center h-100 text-muted\", 4, \"ngIf\"], [\"class\", \"position-absolute top-0 end-0 m-2 bg-primary text-white rounded-circle d-flex align-items-center justify-content-center\", \"style\", \"width: 24px; height: 24px; font-size: 12px;\", 4, \"ngIf\"], [1, \"card-body\", \"p-2\"], [1, \"small\", \"text-truncate\", \"fw-medium\", \"mb-1\", 3, \"title\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"badge\"], [1, \"img-fluid\", \"w-100\", \"h-100\", 2, \"object-fit\", \"cover\", \"transition\", \"transform 0.2s ease-in-out\", 3, \"src\", \"alt\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"h-100\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-image\", \"fa-2x\", \"mb-2\", \"opacity-50\"], [1, \"small\"], [1, \"position-absolute\", \"top-0\", \"end-0\", \"m-2\", \"bg-primary\", \"text-white\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"24px\", \"height\", \"24px\", \"font-size\", \"12px\"], [1, \"fas\", \"fa-check\"], [1, \"text-center\", \"py-5\", \"text-muted\"], [1, \"fas\", \"fa-images\", \"fa-3x\", \"mb-3\", \"opacity-50\"], [1, \"text-muted\", \"mb-2\"], [1, \"small\", \"text-muted\"], [1, \"mt-3\", \"pt-3\", \"border-top\"], [1, \"text-muted\", \"small\"], [\"aria-label\", \"\\u5716\\u7247\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-angle-right\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-link\", 3, \"click\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbSelectComponent, i1.NbOptionComponent, NbSpinnerModule, i1.NbSpinnerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbSpinnerModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ɵɵstyleProp", "ctx_r1", "isImageTempSelected", "image_r5", "getImageUrl", "ɵɵsanitizeUrl", "name", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template_div_click_1_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "toggleImageSelection", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_img_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_4_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_div_5_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_li_12_Template_button_click_1_listener", "page_r8", "_r7", "goToPage", "currentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_7_listener", "_r6", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_10_listener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_li_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_14_listener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template_button_click_17_listener", "totalPages", "ɵɵtextInterpolate3", "itemsPerPage", "Math", "min", "availableImages", "length", "getVisiblePages", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_4_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_div_5_Template", "paginatedImages", "ɵɵtwoWayListener", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "onCategoryChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener", "searchTerm", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_input_13_listener", "onSearchChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_15_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_19_listener", "ref_r9", "dialogRef", "onCancelBinding", "close", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_21_listener", "onConfirmBinding", "materialName", "ɵɵtwoWayProperty", "categoryOptions", "isLoading", "tempSelectedImages", "previewingImage", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener", "_r11", "onToggleImageSelection", "isImageSelected", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener", "_r10", "onPreviousImage", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener", "onNextImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener", "onClose", "currentPreviewIndex", "images", "ɵɵtextInterpolate2", "showSelectionToggle", "ImagePreviewComponent_ng_template_0_nb_card_0_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template", "showBindingInterface", "ImagePreviewComponent", "constructor", "dialogService", "pictureService", "messageService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "confirmImageBinding", "categoryChange", "pageSize", "totalRecords", "ngOnInit", "loadImages", "updatePaginatedImages", "ngOnChanges", "buildCaseId", "materialId", "pictureType", "undefined", "loadAvailableImages", "loadSelectedImages", "apiPictureGetPictureListPost$Json", "body", "CBuildCaseId", "cPictureType", "PageIndex", "PageSize", "CName", "subscribe", "next", "res", "StatusCode", "newImages", "Entries", "map", "picture", "id", "CId", "CPictureCode", "size", "thumbnailUrl", "CFile", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "TotalItems", "selectedIds", "img", "filter", "image", "includes", "setInitialPreviewImage", "showErrorMSG", "Message", "error", "message", "CMaterialId", "console", "max", "emit", "some", "selected", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "getPictureByGuid", "apiPictureGetPictureGuidGet", "response", "log", "imageItem", "category", "openBindingInterface", "imageBindingRef", "index", "findIndex", "splice", "push", "ceil", "startIndex", "endIndex", "slice", "page", "scrollContainer", "document", "querySelector", "scrollTop", "maxVisiblePages", "Array", "from", "_", "i", "halfVisible", "floor", "startPage", "endPage", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "PictureService", "i3", "MessageService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "NbSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService, NbSpinnerModule } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\nimport { PictureService } from 'src/services/api/services';\r\nimport { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbSpinnerModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit, OnChanges {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n\r\n  // 新增輸入參數來接收圖片載入所需的資訊\r\n  @Input() buildCaseId?: number;\r\n  @Input() materialId?: number;\r\n  @Input() pictureType?: number;\r\n  @Input() searchTerm?: string;\r\n\r\n  // 新增綁定功能相關輸入參數\r\n  @Input() showBindingInterface: boolean = false;\r\n  @Input() materialName?: string;\r\n\r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  // 新增綁定功能相關輸出事件\r\n  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();\r\n  @Output() categoryChange = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  // 內部屬性，不再依賴外部傳入\r\n  images: ImageItem[] = [];\r\n  availableImages: ImageItem[] = [];\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n  isLoading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 50;\r\n  totalRecords: number = 0;\r\n  \r\n  // 前端分頁相關（每頁20個項目）\r\n  itemsPerPage: number = 20;\r\n  paginatedImages: ImageItem[] = [];\r\n  Math = Math; // 在模板中使用 Math 對象\r\n\r\n  // 綁定模式相關屬性\r\n  categoryOptions = [\r\n    { value: 1, label: '建材圖片' },\r\n    { value: 2, label: '示意圖片' }\r\n  ];\r\n  selectedCategory: number = 1;\r\n  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private pictureService: PictureService,\r\n    private messageService: MessageService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadImages();\r\n    this.updatePaginatedImages();\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // 當輸入參數變化時重新載入圖片\r\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 載入圖片的主要方法\r\n  loadImages(): void {\r\n    if (!this.buildCaseId && !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n  // 載入可選擇的圖片\r\n  loadAvailableImages(): void {\r\n    if (!this.buildCaseId) {\r\n      this.availableImages = [];\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: this.currentPage,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchTerm || undefined\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 轉換 API 回應為 ImageItem 格式\r\n          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n\r\n          this.totalRecords = res.TotalItems || 0;\r\n\r\n          // 排除已選擇的圖片\r\n          const selectedIds = this.selectedImages.map(img => img.id);\r\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\r\n\r\n          // 合併可選和已選圖片作為完整圖片列表\r\n          this.images = [...this.availableImages, ...this.selectedImages];\r\n\r\n          // 更新分頁顯示\r\n          this.updatePaginatedImages();\r\n\r\n          // 設定初始預覽圖片\r\n          this.setInitialPreviewImage();\r\n        } else {\r\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 載入已選擇的圖片（如果有 materialId）\r\n  loadSelectedImages(): void {\r\n    if (!this.buildCaseId || !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CMaterialId: this.materialId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: 1,\r\n        PageSize: 999 // 載入所有已選圖片\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入已選擇圖片失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 設定初始預覽圖片\r\n  setInitialPreviewImage(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    } else {\r\n      this.previewingImage = null;\r\n      this.currentPreviewIndex = 0;\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    // 如果還沒有圖片，先載入圖片\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    // 開啟對話框\r\n    const template = imagePreviewRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 使用 CGuid 取得圖片資料的方法\r\n  getPictureByGuid(guid: string): void {\r\n    if (!guid) {\r\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\r\n      .subscribe({\r\n        next: (response) => {\r\n          // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\r\n          console.log('取得圖片資料:', response);\r\n        },\r\n        error: (error) => {\r\n          this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 獲取圖片 URL，優先使用 CGuid\r\n  getImageUrl(imageItem: ImageItem): string {\r\n    if (imageItem.guid && !imageItem.fullUrl) {\r\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\r\n      this.getPictureByGuid(imageItem.guid);\r\n    }\r\n\r\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\r\n  }\r\n\r\n  // 綁定模式相關方法\r\n  onCategoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.currentPage = 1; // 重置到第一頁\r\n    this.categoryChange.emit(category);\r\n    if (this.buildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 開啟綁定界面\r\n  openBindingInterface(imageBindingRef?: TemplateRef<any>) {\r\n    this.showBindingInterface = true;\r\n    this.tempSelectedImages = [...this.selectedImages];\r\n\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    const template = imageBindingRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 確認圖片綁定\r\n  onConfirmBinding() {\r\n    this.confirmImageBinding.emit(this.tempSelectedImages);\r\n    this.showBindingInterface = false;\r\n  }\r\n\r\n  // 取消綁定\r\n  onCancelBinding() {\r\n    this.tempSelectedImages = [];\r\n    this.showBindingInterface = false;\r\n    this.onClose();\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n    } else {\r\n      this.tempSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否被選中\r\n  isImageTempSelected(image: ImageItem): boolean {\r\n    return this.tempSelectedImages.some(img => img.id === image.id);\r\n  }\r\n\r\n  // 分頁相關方法\r\n  get totalPages(): number {\r\n    return Math.ceil(this.availableImages.length / this.itemsPerPage);\r\n  }\r\n\r\n  // 更新分頁顯示的圖片\r\n  updatePaginatedImages(): void {\r\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n    const endIndex = startIndex + this.itemsPerPage;\r\n    this.paginatedImages = this.availableImages.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 跳轉到指定頁面\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {\r\n      this.currentPage = page;\r\n      this.updatePaginatedImages();\r\n      // 滾動到頂部\r\n      const scrollContainer = document.querySelector('.flex-1[style*=\"overflow-y: auto\"]');\r\n      if (scrollContainer) {\r\n        scrollContainer.scrollTop = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取可見的頁碼\r\n  getVisiblePages(): number[] {\r\n    const totalPages = this.totalPages;\r\n    const currentPage = this.currentPage;\r\n    const maxVisiblePages = 5;\r\n    \r\n    if (totalPages <= maxVisiblePages) {\r\n      return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n    }\r\n    \r\n    const halfVisible = Math.floor(maxVisiblePages / 2);\r\n    let startPage = Math.max(1, currentPage - halfVisible);\r\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\r\n    \r\n    // 調整開始頁面以確保顯示足夠的頁碼\r\n    if (endPage - startPage + 1 < maxVisiblePages) {\r\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\r\n    }\r\n    \r\n    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n  }\r\n}\r\n", "<!-- 圖片預覽/綁定對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <!-- 綁定模式 -->\r\n  <nb-card *ngIf=\"showBindingInterface\" class=\"w-[90vw] max-w-[1000px] h-[80vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ materialName || '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"d-flex flex-column\" style=\"height: calc(100% - 120px); overflow: hidden;\">\r\n      \r\n      <!-- 控制區 -->\r\n      <div class=\"d-flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">圖片類別</label>\r\n          <nb-select [(selected)]=\"selectedCategory\" placeholder=\"選擇圖片類別\"\r\n            (selectedChange)=\"onCategoryChanged($event)\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"搜尋圖片名稱...\" \r\n                 [(ngModel)]=\"searchTerm\" \r\n                 (input)=\"onSearchChanged()\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center py-4\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n\r\n      <!-- 圖片網格 -->\r\n      <div *ngIf=\"!isLoading\" class=\"flex-1 d-flex flex-column\" style=\"overflow: hidden;\">\r\n        <!-- 圖片顯示區域 -->\r\n        <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n          <div class=\"row g-3\">\r\n            <div *ngFor=\"let image of paginatedImages\" class=\"col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6\">\r\n              <div class=\"card h-100 shadow-sm border-0\" \r\n                   [class.ring-2]=\"isImageTempSelected(image)\"\r\n                   [class.ring-primary]=\"isImageTempSelected(image)\"\r\n                   style=\"transition: all 0.2s ease-in-out; cursor: pointer;\"\r\n                   (click)=\"toggleImageSelection(image)\">\r\n                <!-- 圖片預覽 -->\r\n                <div class=\"position-relative overflow-hidden\" style=\"height: 140px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\">\r\n                  <img *ngIf=\"getImageUrl(image)\" \r\n                       [src]=\"getImageUrl(image)\" \r\n                       [alt]=\"image.name\"\r\n                       class=\"img-fluid w-100 h-100\" \r\n                       style=\"object-fit: cover; transition: transform 0.2s ease-in-out;\"\r\n                       [style.transform]=\"isImageTempSelected(image) ? 'scale(1.05)' : 'scale(1)'\" />\r\n                  <div *ngIf=\"!getImageUrl(image)\" class=\"d-flex align-items-center justify-content-center h-100 text-muted\">\r\n                    <div class=\"text-center\">\r\n                      <i class=\"fas fa-image fa-2x mb-2 opacity-50\"></i>\r\n                      <div class=\"small\">無預覽</div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <!-- 選擇狀態指示器 -->\r\n                  <div *ngIf=\"isImageTempSelected(image)\" \r\n                       class=\"position-absolute top-0 end-0 m-2 bg-primary text-white rounded-circle d-flex align-items-center justify-content-center\"\r\n                       style=\"width: 24px; height: 24px; font-size: 12px;\">\r\n                    <i class=\"fas fa-check\"></i>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- 圖片資訊 -->\r\n                <div class=\"card-body p-2\">\r\n                  <div class=\"small text-truncate fw-medium mb-1\" [title]=\"image.name\">{{ image.name }}</div>\r\n                  <div class=\"d-flex justify-content-between align-items-center\">\r\n                    <span class=\"badge\" \r\n                          [class.bg-success]=\"isImageTempSelected(image)\"\r\n                          [class.bg-light]=\"!isImageTempSelected(image)\"\r\n                          [class.text-dark]=\"!isImageTempSelected(image)\">\r\n                      {{ isImageTempSelected(image) ? '已選' : '點擊選取' }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 空狀態 -->\r\n          <div *ngIf=\"availableImages.length === 0\" class=\"text-center py-5 text-muted\">\r\n            <i class=\"fas fa-images fa-3x mb-3 opacity-50\"></i>\r\n            <h5 class=\"text-muted mb-2\">找不到圖片</h5>\r\n            <p class=\"small text-muted\">請嘗試調整搜尋條件或選擇不同的圖片類別</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分頁控制 -->\r\n        <div *ngIf=\"availableImages.length > 0\" class=\"mt-3 pt-3 border-top\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <!-- 頁面資訊 -->\r\n            <div class=\"text-muted small\">\r\n              顯示第 {{ (currentPage - 1) * itemsPerPage + 1 }} - {{ Math.min(currentPage * itemsPerPage, availableImages.length) }} 筆，\r\n              共 {{ availableImages.length }} 筆圖片\r\n            </div>\r\n            \r\n            <!-- 分頁按鈕 -->\r\n            <nav aria-label=\"圖片分頁\">\r\n              <ul class=\"pagination pagination-sm mb-0\">\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">\r\n                    <i class=\"fas fa-angle-double-left\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">\r\n                    <i class=\"fas fa-angle-left\"></i>\r\n                  </button>\r\n                </li>\r\n                \r\n                <!-- 頁碼 -->\r\n                <li *ngFor=\"let page of getVisiblePages()\" \r\n                    class=\"page-item\" \r\n                    [class.active]=\"page === currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n                </li>\r\n                \r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\">\r\n                    <i class=\"fas fa-angle-right\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\">\r\n                    <i class=\"fas fa-angle-double-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇狀態 -->\r\n      <div class=\"flex-shrink-0 mt-3 text-center\">\r\n        已選擇 {{ tempSelectedImages.length }} 張圖片\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onCancelBinding(); ref.close()\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onConfirmBinding(); ref.close()\" \r\n        [disabled]=\"tempSelectedImages.length === 0\">\r\n        確定選擇 ({{ tempSelectedImages.length }})\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n\r\n  <!-- 預覽模式 -->\r\n  <nb-card *ngIf=\"!showBindingInterface\" class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n      \r\n      <!-- 圖片顯示 -->\r\n      <img *ngIf=\"!isLoading && previewingImage && getImageUrl(previewingImage)\" \r\n        [src]=\"getImageUrl(previewingImage)\"\r\n        [alt]=\"previewingImage.name\" \r\n        class=\"max-w-full max-h-full object-contain\" />\r\n      \r\n      <!-- 無圖片狀態 -->\r\n      <div *ngIf=\"!isLoading && (!previewingImage || !getImageUrl(previewingImage))\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>無可預覽的圖片</div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAmD,eAAe;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAA0BC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;ICY1DC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAYNT,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAeMH,EAAA,CAAAU,SAAA,cAKmF;;;;;IAA9EV,EAAA,CAAAW,WAAA,cAAAC,MAAA,CAAAC,mBAAA,CAAAC,QAAA,+BAA2E;IAH3Ed,EADA,CAAAI,UAAA,QAAAQ,MAAA,CAAAG,WAAA,CAAAD,QAAA,GAAAd,EAAA,CAAAgB,aAAA,CAA0B,QAAAF,QAAA,CAAAG,IAAA,CACR;;;;;IAKrBjB,EADF,CAAAC,cAAA,cAA2G,cAChF;IACvBD,EAAA,CAAAU,SAAA,YAAkD;IAClDV,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAE1BF,EAF0B,CAAAG,YAAA,EAAM,EACxB,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,cAEyD;IACvDD,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;;;;;;IAzBVH,EADF,CAAAC,cAAA,cAA6F,cAKhD;IAAtCD,EAAA,CAAAkB,UAAA,mBAAAC,yFAAA;MAAA,MAAAL,QAAA,GAAAd,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAa,oBAAA,CAAAX,QAAA,CAA2B;IAAA,EAAC;IAExCd,EAAA,CAAAC,cAAA,cAAqI;IAenID,EAdA,CAAA0B,UAAA,IAAAC,yEAAA,kBAKmF,IAAAC,yEAAA,kBACwB,IAAAC,yEAAA,kBAUlD;IAG3D7B,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA2B,cAC4C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEzFH,EADF,CAAAC,cAAA,cAA+D,gBAIP;IACpDD,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAO,EACH,EACF,EACF,EACF;;;;;IAxCCH,EAAA,CAAAO,SAAA,EAA2C;IAC3CP,EADA,CAAA8B,WAAA,WAAAlB,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EAA2C,iBAAAF,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EACM;IAK5Cd,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAG,WAAA,CAAAD,QAAA,EAAwB;IAMxBd,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAQ,MAAA,CAAAG,WAAA,CAAAD,QAAA,EAAyB;IAQzBd,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EAAgC;IASUd,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAU,QAAA,CAAAG,IAAA,CAAoB;IAACjB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAjB,QAAA,CAAAG,IAAA,CAAgB;IAG7EjB,EAAA,CAAAO,SAAA,GAA+C;IAE/CP,EAFA,CAAA8B,WAAA,eAAAlB,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EAA+C,cAAAF,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EACD,eAAAF,MAAA,CAAAC,mBAAA,CAAAC,QAAA,EACC;IACnDd,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAI,MAAA,CAAAC,mBAAA,CAAAC,QAAA,qDACF;;;;;IAQVd,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAU,SAAA,YAAmD;IACnDV,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAE,MAAA,yHAAmB;IACjDF,EADiD,CAAAG,YAAA,EAAI,EAC/C;;;;;;IA8BEH,EAHF,CAAAC,cAAA,aAE0C,iBACW;IAAzBD,EAAA,CAAAkB,UAAA,mBAAAc,kGAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAoB,aAAA,CAAAc,GAAA,EAAAZ,SAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAuB,QAAA,CAAAF,OAAA,CAAc;IAAA,EAAC;IAACjC,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFDH,EAAA,CAAA8B,WAAA,WAAAG,OAAA,KAAArB,MAAA,CAAAwB,WAAA,CAAqC;IACYpC,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAA+B,iBAAA,CAAAE,OAAA,CAAU;;;;;;IAvBnEjC,EAHJ,CAAAC,cAAA,cAAqE,cACJ,cAE/B;IAC5BD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAMAH,EAHN,CAAAC,cAAA,cAAuB,aACqB,aACmB,iBACsB;IAArDD,EAAA,CAAAkB,UAAA,mBAAAmB,4FAAA;MAAArC,EAAA,CAAAoB,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAuB,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7CnC,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,aAA2D,kBACoC;IAAnED,EAAA,CAAAkB,UAAA,mBAAAqB,6FAAA;MAAAvC,EAAA,CAAAoB,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAAwB,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAC3DpC,EAAA,CAAAU,SAAA,aAAiC;IAErCV,EADE,CAAAG,YAAA,EAAS,EACN;IAGLH,EAAA,CAAA0B,UAAA,KAAAc,yEAAA,iBAE0C;IAKxCxC,EADF,CAAAC,cAAA,cAAoE,kBACoC;IAA5ED,EAAA,CAAAkB,UAAA,mBAAAuB,6FAAA;MAAAzC,EAAA,CAAAoB,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAAwB,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAC3DpC,EAAA,CAAAU,SAAA,aAAkC;IAEtCV,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,cAAoE,kBAC+B;IAAvED,EAAA,CAAAkB,UAAA,mBAAAwB,6FAAA;MAAA1C,EAAA,CAAAoB,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAuB,QAAA,CAAAvB,MAAA,CAAA+B,UAAA,CAAoB;IAAA,EAAC;IACtD3C,EAAA,CAAAU,SAAA,aAAyC;IAMrDV,EALU,CAAAG,YAAA,EAAS,EACN,EACF,EACD,EACF,EACF;;;;IAtCAH,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAA4C,kBAAA,0BAAAhC,MAAA,CAAAwB,WAAA,QAAAxB,MAAA,CAAAiC,YAAA,aAAAjC,MAAA,CAAAkC,IAAA,CAAAC,GAAA,CAAAnC,MAAA,CAAAwB,WAAA,GAAAxB,MAAA,CAAAiC,YAAA,EAAAjC,MAAA,CAAAoC,eAAA,CAAAC,MAAA,4BAAArC,MAAA,CAAAoC,eAAA,CAAAC,MAAA,yBAEF;IAK0BjD,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA8B,WAAA,aAAAlB,MAAA,CAAAwB,WAAA,OAAoC;IACRpC,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAwB,WAAA,OAA8B;IAI1DpC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA8B,WAAA,aAAAlB,MAAA,CAAAwB,WAAA,OAAoC;IACMpC,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAwB,WAAA,OAA8B;IAMzEpC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,YAAAQ,MAAA,CAAAsC,eAAA,GAAoB;IAMnBlD,EAAA,CAAAO,SAAA,EAA6C;IAA7CP,EAAA,CAAA8B,WAAA,aAAAlB,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAA6C;IACH3C,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAAuC;IAIjF3C,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAA8B,WAAA,aAAAlB,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAA6C;IACR3C,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAwB,WAAA,KAAAxB,MAAA,CAAA+B,UAAA,CAAuC;;;;;IA1FxG3C,EAHJ,CAAAC,cAAA,cAAoF,cAEpC,cACvB;IACnBD,EAAA,CAAA0B,UAAA,IAAAyB,mEAAA,oBAA6F;IA2C/FnD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA0B,UAAA,IAAA0B,mEAAA,kBAA8E;IAKhFpD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA0B,UAAA,IAAA2B,mEAAA,oBAAqE;IA2CvErD,EAAA,CAAAG,YAAA,EAAM;;;;IAjGuBH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAQ,MAAA,CAAA0C,eAAA,CAAkB;IA8CrCtD,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAoC,eAAA,CAAAC,MAAA,OAAkC;IAQpCjD,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAoC,eAAA,CAAAC,MAAA,KAAgC;;;;;;IAzF1CjD,EADF,CAAAC,cAAA,iBAA+E,qBAC7D;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,sBAA+F,aAGhD,aACvB,eACQ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,mBAC+C;IADpCD,EAAA,CAAAuD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAzD,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAA9C,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAAvB,EAAA,CAAA2D,kBAAA,CAAA/C,MAAA,CAAAgD,gBAAA,EAAAH,MAAA,MAAA7C,MAAA,CAAAgD,gBAAA,GAAAH,MAAA;MAAA,OAAAzD,EAAA,CAAAwB,WAAA,CAAAiC,MAAA;IAAA,EAA+B;IACxCzD,EAAA,CAAAkB,UAAA,4BAAAsC,2FAAAC,MAAA;MAAAzD,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAA9C,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAkBZ,MAAA,CAAAiD,iBAAA,CAAAJ,MAAA,CAAyB;IAAA,EAAC;IAC5CzD,EAAA,CAAA0B,UAAA,IAAAoC,kEAAA,uBAAyE;IAI7E9D,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,iBAEqC;IAD9BD,EAAA,CAAAuD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAzD,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAA9C,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAAvB,EAAA,CAAA2D,kBAAA,CAAA/C,MAAA,CAAAoD,UAAA,EAAAP,MAAA,MAAA7C,MAAA,CAAAoD,UAAA,GAAAP,MAAA;MAAA,OAAAzD,EAAA,CAAAwB,WAAA,CAAAiC,MAAA;IAAA,EAAwB;IACxBzD,EAAA,CAAAkB,UAAA,mBAAA+C,+EAAA;MAAAjE,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAA9C,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAsD,eAAA,EAAiB;IAAA,EAAC;IAEtClE,EAJI,CAAAG,YAAA,EAEqC,EACjC,EACF;IASNH,EANA,CAAA0B,UAAA,KAAAyC,6DAAA,kBAAgD,KAAAC,6DAAA,kBAMoC;IAwGpFpE,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAuD,kBACsB;IAAzCD,EAAA,CAAAkB,UAAA,mBAAAmD,gFAAA;MAAArE,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAAY,MAAA,GAAAtE,EAAA,CAAAuB,aAAA,GAAAgD,SAAA;MAAA,MAAA3D,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAASX,MAAA,CAAA4D,eAAA,EAAiB;MAAA,OAAAxE,EAAA,CAAAwB,WAAA,CAAE8C,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAACzE,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtFH,EAAA,CAAAC,cAAA,kBAC+C;IADfD,EAAA,CAAAkB,UAAA,mBAAAwD,gFAAA;MAAA1E,EAAA,CAAAoB,aAAA,CAAAsC,GAAA;MAAA,MAAAY,MAAA,GAAAtE,EAAA,CAAAuB,aAAA,GAAAgD,SAAA;MAAA,MAAA3D,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAASX,MAAA,CAAA+D,gBAAA,EAAkB;MAAA,OAAA3E,EAAA,CAAAwB,WAAA,CAAE8C,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEvEzE,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;IAjJNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAI,MAAA,CAAAgE,YAAA,gDACF;IAOiB5E,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAA6E,gBAAA,aAAAjE,MAAA,CAAAgD,gBAAA,CAA+B;IAEV5D,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAQ,MAAA,CAAAkE,eAAA,CAAkB;IAQ3C9E,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAA6E,gBAAA,YAAAjE,MAAA,CAAAoD,UAAA,CAAwB;IAM7BhE,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAmE,SAAA,CAAe;IAMf/E,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,UAAAQ,MAAA,CAAAmE,SAAA,CAAgB;IAyGpB/E,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAI,MAAA,CAAAoE,kBAAA,CAAA/B,MAAA,yBACF;IAKEjD,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAoE,kBAAA,CAAA/B,MAAA,OAA4C;IAC5CjD,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAI,MAAA,CAAAoE,kBAAA,CAAA/B,MAAA,OACF;;;;;IAqBAjD,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAGNH,EAAA,CAAAU,SAAA,cAGiD;;;;IAD/CV,EADA,CAAAI,UAAA,QAAAQ,MAAA,CAAAG,WAAA,CAAAH,MAAA,CAAAqE,eAAA,GAAAjF,EAAA,CAAAgB,aAAA,CAAoC,QAAAJ,MAAA,CAAAqE,eAAA,CAAAhE,IAAA,CACR;;;;;IAI9BjB,EAAA,CAAAC,cAAA,cAAiH;IAC/GD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACdF,EADc,CAAAG,YAAA,EAAM,EACd;;;;;;IAQJH,EAAA,CAAAC,cAAA,iBAA2G;IAAnCD,EAAA,CAAAkB,UAAA,mBAAAgE,yFAAA;MAAAlF,EAAA,CAAAoB,aAAA,CAAA+D,IAAA;MAAA,MAAAvE,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAAwE,sBAAA,EAAwB;IAAA,EAAC;IACxGpF,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAI,MAAA,CAAAqE,eAAA,IAAArE,MAAA,CAAAyE,eAAA,CAAAzE,MAAA,CAAAqE,eAAA,uEACF;;;;;;IAvCFjF,EAFJ,CAAAC,cAAA,kBAAmE,yBACS,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,iBACyF;IAA5BD,EAAA,CAAAkB,UAAA,mBAAAoE,+EAAA;MAAAtF,EAAA,CAAAoB,aAAA,CAAAmE,IAAA;MAAA,MAAA3E,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAA4E,eAAA,EAAiB;IAAA,EAAC;IAC9GxF,EAAA,CAAAU,SAAA,YAAmC;IAACV,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC0B;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAuE,+EAAA;MAAAzF,EAAA,CAAAoB,aAAA,CAAAmE,IAAA;MAAA,MAAA3E,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASZ,MAAA,CAAA8E,WAAA,EAAa;IAAA,EAAC;IACvB1F,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAU,SAAA,aAAoC;IAG9CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAkG;IAchGD,EAZA,CAAA0B,UAAA,KAAAiE,6DAAA,kBAA2C,KAAAC,6DAAA,kBASM,KAAAC,6DAAA,kBAGgE;IAInH7F,EAAA,CAAAG,YAAA,EAAe;IAGbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAA0B,UAAA,KAAAoE,gEAAA,qBAA2G;IAG3G9F,EAAA,CAAAC,cAAA,kBAAuE;IAAjCD,EAAA,CAAAkB,UAAA,mBAAA6E,gFAAA;MAAA/F,EAAA,CAAAoB,aAAA,CAAAmE,IAAA;MAAA,MAAAjB,MAAA,GAAAtE,EAAA,CAAAuB,aAAA,GAAAgD,SAAA;MAAA,MAAA3D,MAAA,GAAAZ,EAAA,CAAAuB,aAAA;MAASX,MAAA,CAAAoF,OAAA,EAAS;MAAA,OAAAhG,EAAA,CAAAwB,WAAA,CAAE8C,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAACzE,EAAA,CAAAE,MAAA,oBAAE;IAG/EF,EAH+E,CAAAG,YAAA,EAAS,EAC9E,EACS,EACT;;;;IA3CAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAI,MAAA,CAAAqE,eAAA,kBAAArE,MAAA,CAAAqE,eAAA,CAAAhE,IAAA,KAAkC;IAESjB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAqF,mBAAA,MAAqC;IAGrCjG,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAQ,MAAA,CAAAqF,mBAAA,IAAArF,MAAA,CAAAsF,MAAA,CAAAjD,MAAA,KAAqD;IAShGjD,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAmE,SAAA,CAAe;IAMf/E,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,UAAA,UAAAQ,MAAA,CAAAmE,SAAA,IAAAnE,MAAA,CAAAqE,eAAA,IAAArE,MAAA,CAAAG,WAAA,CAAAH,MAAA,CAAAqE,eAAA,EAAmE;IAMnEjF,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAI,UAAA,UAAAQ,MAAA,CAAAmE,SAAA,MAAAnE,MAAA,CAAAqE,eAAA,KAAArE,MAAA,CAAAG,WAAA,CAAAH,MAAA,CAAAqE,eAAA,GAAuE;IAQ3EjF,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAmG,kBAAA,MAAAvF,MAAA,CAAAqF,mBAAA,aAAArF,MAAA,CAAAsF,MAAA,CAAAjD,MAAA,MACF;IAEWjD,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAwF,mBAAA,CAAyB;;;;;IAvCxCpG,EAtJA,CAAA0B,UAAA,IAAA2E,sDAAA,sBAA+E,IAAAC,sDAAA,sBAsJZ;;;;IAtJzDtG,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAA2F,oBAAA,CAA0B;IAsJ1BvG,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,UAAAQ,MAAA,CAAA2F,oBAAA,CAA2B;;;AD/HvC,OAAM,MAAOC,qBAAqB;EAoDhCC,YACUC,aAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAtDf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAX,mBAAmB,GAAY,IAAI;IAQ5C;IACS,KAAAG,oBAAoB,GAAY,KAAK;IAGpC,KAAAS,oBAAoB,GAAG,IAAIpH,YAAY,EAAa;IACpD,KAAA6E,KAAK,GAAG,IAAI7E,YAAY,EAAQ;IAChC,KAAAqH,aAAa,GAAG,IAAIrH,YAAY,EAAU;IAC1C,KAAAsH,SAAS,GAAG,IAAItH,YAAY,EAAU;IAEhD;IACU,KAAAuH,mBAAmB,GAAG,IAAIvH,YAAY,EAAe;IACrD,KAAAwH,cAAc,GAAG,IAAIxH,YAAY,EAAU;IAIrD;IACA,KAAAsG,MAAM,GAAgB,EAAE;IACxB,KAAAlD,eAAe,GAAgB,EAAE;IACjC,KAAAiC,eAAe,GAAqB,IAAI;IACxC,KAAAgB,mBAAmB,GAAW,CAAC;IAC/B,KAAAlB,SAAS,GAAY,KAAK;IAE1B;IACA,KAAA3C,WAAW,GAAW,CAAC;IACvB,KAAAiF,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,CAAC;IAExB;IACA,KAAAzE,YAAY,GAAW,EAAE;IACzB,KAAAS,eAAe,GAAgB,EAAE;IACjC,KAAAR,IAAI,GAAGA,IAAI,CAAC,CAAC;IAEb;IACA,KAAAgC,eAAe,GAAG,CAChB;MAAExE,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IACD,KAAAmD,gBAAgB,GAAW,CAAC;IAC5B,KAAAoB,kBAAkB,GAAgB,EAAE,CAAC,CAAC;EAMlC;EAEJuC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MACzE,IAAI,CAACN,UAAU,EAAE;IACnB;EACF;EAEA;EACAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACG,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgD,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;MACrB,IAAI,CAAC3E,eAAe,GAAG,EAAE;MACzB;IACF;IAEA,IAAI,CAAC2D,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9BS,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,IAAI,CAACjG,WAAW;QAC3BkG,QAAQ,EAAE,IAAI,CAACjB,QAAQ;QACvBkB,KAAK,EAAE,IAAI,CAACvE,UAAU,IAAI8D;;KAE7B,CAAC,CAACU,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YACvEC,EAAE,EAAED,OAAO,CAACE,GAAG,IAAI,CAAC;YACpBhI,IAAI,EAAE8H,OAAO,CAACG,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEP,OAAO,CAACM,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEX,OAAO,CAACY;WACf,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACrC,YAAY,GAAGoB,GAAG,CAACkB,UAAU,IAAI,CAAC;UAEvC;UACA,MAAMC,WAAW,GAAG,IAAI,CAAC/C,cAAc,CAACgC,GAAG,CAACgB,GAAG,IAAIA,GAAG,CAACd,EAAE,CAAC;UAC1D,IAAI,CAAChG,eAAe,GAAG4F,SAAS,CAACmB,MAAM,CAACC,KAAK,IAAI,CAACH,WAAW,CAACI,QAAQ,CAACD,KAAK,CAAChB,EAAE,CAAC,CAAC;UAEjF;UACA,IAAI,CAAC9C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAClD,eAAe,EAAE,GAAG,IAAI,CAAC8D,cAAc,CAAC;UAE/D;UACA,IAAI,CAACW,qBAAqB,EAAE;UAE5B;UACA,IAAI,CAACyC,sBAAsB,EAAE;QAC/B,CAAC,MAAM;UACL,IAAI,CAACtD,cAAc,CAACuD,YAAY,CAACzB,GAAG,CAAC0B,OAAO,IAAI,QAAQ,CAAC;QAC3D;QACA,IAAI,CAACrF,SAAS,GAAG,KAAK;MACxB,CAAC;MACDsF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,UAAU,IAAIE,KAAK,CAACC,OAAO,IAAI,MAAM,CAAC,CAAC;QACxE,IAAI,CAACvF,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACAiD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACL,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAACjB,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9B4C,WAAW,EAAE,IAAI,CAAC3C,UAAU;QAC5BQ,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACE,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7B,cAAc,GAAG4B,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YAC3EC,EAAE,EAAED,OAAO,CAACE,GAAG,IAAI,CAAC;YACpBhI,IAAI,EAAE8H,OAAO,CAACG,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEP,OAAO,CAACM,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEX,OAAO,CAACY;WACf,CAAC,CAAC,IAAI,EAAE;QACX;MACF,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfG,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACAH,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAChE,MAAM,CAACjD,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACgD,mBAAmB,GAAGnD,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAE3H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACgE,iBAAiB,EAAE,IAAI,CAACb,MAAM,CAACjD,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACgC,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAChB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgB,mBAAmB,GAAG,CAAC;IAC9B;EACF;EAEAT,eAAeA,CAAA;IACb,IAAI,IAAI,CAACS,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACgB,aAAa,CAACyD,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;IACnD;EACF;EAEAP,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAACjD,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACgD,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACiB,SAAS,CAACwD,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;IAC/C;EACF;EAEAb,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACH,eAAe,EAAE;MACxB,IAAI,CAAC+B,oBAAoB,CAAC0D,IAAI,CAAC,IAAI,CAACzF,eAAe,CAAC;IACtD;EACF;EAEAI,eAAeA,CAAC2E,KAAgB;IAC9B,OAAO,IAAI,CAAClD,cAAc,CAAC6D,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAC5B,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;EACvE;EAEAhD,OAAOA,CAAA;IACL,IAAI,CAACvB,KAAK,CAACiG,IAAI,EAAE;EACnB;EAEA;EACAG,WAAWA,CAACC,eAAkC;IAC5C;IACA,IAAI,IAAI,CAAC5E,MAAM,CAACjD,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACuE,UAAU,EAAE;IACnB;IAEA;IACA,MAAMuD,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;IACrD,IAAI,CAACtE,aAAa,CAACuE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAG,gBAAgBA,CAACxB,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAC9C,cAAc,CAACuD,YAAY,CAAC,cAAc,CAAC;MAChD;IACF;IAEA,IAAI,CAACxD,cAAc,CAACwE,2BAA2B,CAAC;MAAEzB,IAAI,EAAEA;IAAI,CAAE,CAAC,CAC5DlB,SAAS,CAAC;MACTC,IAAI,EAAG2C,QAAQ,IAAI;QACjB;QACAZ,OAAO,CAACa,GAAG,CAAC,SAAS,EAAED,QAAQ,CAAC;MAClC,CAAC;MACDf,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,cAAc,CAACuD,YAAY,CAAC,WAAWE,KAAK,CAACC,OAAO,IAAI,MAAM,EAAE,CAAC;MACxE;KACD,CAAC;EACN;EAEA;EACAvJ,WAAWA,CAACuK,SAAoB;IAC9B,IAAIA,SAAS,CAAC5B,IAAI,IAAI,CAAC4B,SAAS,CAAChC,OAAO,EAAE;MACxC;MACA,IAAI,CAAC4B,gBAAgB,CAACI,SAAS,CAAC5B,IAAI,CAAC;IACvC;IAEA,OAAO4B,SAAS,CAAChC,OAAO,IAAIgC,SAAS,CAAClC,YAAY,IAAI,EAAE;EAC1D;EAEA;EACAvF,iBAAiBA,CAAC0H,QAAgB;IAChC,IAAI,CAAC3H,gBAAgB,GAAG2H,QAAQ;IAChC,IAAI,CAACnJ,WAAW,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,CAACgF,cAAc,CAACsD,IAAI,CAACa,QAAQ,CAAC;IAClC,IAAI,IAAI,CAAC5D,WAAW,EAAE;MACpB,IAAI,CAACH,UAAU,EAAE;IACnB;EACF;EAEA;EACAgE,oBAAoBA,CAACC,eAAkC;IACrD,IAAI,CAAClF,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACvB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC8B,cAAc,CAAC;IAElD,IAAI,IAAI,CAACZ,MAAM,CAACjD,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACuE,UAAU,EAAE;IACnB;IAEA,MAAMuD,QAAQ,GAAGU,eAAe,IAAI,IAAI,CAACT,YAAY;IACrD,IAAI,CAACtE,aAAa,CAACuE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACApG,gBAAgBA,CAAA;IACd,IAAI,CAACwC,mBAAmB,CAACuD,IAAI,CAAC,IAAI,CAAC1F,kBAAkB,CAAC;IACtD,IAAI,CAACuB,oBAAoB,GAAG,KAAK;EACnC;EAEA;EACA/B,eAAeA,CAAA;IACb,IAAI,CAACQ,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACuB,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACP,OAAO,EAAE;EAChB;EAEA;EACAvE,oBAAoBA,CAACuI,KAAgB;IACnC,MAAM0B,KAAK,GAAG,IAAI,CAAC1G,kBAAkB,CAAC2G,SAAS,CAAC7B,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;IAC3E,IAAI0C,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC1G,kBAAkB,CAAC4G,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC1G,kBAAkB,CAAC6G,IAAI,CAAC7B,KAAK,CAAC;IACrC;EACF;EAEA;EACAnJ,mBAAmBA,CAACmJ,KAAgB;IAClC,OAAO,IAAI,CAAChF,kBAAkB,CAAC2F,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKgB,KAAK,CAAChB,EAAE,CAAC;EACjE;EAEA;EACA,IAAIrG,UAAUA,CAAA;IACZ,OAAOG,IAAI,CAACgJ,IAAI,CAAC,IAAI,CAAC9I,eAAe,CAACC,MAAM,GAAG,IAAI,CAACJ,YAAY,CAAC;EACnE;EAEA;EACA4E,qBAAqBA,CAAA;IACnB,MAAMsE,UAAU,GAAG,CAAC,IAAI,CAAC3J,WAAW,GAAG,CAAC,IAAI,IAAI,CAACS,YAAY;IAC7D,MAAMmJ,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAClJ,YAAY;IAC/C,IAAI,CAACS,eAAe,GAAG,IAAI,CAACN,eAAe,CAACiJ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACzE;EAEA;EACA7J,QAAQA,CAAC+J,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACvJ,UAAU,IAAIuJ,IAAI,KAAK,IAAI,CAAC9J,WAAW,EAAE;MACrE,IAAI,CAACA,WAAW,GAAG8J,IAAI;MACvB,IAAI,CAACzE,qBAAqB,EAAE;MAC5B;MACA,MAAM0E,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,oCAAoC,CAAC;MACpF,IAAIF,eAAe,EAAE;QACnBA,eAAe,CAACG,SAAS,GAAG,CAAC;MAC/B;IACF;EACF;EAEA;EACApJ,eAAeA,CAAA;IACb,MAAMP,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMP,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMmK,eAAe,GAAG,CAAC;IAEzB,IAAI5J,UAAU,IAAI4J,eAAe,EAAE;MACjC,OAAOC,KAAK,CAACC,IAAI,CAAC;QAAExJ,MAAM,EAAEN;MAAU,CAAE,EAAE,CAAC+J,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IAC5D;IAEA,MAAMC,WAAW,GAAG9J,IAAI,CAAC+J,KAAK,CAACN,eAAe,GAAG,CAAC,CAAC;IACnD,IAAIO,SAAS,GAAGhK,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAErI,WAAW,GAAGwK,WAAW,CAAC;IACtD,IAAIG,OAAO,GAAGjK,IAAI,CAACC,GAAG,CAACJ,UAAU,EAAEmK,SAAS,GAAGP,eAAe,GAAG,CAAC,CAAC;IAEnE;IACA,IAAIQ,OAAO,GAAGD,SAAS,GAAG,CAAC,GAAGP,eAAe,EAAE;MAC7CO,SAAS,GAAGhK,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAEsC,OAAO,GAAGR,eAAe,GAAG,CAAC,CAAC;IACxD;IAEA,OAAOC,KAAK,CAACC,IAAI,CAAC;MAAExJ,MAAM,EAAE8J,OAAO,GAAGD,SAAS,GAAG;IAAC,CAAE,EAAE,CAACJ,CAAC,EAAEC,CAAC,KAAKG,SAAS,GAAGH,CAAC,CAAC;EACjF;;;uCA5VWnG,qBAAqB,EAAAxG,EAAA,CAAAgN,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAlN,EAAA,CAAAgN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApN,EAAA,CAAAgN,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArB9G,qBAAqB;MAAA+G,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCzBlC1N,EAAA,CAAA0B,UAAA,IAAAkM,4CAAA,gCAAA5N,EAAA,CAAA6N,sBAAA,CAA0D;;;qBDuB9ChO,YAAY,EAAAiO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjO,YAAY,EAAAkO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAnB,EAAA,CAAAoB,eAAA,EAAApB,EAAA,CAAAqB,mBAAA,EAAArB,EAAA,CAAAsB,qBAAA,EAAAtB,EAAA,CAAAuB,qBAAA,EAAAvB,EAAA,CAAAwB,iBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAE5O,eAAe,EAAAmN,EAAA,CAAA0B,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}