{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nlet ImagePreviewComponent = class ImagePreviewComponent {\n  constructor(dialogService) {\n    this.dialogService = dialogService;\n    this.isVisible = false;\n    this.images = [];\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n  }\n  ngOnInit() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    }\n  }\n  ngOnChanges() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    if (this.images.length > 0) {\n      this.dialogService.open(imagePreviewRef);\n    }\n  }\n};\n__decorate([Input()], ImagePreviewComponent.prototype, \"isVisible\", void 0);\n__decorate([Input()], ImagePreviewComponent.prototype, \"images\", void 0);\n__decorate([Input()], ImagePreviewComponent.prototype, \"selectedImages\", void 0);\n__decorate([Input()], ImagePreviewComponent.prototype, \"initialImageIndex\", void 0);\n__decorate([Input()], ImagePreviewComponent.prototype, \"showSelectionToggle\", void 0);\n__decorate([Output()], ImagePreviewComponent.prototype, \"imageSelectionToggle\", void 0);\n__decorate([Output()], ImagePreviewComponent.prototype, \"close\", void 0);\n__decorate([Output()], ImagePreviewComponent.prototype, \"previousImage\", void 0);\n__decorate([Output()], ImagePreviewComponent.prototype, \"nextImage\", void 0);\nImagePreviewComponent = __decorate([Component({\n  selector: 'app-image-preview',\n  templateUrl: './image-preview.component.html',\n  styleUrls: ['./image-preview.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], ImagePreviewComponent);\nexport { ImagePreviewComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "CommonModule", "SharedModule", "ImagePreviewComponent", "constructor", "dialogService", "isVisible", "images", "selectedImages", "initialImageIndex", "showSelectionToggle", "imageSelectionToggle", "close", "previousImage", "nextImage", "previewingImage", "currentPreviewIndex", "ngOnInit", "length", "Math", "max", "min", "ngOnChanges", "onPreviousImage", "emit", "onNextImage", "onToggleImageSelection", "isImageSelected", "image", "some", "selected", "id", "onClose", "openPreview", "imagePreviewRef", "open", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { SharedModule } from '../../components/shared.module';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() images: ImageItem[] = [];\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n  \r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n\r\n  constructor(private dialogService: NbDialogService) {}\r\n\r\n  ngOnInit(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef: TemplateRef<any>) {\r\n    if (this.images.length > 0) {\r\n      this.dialogService.open(imagePreviewRef);\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,QAA6B,eAAe;AAC3F,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,gCAAgC;AAmBtD,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAehCC,YAAoBC,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;IAdxB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,MAAM,GAAgB,EAAE;IACxB,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,mBAAmB,GAAY,IAAI;IAElC,KAAAC,oBAAoB,GAAG,IAAIX,YAAY,EAAa;IACpD,KAAAY,KAAK,GAAG,IAAIZ,YAAY,EAAQ;IAChC,KAAAa,aAAa,GAAG,IAAIb,YAAY,EAAU;IAC1C,KAAAc,SAAS,GAAG,IAAId,YAAY,EAAU;IAEhD,KAAAe,eAAe,GAAqB,IAAI;IACxC,KAAAC,mBAAmB,GAAW,CAAC;EAEsB;EAErDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,MAAM,CAACW,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACF,mBAAmB,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACZ,iBAAiB,EAAE,IAAI,CAACF,MAAM,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACH,eAAe,GAAG,IAAI,CAACR,MAAM,CAAC,IAAI,CAACS,mBAAmB,CAAC;IAC9D;EACF;EAEAM,WAAWA,CAAA;IACT,IAAI,IAAI,CAACf,MAAM,CAACW,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACF,mBAAmB,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACZ,iBAAiB,EAAE,IAAI,CAACF,MAAM,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACH,eAAe,GAAG,IAAI,CAACR,MAAM,CAAC,IAAI,CAACS,mBAAmB,CAAC;IAC9D;EACF;EAEAO,eAAeA,CAAA;IACb,IAAI,IAAI,CAACP,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACR,MAAM,CAAC,IAAI,CAACS,mBAAmB,CAAC;MAC5D,IAAI,CAACH,aAAa,CAACW,IAAI,CAAC,IAAI,CAACR,mBAAmB,CAAC;IACnD;EACF;EAEAS,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,mBAAmB,GAAG,IAAI,CAACT,MAAM,CAACW,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACF,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACR,MAAM,CAAC,IAAI,CAACS,mBAAmB,CAAC;MAC5D,IAAI,CAACF,SAAS,CAACU,IAAI,CAAC,IAAI,CAACR,mBAAmB,CAAC;IAC/C;EACF;EAEAU,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACX,eAAe,EAAE;MACxB,IAAI,CAACJ,oBAAoB,CAACa,IAAI,CAAC,IAAI,CAACT,eAAe,CAAC;IACtD;EACF;EAEAY,eAAeA,CAACC,KAAgB;IAC9B,OAAO,IAAI,CAACpB,cAAc,CAACqB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,EAAE,KAAKH,KAAK,CAACG,EAAE,CAAC;EACvE;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACpB,KAAK,CAACY,IAAI,EAAE;EACnB;EAEA;EACAS,WAAWA,CAACC,eAAiC;IAC3C,IAAI,IAAI,CAAC3B,MAAM,CAACW,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACb,aAAa,CAAC8B,IAAI,CAACD,eAAe,CAAC;IAC1C;EACF;CACD;AAlEUE,UAAA,EAARtC,KAAK,EAAE,C,uDAA4B;AAC3BsC,UAAA,EAARtC,KAAK,EAAE,C,oDAA0B;AACzBsC,UAAA,EAARtC,KAAK,EAAE,C,4DAAkC;AACjCsC,UAAA,EAARtC,KAAK,EAAE,C,+DAA+B;AAC9BsC,UAAA,EAARtC,KAAK,EAAE,C,iEAAqC;AAEnCsC,UAAA,EAATrC,MAAM,EAAE,C,kEAAsD;AACrDqC,UAAA,EAATrC,MAAM,EAAE,C,mDAAkC;AACjCqC,UAAA,EAATrC,MAAM,EAAE,C,2DAA4C;AAC3CqC,UAAA,EAATrC,MAAM,EAAE,C,uDAAwC;AAVtCI,qBAAqB,GAAAiC,UAAA,EAPjCvC,SAAS,CAAC;EACTwC,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxC,YAAY,EAAEC,YAAY;CACrC,CAAC,C,EACWC,qBAAqB,CAmEjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}