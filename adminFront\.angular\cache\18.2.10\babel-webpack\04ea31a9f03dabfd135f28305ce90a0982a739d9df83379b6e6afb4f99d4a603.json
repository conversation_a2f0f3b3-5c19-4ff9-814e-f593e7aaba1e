{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\nimport { ImagePreviewComponent } from '../../../shared/components/image-preview';\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nlet BuildingMaterialComponent = class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    this.CMaterialCode = \"\";\n    this.ShowPrice = false;\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 簡化後只保留必要的\n    this.selectedImages = []; // 右側已選擇的圖片\n    this.imageSearchTerm = \"\";\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\n        // 不立即載入材料列表，等待建案選擇事件觸發\n      }\n    })).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        CMaterialCode: this.CMaterialCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  // 建案選擇事件處理（新）\n  onBuildCaseSelectionChange(selectedBuildCase) {\n    if (selectedBuildCase) {\n      this.selectedBuildCaseId = selectedBuildCase.cID;\n    } else if (this.listBuildCases.length > 0) {\n      this.selectedBuildCaseId = this.listBuildCases[0].cID;\n    }\n    this.search();\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1,\n      // 預設為啟用狀態\n      CPrice: 0 // 預設價格為0\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    // 使用 imagePreviewComponent 的綁定界面\n    if (this.imagePreviewComponent) {\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\n      // 監聽圖片綁定確認事件\n      this.imagePreviewComponent.confirmImageBinding.subscribe(selectedImages => {\n        this.onConfirmImageBinding(selectedImages);\n      });\n      this.imagePreviewComponent.openBindingInterface();\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.required('[價格]', this.selectedMaterial.CPrice);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30);\n    // 價格驗證：必須為數字且大於等於0\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\n      if (this.selectedMaterial.CPrice < 0) {\n        this.valid.errorMessages.push('[價格] 不能小於0');\n      }\n    }\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CFile: target.files[0]\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        }), mergeMap(() => this.getMaterialList(1))).subscribe();\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder() {\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    // 使用 imagePreviewComponent 的綁定界面\n    if (this.imagePreviewComponent) {\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\n      // 監聽圖片綁定確認事件\n      this.imagePreviewComponent.confirmImageBinding.subscribe(selectedImages => {\n        this.onConfirmImageBinding(selectedImages);\n      });\n      this.imagePreviewComponent.openBindingInterface();\n    }\n  }\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\n  previewImage(image, imagePreviewComponent, event) {\n    event.stopPropagation();\n    // 設定預覽元件參數，讓它自行載入圖片\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\n    imagePreviewComponent.pictureType = this.selectedCategory;\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\n    imagePreviewComponent.showSelectionToggle = true;\n    // 開啟預覽對話框\n    imagePreviewComponent.openPreview();\n  }\n  onConfirmImageBinding(selectedImages) {\n    if (selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = selectedImages.map(img => img.id);\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    // 清理選擇狀態\n    this.selectedImages = [];\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n};\n__decorate([ViewChild('imagePreviewComponent', {\n  static: false\n})], BuildingMaterialComponent.prototype, \"imagePreviewComponent\", void 0);\nBuildingMaterialComponent = __decorate([Component({\n  selector: 'ngx-building-material',\n  templateUrl: './building-material.component.html',\n  styleUrls: ['./building-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent]\n})], BuildingMaterialComponent);\nexport { BuildingMaterialComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "BuildCaseSelectComponent", "ImagePreviewComponent", "PictureCategory", "BuildingMaterialComponent", "getStatusLabel", "status", "option", "statusOptions", "find", "opt", "value", "label", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "CSelectName", "CMaterialCode", "ShowPrice", "filterMapping", "CIsMapping", "selectedImages", "imageSearchTerm", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "selectedCate<PERSON><PERSON>", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "length", "subscribe", "getMaterialList", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "selectedBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "materialList", "totalRecords", "TotalItems", "CShowPrice", "onBuildCaseSelectionChange", "selectedBuildCase", "cID", "search", "pageChanged", "exportExelMaterialList", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "addNew", "ref", "selectedMaterial", "CPrice", "open", "onSelectedMaterial", "data", "bindImageForMaterial", "imagePreviewComponent", "buildCaseId", "materialId", "CId", "pictureType", "materialName", "undefined", "confirmImageBinding", "onConfirmImageBinding", "openBindingInterface", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "push", "onSubmit", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CDescription", "CMaterialId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "close", "onClose", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "utils", "sheet_to_json", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "changeFilter", "openImageBinder", "previewImage", "image", "stopPropagation", "searchTerm", "showSelectionToggle", "openPreview", "map", "img", "id", "saveImageBinding", "categoryChanged", "category", "getCategoryLabel", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\r\nimport { ImagePreviewComponent, ImageItem } from '../../../shared/components/image-preview';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('imagePreviewComponent', { static: false }) imagePreviewComponent!: ImagePreviewComponent;\r\n\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 簡化後只保留必要的\r\n  selectedImages: ImageItem[] = [] // 右側已選擇的圖片\r\n  imageSearchTerm: string = \"\"\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\r\n            // 不立即載入材料列表，等待建案選擇事件觸發\r\n          }\r\n        })\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        CMaterialCode: this.CMaterialCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  // 建案選擇事件處理（新）\r\n  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {\r\n    if (selectedBuildCase) {\r\n      this.selectedBuildCaseId = selectedBuildCase.cID!;\r\n    } else if (this.listBuildCases.length > 0) {\r\n      this.selectedBuildCaseId = this.listBuildCases[0].cID!;\r\n    }\r\n    this.search();\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1, // 預設為啟用狀態\r\n      CPrice: 0   // 預設價格為0\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse) {\r\n    this.selectedMaterial = { ...data }\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\r\n\r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n\r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.required('[價格]', this.selectedMaterial.CPrice)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\r\n    // 價格驗證：必須為數字且大於等於0\r\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\r\n      if (this.selectedMaterial.CPrice < 0) {\r\n        this.valid.errorMessages.push('[價格] 不能小於0')\r\n      }\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n\r\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CFile: target.files[0]\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG(\"執行成功\")\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n          }),\r\n          mergeMap(() => this.getMaterialList(1))\r\n        ).subscribe();\r\n\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder() {\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\r\n\r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n\r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n  }\r\n\r\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\r\n  previewImage(image: ImageItem, imagePreviewComponent: ImagePreviewComponent, event: Event) {\r\n    event.stopPropagation();\r\n\r\n    // 設定預覽元件參數，讓它自行載入圖片\r\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\r\n    imagePreviewComponent.pictureType = this.selectedCategory;\r\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\r\n    imagePreviewComponent.showSelectionToggle = true;\r\n\r\n    // 開啟預覽對話框\r\n    imagePreviewComponent.openPreview();\r\n  }\r\n\r\n  onConfirmImageBinding(selectedImages: ImageItem[]) {\r\n    if (selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = selectedImages.map(img => img.id);\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    // 清理選擇狀態\r\n    this.selectedImages = [];\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  // 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAuBC,SAAS,QAAQ,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,qBAAqB,QAAmB,0CAA0C;AAE3F;AACA,IAAKC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAcb,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQJ,aAAa;EAmD1D;EACAK,cAAcA,CAACC,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,KAAK;EACtC;EAEAC,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA9DzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEb,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAAa,iBAAiB,GAAG,IAAI;IAC9B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAC,cAAc,GAAgB,EAAE,EAAC;IACjC,KAAAC,eAAe,GAAW,EAAE;IAE5B;IACA,KAAAC,eAAe,GAAG,CAChB;MAAEtB,KAAK,EAAER,eAAe,CAAC+B,iBAAiB;MAAEtB,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAER,eAAe,CAACgC,SAAS;MAAEvB,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAwB,gBAAgB,GAAoBjC,eAAe,CAAC+B,iBAAiB;IACrE,KAAAG,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAAlC,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAAK,aAAa,GAAG,CAAC;MACfG,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAES0B,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACrB,iBAAiB,CAACsB,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACH/C,GAAG,CAACgD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvB,cAAc,GAAGsB,GAAG,CAACE,OAAO,EAAEC,MAAM,GAAGH,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D;QACA;MACF;IACF,CAAC,CAAC,CACH,CAACE,SAAS,EAAE;EACjB;EAAEC,eAAeA,CAACC,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAAChC,gBAAgB,CAACiC,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtCC,QAAQ,EAAE,IAAI,CAAC9B,iBAAiB;QAChCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC6B,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEP,SAAS;QACpBrB,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACc,IAAI,CACL/C,GAAG,CAACgD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACa,YAAY,GAAGd,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACa,YAAY,GAAGf,GAAG,CAACgB,UAAW;QAEnC,IAAI,IAAI,CAACF,YAAY,CAACX,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACpB,SAAS,GAAG,IAAI,CAAC+B,YAAY,CAAC,CAAC,CAAC,CAACG,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA;EACAC,0BAA0BA,CAACC,iBAAiD;IAC1E,IAAIA,iBAAiB,EAAE;MACrB,IAAI,CAACV,mBAAmB,GAAGU,iBAAiB,CAACC,GAAI;IACnD,CAAC,MAAM,IAAI,IAAI,CAAC1C,cAAc,CAACyB,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAAC/B,cAAc,CAAC,CAAC,CAAC,CAAC0C,GAAI;IACxD;IACA,IAAI,CAACC,MAAM,EAAE;EACf;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAChB,eAAe,EAAE,CAACD,SAAS,EAAE;EACpC;EAEAkB,WAAWA,CAAChB,SAAiB;IAC3B,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACF,SAAS,EAAE;EAC7C;EAEAmB,sBAAsBA,CAAA;IACpB,IAAI,CAACjD,gBAAgB,CAACkD,2CAA2C,CAAC;MAChE5B,IAAI,EAAE,IAAI,CAACa;KACZ,CAAC,CAACL,SAAS,CAACJ,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE;UACzB,IAAI,CAAClD,eAAe,CAACmD,iBAAiB,CAAC1B,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACrD,gBAAgB,CAACsD,+CAA+C,CAAC;MACpEhC,IAAI,EAAE,IAAI,CAACa;KACZ,CAAC,CAACL,SAAS,CAACJ,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE;UACzB,IAAI,CAAClD,eAAe,CAACmD,iBAAiB,CAAC1B,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACAI,MAAMA,CAACC,GAAQ;IACb,IAAI,CAACrD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACsD,gBAAgB,GAAG;MACtBjC,OAAO,EAAE,CAAC;MAAE;MACZkC,MAAM,EAAE,CAAC,CAAG;KACb;IACD,IAAI,CAAC9D,aAAa,CAAC+D,IAAI,CAACH,GAAG,CAAC;EAC9B;EACAI,kBAAkBA,CAACC,IAA6B,EAAEL,GAAQ;IACxD,IAAI,CAACrD,KAAK,GAAG,KAAK;IAClB,IAAI,CAACsD,gBAAgB,GAAG;MAAE,GAAGI;IAAI,CAAE;IACnC,IAAI,CAACjE,aAAa,CAAC+D,IAAI,CAACH,GAAG,CAAC;EAC9B;EACAM,oBAAoBA,CAACD,IAA6B;IAChD,IAAI,CAACJ,gBAAgB,GAAG;MAAE,GAAGI;IAAI,CAAE;IACnC;IACA,IAAI,CAACjD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,IAAI,CAACkD,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAAC7B,mBAAmB;MACjE,IAAI,CAAC4B,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACR,gBAAgB,CAACS,GAAG;MACjE,IAAI,CAACH,qBAAqB,CAACI,WAAW,GAAG,IAAI,CAAClD,gBAAgB;MAC9D,IAAI,CAAC8C,qBAAqB,CAACK,YAAY,GAAG,IAAI,CAACX,gBAAgB,CAAClD,WAAW,IAAI8D,SAAS;MAExF;MACA,IAAI,CAACN,qBAAqB,CAACO,mBAAmB,CAACxC,SAAS,CAAElB,cAA2B,IAAI;QACvF,IAAI,CAAC2D,qBAAqB,CAAC3D,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAACmD,qBAAqB,CAACS,oBAAoB,EAAE;IACnD;EACF;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC3E,KAAK,CAAC4E,KAAK,EAAE;IAElB,IAAI,CAAC5E,KAAK,CAAC6E,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClB,gBAAgB,CAAClD,WAAW,CAAC;IAClE;IACA,IAAI,CAACT,KAAK,CAAC6E,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClB,gBAAgB,CAACjD,aAAa,CAAC;IAClE,IAAI,CAACV,KAAK,CAAC6E,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClB,gBAAgB,CAACjC,OAAO,CAAC;IAC1D,IAAI,CAAC1B,KAAK,CAAC6E,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClB,gBAAgB,CAACC,MAAM,CAAC;IACzD,IAAI,CAAC5D,KAAK,CAAC8E,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACnB,gBAAgB,CAAClD,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACT,KAAK,CAAC8E,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACnB,gBAAgB,CAACjD,aAAa,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,IAAI,CAACiD,gBAAgB,CAACC,MAAM,KAAKW,SAAS,IAAI,IAAI,CAACZ,gBAAgB,CAACC,MAAM,KAAK,IAAI,EAAE;MACvF,IAAI,IAAI,CAACD,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAAC5D,KAAK,CAAC+E,aAAa,CAACC,IAAI,CAAC,YAAY,CAAC;MAC7C;IACF;EACF;EAEAC,QAAQA,CAACvB,GAAQ;IACf,IAAI,CAACiB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3E,KAAK,CAAC+E,aAAa,CAAChD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChC,OAAO,CAACmF,aAAa,CAAC,IAAI,CAAClF,KAAK,CAAC+E,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC7E,gBAAgB,CAACiF,qCAAqC,CAAC;MAC5D3D,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC;QACA3B,aAAa,EAAE,IAAI,CAACiD,gBAAgB,CAACjD,aAAa;QAClDD,WAAW,EAAE,IAAI,CAACkD,gBAAgB,CAAClD,WAAW;QAC9C2E,YAAY,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAAChF,KAAK,GAAG,IAAI,GAAG,IAAI,CAACsD,gBAAgB,CAACS,GAAI;QAC3DR,MAAM,EAAE,IAAI,CAACD,gBAAgB,CAACC,MAAM;QACpClC,OAAO,EAAE,IAAI,CAACiC,gBAAgB,CAACjC,OAAO;QAAE;QACxC4D,UAAU,EAAG,IAAI,CAAC3B,gBAAwB,CAAC4B,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACC5D,IAAI,CACH/C,GAAG,CAACgD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACzF,OAAO,CAAC0F,YAAY,CAAC7D,GAAG,CAAC8D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACF/G,QAAQ,CAAC,MAAM,IAAI,CAACsD,eAAe,EAAE,CAAC,EACtCvD,QAAQ,CAAC,MAAMgF,GAAG,CAACiC,KAAK,EAAE,CAAC,CAC5B,CAAC3D,SAAS,EAAE;EACjB;EAEA4D,OAAOA,CAAClC,GAAQ;IACdA,GAAG,CAACiC,KAAK,EAAE;EACb;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkB3H,IAAI,CAAC4H,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,MAAM5C,IAAI,GAAGlF,IAAI,CAACkI,KAAK,CAACC,aAAa,CAACH,EAAE,CAAC;MACzC,IAAI9C,IAAI,IAAIA,IAAI,CAAChC,MAAM,GAAG,CAAC,EAAE;QAE3B,IAAI,CAAC7B,gBAAgB,CAAC+G,2CAA2C,CAAC;UAChEzF,IAAI,EAAE;YACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;YACtC6E,KAAK,EAAEnB,MAAM,CAACI,KAAK,CAAC,CAAC;;SAExB,CAAC,CAACxE,IAAI,CACL/C,GAAG,CAACgD,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC9B,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACzF,OAAO,CAAC0F,YAAY,CAAC7D,GAAG,CAAC8D,OAAQ,CAAC;UACzC;QACF,CAAC,CAAC,EACF/G,QAAQ,CAAC,MAAM,IAAI,CAACsD,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACD,SAAS,EAAE;MAEf,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAAC0F,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAK,KAAK,CAACC,MAAM,CAACrG,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAyH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACvG,aAAa,EAAE;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACoB,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACnB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoB,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC;EACF;EACA;EACAoF,eAAeA,CAAA;IACb;IACA,IAAI,CAACtG,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,IAAI,CAACkD,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAAC7B,mBAAmB;MACjE,IAAI,CAAC4B,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACR,gBAAgB,CAACS,GAAG;MACjE,IAAI,CAACH,qBAAqB,CAACI,WAAW,GAAG,IAAI,CAAClD,gBAAgB;MAC9D,IAAI,CAAC8C,qBAAqB,CAACK,YAAY,GAAG,IAAI,CAACX,gBAAgB,CAAClD,WAAW,IAAI8D,SAAS;MAExF;MACA,IAAI,CAACN,qBAAqB,CAACO,mBAAmB,CAACxC,SAAS,CAAElB,cAA2B,IAAI;QACvF,IAAI,CAAC2D,qBAAqB,CAAC3D,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAACmD,qBAAqB,CAACS,oBAAoB,EAAE;IACnD;EACF;EAEA;EACA2C,YAAYA,CAACC,KAAgB,EAAErD,qBAA4C,EAAE6B,KAAY;IACvFA,KAAK,CAACyB,eAAe,EAAE;IAEvB;IACAtD,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAAC7B,mBAAmB;IAC5D4B,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACR,gBAAgB,EAAES,GAAG;IAC7DH,qBAAqB,CAACI,WAAW,GAAG,IAAI,CAAClD,gBAAgB;IACzD8C,qBAAqB,CAACuD,UAAU,GAAG,IAAI,CAACzG,eAAe;IACvDkD,qBAAqB,CAACwD,mBAAmB,GAAG,IAAI;IAEhD;IACAxD,qBAAqB,CAACyD,WAAW,EAAE;EACrC;EAEAjD,qBAAqBA,CAAC3D,cAA2B;IAC/C,IAAIA,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,MAAMwD,gBAAgB,GAAGzE,cAAc,CAAC6G,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC;MAE1D;MACC,IAAI,CAAClE,gBAAwB,CAAC4B,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAAC5B,gBAAgB,CAACS,GAAG,EAAE;QAC7B,IAAI,CAAC0D,gBAAgB,EAAE;MACzB;IACF;IAEA;IACA,IAAI,CAAChH,cAAc,GAAG,EAAE;EAC1B,CAAC,CAAE;EACHgH,gBAAgBA,CAAA;IACd,IAAI,CAAC5H,gBAAgB,CAACiF,qCAAqC,CAAC;MAC1D3D,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC3B,aAAa,EAAE,IAAI,CAACiD,gBAAgB,CAACjD,aAAa;QAClDD,WAAW,EAAE,IAAI,CAACkD,gBAAgB,CAAClD,WAAW;QAC9C2E,YAAY,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAAC1B,gBAAgB,CAACS,GAAI;QACvCR,MAAM,EAAE,IAAI,CAACD,gBAAgB,CAACC,MAAM;QACpClC,OAAO,EAAE,IAAI,CAACiC,gBAAgB,CAACjC,OAAO;QAAE;QACxC4D,UAAU,EAAG,IAAI,CAAC3B,gBAAwB,CAAC4B,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAAC5D,IAAI,CACL/C,GAAG,CAACgD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACyF,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACzF,OAAO,CAAC0F,YAAY,CAAC7D,GAAG,CAAC8D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACF/G,QAAQ,CAAC,MAAM,IAAI,CAACsD,eAAe,EAAE,CAAC,EACtCvD,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACiF,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC3B,SAAS,EAAE;EACf;EAEA;EACA+F,eAAeA,CAACC,QAAyB;IACvC,IAAI,CAAC7G,gBAAgB,GAAG6G,QAAQ;IAChC,IAAI,CAAC5G,kBAAkB,GAAG,IAAI;EAChC;EAEA;EACA6G,gBAAgBA,CAACD,QAAgB;IAC/B,MAAM1I,MAAM,GAAG,IAAI,CAAC0B,eAAe,CAACxB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKsI,QAAQ,CAAC;IACvE,OAAO1I,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,MAAM;EACvC;CAED;AA/XwDuI,UAAA,EAAtD1J,SAAS,CAAC,uBAAuB,EAAE;EAAE2J,MAAM,EAAE;AAAK,CAAE,CAAC,C,uEAA+C;AAD1FhJ,yBAAyB,GAAA+I,UAAA,EARrC3J,SAAS,CAAC;EACT6J,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC/J,YAAY,EAAEK,YAAY,EAAEE,wBAAwB,EAAEC,qBAAqB;CACtF,CAAC,C,EAEWE,yBAAyB,CAgYrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}