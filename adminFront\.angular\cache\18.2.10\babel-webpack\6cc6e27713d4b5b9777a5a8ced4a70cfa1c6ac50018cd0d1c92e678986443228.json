{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbSpinnerModule } from '@nebular/theme';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"nb-spinner\", 34);\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 50);\n  }\n  if (rf & 2) {\n    const image_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r6), i0.ɵɵsanitizeUrl)(\"alt\", image_r6.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵtemplate(2, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template, 1, 2, \"img\", 45)(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"div\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template_button_click_9_listener() {\n      const image_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.moveToSelected(image_r6));\n    });\n    i0.ɵɵelement(10, \"i\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const image_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", image_r6.id, \"\");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4, \"\\u627E\\u4E0D\\u5230\\u53EF\\u9078\\u64C7\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template, 11, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template, 5, 0, \"div\", 26);\n    i0.ɵɵelementStart(4, \"div\", 37)(5, \"div\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToAvailablePage(ctx_r1.availableCurrentPage - 1));\n    });\n    i0.ɵɵelement(9, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToAvailablePage(ctx_r1.availableCurrentPage + 1));\n    });\n    i0.ɵɵelement(11, \"i\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedAvailableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.availableCurrentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.availablePageCount, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableCurrentPage <= 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableCurrentPage >= ctx_r1.availablePageCount);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 50);\n  }\n  if (rf & 2) {\n    const image_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r8), i0.ɵɵsanitizeUrl)(\"alt\", image_r8.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template_button_click_1_listener() {\n      const image_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.moveToAvailable(image_r8));\n    });\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template, 1, 2, \"img\", 45)(5, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r8));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r8.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", image_r8.id, \"\");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4, \"\\u5C1A\\u672A\\u9078\\u64C7\\u4EFB\\u4F55\\u5716\\u7247\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToSelectedPage(ctx_r1.selectedCurrentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToSelectedPage(ctx_r1.selectedCurrentPage + 1));\n    });\n    i0.ɵɵelement(7, \"i\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.selectedCurrentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.selectedPageCount, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedCurrentPage <= 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedCurrentPage >= ctx_r1.selectedPageCount);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"label\", 7);\n    i0.ɵɵtext(7, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-select\", 8);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChanged($event));\n    });\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"label\", 7);\n    i0.ɵɵtext(12, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchTermChange());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13)(17, \"h6\", 14);\n    i0.ɵɵtext(18, \"\\u53EF\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 15);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template, 5, 0, \"div\", 16)(22, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template, 12, 6, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 18)(24, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveAllToSelected());\n    });\n    i0.ɵɵelement(25, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveAllToAvailable());\n    });\n    i0.ɵɵelement(27, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 12)(29, \"div\", 13)(30, \"h6\", 14);\n    i0.ɵɵtext(31, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 23);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 24);\n    i0.ɵɵtemplate(35, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template, 11, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template, 5, 0, \"div\", 26)(37, ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template, 8, 4, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"nb-card-footer\", 28)(39, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onCancelBinding();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(40, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onConfirmBinding();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r1.materialName || \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.totalAvailableRecords, \" \\u7B46\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.tempSelectedImages.length, \" \\u7B46\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedSelectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tempSelectedImages.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.tempSelectedImages.length, \") \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"nb-spinner\", 34);\n    i0.ɵɵelementStart(2, \"div\", 35);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(ctx_r1.previewingImage), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u53EF\\u9810\\u89BD\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"span\", 66);\n    i0.ɵɵtext(2, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 74)(6, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToPreviousPage());\n    });\n    i0.ɵɵelement(7, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToNextPage());\n    });\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.totalPages, \" \\u9801\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.hasPreviousPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.hasMorePages);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 57)(1, \"nb-card-header\", 58)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 41);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 61);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template, 4, 0, \"div\", 62)(13, ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template, 1, 2, \"img\", 63)(14, ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template, 4, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 58)(16, \"div\", 65)(17, \"div\", 66);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template, 10, 4, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 59);\n    i0.ɵɵtemplate(21, ImagePreviewComponent_ng_template_0_nb_card_1_button_21_Template, 2, 1, \"button\", 68);\n    i0.ɵɵelementStart(22, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(23, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.previewingImage && ctx_r1.getImageUrl(ctx_r1.previewingImage));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && (!ctx_r1.previewingImage || !ctx_r1.getImageUrl(ctx_r1.previewingImage)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalPages > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_nb_card_0_Template, 43, 15, \"nb-card\", 1)(1, ImagePreviewComponent_ng_template_0_nb_card_1_Template, 24, 10, \"nb-card\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBindingInterface);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showBindingInterface);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService, pictureService, messageService) {\n    this.dialogService = dialogService;\n    this.pictureService = pictureService;\n    this.messageService = messageService;\n    this.isVisible = false;\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    // 新增綁定功能相關輸入參數\n    this.showBindingInterface = false;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    // 新增綁定功能相關輸出事件\n    this.confirmImageBinding = new EventEmitter();\n    this.categoryChange = new EventEmitter();\n    // 內部屬性，不再依賴外部傳入\n    this.images = [];\n    this.availableImages = [];\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    this.isLoading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalRecords = 0;\n    // Picklist 分頁相關 (每頁5筆)\n    this.picklistPageSize = 5;\n    this.availableCurrentPage = 1;\n    this.selectedCurrentPage = 1;\n    // 綁定模式相關屬性\n    this.categoryOptions = [{\n      value: 1,\n      label: '建材圖片'\n    }, {\n      value: 2,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = 1;\n    this.tempSelectedImages = []; // 臨時選擇的圖片\n  }\n  ngOnInit() {\n    this.loadImages();\n  }\n  ngOnChanges() {\n    // 當輸入參數變化時重新載入圖片\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\n      this.loadImages();\n    }\n  }\n  // Computed properties for picklist pagination\n  get totalAvailableRecords() {\n    return this.availableImages.length;\n  }\n  get availablePageCount() {\n    return Math.ceil(this.totalAvailableRecords / this.picklistPageSize);\n  }\n  get selectedPageCount() {\n    return Math.ceil(this.tempSelectedImages.length / this.picklistPageSize);\n  }\n  // 主要分頁相關計算屬性\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  get hasMorePages() {\n    return this.currentPage < this.totalPages;\n  }\n  get hasPreviousPage() {\n    return this.currentPage > 1;\n  }\n  get paginatedAvailableImages() {\n    const startIndex = (this.availableCurrentPage - 1) * this.picklistPageSize;\n    const endIndex = startIndex + this.picklistPageSize;\n    return this.availableImages.slice(startIndex, endIndex);\n  }\n  get paginatedSelectedImages() {\n    const startIndex = (this.selectedCurrentPage - 1) * this.picklistPageSize;\n    const endIndex = startIndex + this.picklistPageSize;\n    return this.tempSelectedImages.slice(startIndex, endIndex);\n  }\n  // 載入圖片的主要方法\n  loadImages() {\n    if (!this.buildCaseId && !this.materialId) {\n      return;\n    }\n    this.isLoading = true;\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n  }\n  // 載入可選擇的圖片\n  loadAvailableImages() {\n    if (!this.buildCaseId) {\n      this.availableImages = [];\n      return;\n    }\n    // 載入較大量的資料以支援前端分頁，或者根據需求調整\n    const apiPageSize = this.pageSize; // 使用 pageSize 屬性 (50)\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        cPictureType: this.pictureType,\n        PageIndex: this.currentPage,\n        PageSize: apiPageSize,\n        CName: this.searchTerm || undefined\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          // 轉換 API 回應為 ImageItem 格式\n          const newImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n          this.totalRecords = res.TotalItems || 0;\n          // 保存完整圖片列表\n          this.images = newImages;\n          // 更新可選圖片（排除已選擇的）\n          this.updateAvailableImages();\n          // 設定初始預覽圖片\n          this.setInitialPreviewImage();\n        } else {\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\n        this.isLoading = false;\n      }\n    });\n  }\n  // 載入已選擇的圖片（如果有 materialId）\n  loadSelectedImages() {\n    if (!this.buildCaseId || !this.materialId) {\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CMaterialId: this.materialId,\n        cPictureType: this.pictureType,\n        PageIndex: 1,\n        PageSize: 999 // 載入所有已選圖片\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n        }\n      },\n      error: error => {\n        console.error('載入已選擇圖片失敗:', error);\n      }\n    });\n  }\n  // 設定初始預覽圖片\n  setInitialPreviewImage() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    } else {\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    // 每次開啟時重新載入圖片，確保資料是最新的\n    this.loadImages();\n    // 開啟對話框\n    const template = imagePreviewRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 使用 CGuid 取得圖片資料的方法\n  getPictureByGuid(guid) {\n    if (!guid) {\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\n      return;\n    }\n    // this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\n    //   .subscribe({\n    //     next: (response) => {\n    //       // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\n    //       console.log('取得圖片資料:', response);\n    //     },\n    //     error: (error) => {\n    //       this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\n    //     }\n    //   });\n  }\n  // 獲取圖片 URL，優先使用 CGuid\n  getImageUrl(imageItem) {\n    if (imageItem.guid && !imageItem.fullUrl) {\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\n      this.getPictureByGuid(imageItem.guid);\n    }\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\n  }\n  // 綁定模式相關方法\n  onCategoryChanged(category) {\n    this.selectedCategory = category;\n    this.categoryChange.emit(category);\n    if (this.buildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 開啟綁定界面\n  openBindingInterface(imageBindingRef) {\n    this.showBindingInterface = true;\n    this.tempSelectedImages = [...this.selectedImages];\n    // 重置分頁狀態\n    this.availableCurrentPage = 1;\n    this.selectedCurrentPage = 1;\n    // 每次開啟時重新載入圖片，確保資料是最新的\n    this.loadImages();\n    const template = imageBindingRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 確認圖片綁定\n  onConfirmBinding() {\n    this.confirmImageBinding.emit(this.tempSelectedImages);\n    this.showBindingInterface = false;\n  }\n  // 取消綁定\n  onCancelBinding() {\n    this.tempSelectedImages = [];\n    this.showBindingInterface = false;\n    this.onClose();\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n    } else {\n      this.tempSelectedImages.push(image);\n    }\n  }\n  // 檢查圖片是否被選中\n  isImageTempSelected(image) {\n    return this.tempSelectedImages.some(img => img.id === image.id);\n  }\n  // Picklist 相關方法\n  moveToSelected(image) {\n    if (!this.isImageTempSelected(image)) {\n      this.tempSelectedImages.push(image);\n      this.updateAvailableImages();\n      this.resetSelectedPageIfNeeded();\n    }\n  }\n  moveToAvailable(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n      this.updateAvailableImages();\n      this.resetSelectedPageIfNeeded();\n    }\n  }\n  moveAllToSelected() {\n    // 將目前頁面的可選圖片全部移到已選擇\n    this.paginatedAvailableImages.forEach(image => {\n      if (!this.isImageTempSelected(image)) {\n        this.tempSelectedImages.push(image);\n      }\n    });\n    this.updateAvailableImages();\n    this.resetSelectedPageIfNeeded();\n  }\n  moveAllToAvailable() {\n    this.tempSelectedImages = [];\n    this.updateAvailableImages();\n    this.selectedCurrentPage = 1;\n  }\n  updateAvailableImages() {\n    // 重新計算可選圖片（排除已選擇的）\n    const selectedIds = this.tempSelectedImages.map(img => img.id);\n    this.availableImages = this.images.filter(image => !selectedIds.includes(image.id));\n    this.resetAvailablePageIfNeeded();\n  }\n  resetAvailablePageIfNeeded() {\n    if (this.availableCurrentPage > this.availablePageCount && this.availablePageCount > 0) {\n      this.availableCurrentPage = this.availablePageCount;\n    }\n  }\n  resetSelectedPageIfNeeded() {\n    if (this.selectedCurrentPage > this.selectedPageCount && this.selectedPageCount > 0) {\n      this.selectedCurrentPage = this.selectedPageCount;\n    }\n  }\n  // 分頁導航方法\n  goToAvailablePage(page) {\n    if (page >= 1 && page <= this.availablePageCount) {\n      this.availableCurrentPage = page;\n    }\n  }\n  goToSelectedPage(page) {\n    if (page >= 1 && page <= this.selectedPageCount) {\n      this.selectedCurrentPage = page;\n    }\n  }\n  // 主要分頁導航方法\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.loadImages();\n    }\n  }\n  goToNextPage() {\n    if (this.hasMorePages) {\n      this.currentPage++;\n      this.loadImages();\n    }\n  }\n  goToPreviousPage() {\n    if (this.hasPreviousPage) {\n      this.currentPage--;\n      this.loadImages();\n    }\n  }\n  goToFirstPage() {\n    if (this.currentPage !== 1) {\n      this.currentPage = 1;\n      this.loadImages();\n    }\n  }\n  goToLastPage() {\n    if (this.currentPage !== this.totalPages) {\n      this.currentPage = this.totalPages;\n      this.loadImages();\n    }\n  }\n  // 搜尋變更處理\n  onSearchTermChange() {\n    // 重新載入圖片並重置分頁\n    this.availableCurrentPage = 1;\n    this.loadImages();\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.PictureService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\",\n        buildCaseId: \"buildCaseId\",\n        materialId: \"materialId\",\n        pictureType: \"pictureType\",\n        searchTerm: \"searchTerm\",\n        showBindingInterface: \"showBindingInterface\",\n        materialName: \"materialName\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\",\n        confirmImageBinding: \"confirmImageBinding\",\n        categoryChange: \"categoryChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [\"class\", \"w-[95vw] max-w-[1200px] h-[85vh]\", 4, \"ngIf\"], [\"class\", \"w-[800px] h-[600px]\", 4, \"ngIf\"], [1, \"w-[95vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\"], [1, \"d-flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [1, \"form-label\"], [\"placeholder\", \"\\u9078\\u64C7\\u5716\\u7247\\u985E\\u5225\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex-1\", \"d-flex\", \"gap-3\", 2, \"overflow\", \"hidden\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"badge\", \"badge-secondary\"], [\"class\", \"flex-1 d-flex align-items-center justify-content-center\", 4, \"ngIf\"], [\"class\", \"flex-1 d-flex flex-column\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"80px\"], [\"title\", \"\\u5168\\u90E8\\u9078\\u64C7\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u9664\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"badge\", \"badge-primary\"], [1, \"flex-1\", 2, \"overflow-y\", \"auto\"], [\"class\", \"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\", \"style\", \"cursor: pointer;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex-1 d-flex align-items-center justify-content-center text-muted\", 4, \"ngIf\"], [\"class\", \"mt-3 d-flex justify-content-between align-items-center\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"flex-1\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\"], [\"size\", \"large\"], [1, \"mt-2\"], [1, \"flex-1\", \"d-flex\", \"flex-column\"], [1, \"mt-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"small\", \"text-muted\"], [1, \"d-flex\", \"gap-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"d-flex\", \"align-items-center\", \"p-2\", \"mb-2\", \"border\", \"rounded\", \"hover-highlight\", 2, \"cursor\", \"pointer\"], [1, \"me-3\", 2, \"width\", \"60px\", \"height\", \"60px\", \"flex-shrink\", \"0\"], [\"class\", \"img-fluid rounded\", \"style\", \"width: 100%; height: 100%; object-fit: cover;\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\", 4, \"ngIf\"], [1, \"flex-1\", \"me-3\"], [1, \"fw-bold\", \"text-truncate\", 3, \"title\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"img-fluid\", \"rounded\", 2, \"width\", \"100%\", \"height\", \"100%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [1, \"w-100\", \"h-100\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"bg-light\", \"rounded\"], [1, \"fas\", \"fa-image\", \"text-muted\"], [1, \"flex-1\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"text-muted\"], [1, \"fas\", \"fa-images\", \"fa-2x\", \"mb-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"me-3\", 3, \"click\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"mb-2\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", \"gap-3\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"d-flex align-items-center gap-2\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbSelectComponent, i1.NbOptionComponent, NbSpinnerModule, i1.NbSpinnerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\n.hover-highlight[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.hover-highlight[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.picklist-container[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .rounded[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .bg-light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.75rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.375rem;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #0d6efd;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #6c757d;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .text-truncate[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .small[_ngcontent-%COMP%] {\\n  font-size: 0.875em;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-column[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-1[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.25rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-3[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-2[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-3[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .me-3[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mt-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\nnb-card[_ngcontent-%COMP%]   .h-100[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbSpinnerModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ctx_r1", "getImageUrl", "image_r6", "ɵɵsanitizeUrl", "name", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template_button_click_9_listener", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "moveToSelected", "ɵɵtextInterpolate", "id", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_8_listener", "_r4", "goToAvailablePage", "availableCurrentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_10_listener", "paginatedAvailableImages", "availableImages", "length", "ɵɵtextInterpolate2", "availablePageCount", "image_r8", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template_button_click_1_listener", "_r7", "moveToAvailable", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_4_listener", "_r9", "goToSelectedPage", "selectedCurrentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_6_listener", "selectedPageCount", "ɵɵtwoWayListener", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "onCategoryChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener", "searchTerm", "onSearchTermChange", "ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_24_listener", "moveAllToSelected", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_26_listener", "moveAllToAvailable", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_39_listener", "ref_r10", "dialogRef", "onCancelBinding", "close", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_41_listener", "onConfirmBinding", "materialName", "ɵɵtwoWayProperty", "categoryOptions", "totalAvailableRecords", "isLoading", "tempSelectedImages", "paginatedSelectedImages", "previewingImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template_button_click_6_listener", "_r12", "goToPreviousPage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template_button_click_8_listener", "goToNextPage", "currentPage", "totalPages", "hasPreviousPage", "hasMorePages", "ImagePreviewComponent_ng_template_0_nb_card_1_button_21_Template_button_click_0_listener", "_r13", "onToggleImageSelection", "isImageSelected", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener", "_r11", "onPreviousImage", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener", "onNextImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_19_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_button_21_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_22_listener", "onClose", "currentPreviewIndex", "images", "showSelectionToggle", "ImagePreviewComponent_ng_template_0_nb_card_0_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template", "showBindingInterface", "ImagePreviewComponent", "constructor", "dialogService", "pictureService", "messageService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "confirmImageBinding", "categoryChange", "pageSize", "totalRecords", "picklistPageSize", "ngOnInit", "loadImages", "ngOnChanges", "buildCaseId", "materialId", "pictureType", "undefined", "Math", "ceil", "startIndex", "endIndex", "slice", "loadAvailableImages", "loadSelectedImages", "apiPageSize", "apiPictureGetPictureListPost$Json", "body", "CBuildCaseId", "cPictureType", "PageIndex", "PageSize", "CName", "subscribe", "next", "res", "StatusCode", "newImages", "Entries", "map", "picture", "CId", "CPictureCode", "size", "thumbnailUrl", "CFile", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "TotalItems", "updateAvailableImages", "setInitialPreviewImage", "showErrorMSG", "Message", "error", "message", "CMaterialId", "console", "max", "min", "emit", "image", "some", "selected", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "getPictureByGuid", "imageItem", "category", "openBindingInterface", "imageBindingRef", "toggleImageSelection", "index", "findIndex", "img", "splice", "push", "isImageTempSelected", "resetSelectedPageIfNeeded", "for<PERSON>ach", "selectedIds", "filter", "includes", "resetAvailablePageIfNeeded", "page", "goToPage", "goToFirstPage", "goToLastPage", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "PictureService", "i3", "MessageService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "NbSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService, NbSpinnerModule } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\nimport { PictureService } from 'src/services/api/services';\r\nimport { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbSpinnerModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit, OnChanges {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n\r\n  // 新增輸入參數來接收圖片載入所需的資訊\r\n  @Input() buildCaseId?: number;\r\n  @Input() materialId?: number;\r\n  @Input() pictureType?: number;\r\n  @Input() searchTerm?: string;\r\n\r\n  // 新增綁定功能相關輸入參數\r\n  @Input() showBindingInterface: boolean = false;\r\n  @Input() materialName?: string;\r\n\r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  // 新增綁定功能相關輸出事件\r\n  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();\r\n  @Output() categoryChange = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  // 內部屬性，不再依賴外部傳入\r\n  images: ImageItem[] = [];\r\n  availableImages: ImageItem[] = [];\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n  isLoading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 50;\r\n  totalRecords: number = 0;\r\n\r\n  // Picklist 分頁相關 (每頁5筆)\r\n  picklistPageSize: number = 5;\r\n  availableCurrentPage: number = 1;\r\n  selectedCurrentPage: number = 1;\r\n\r\n  // 綁定模式相關屬性\r\n  categoryOptions = [\r\n    { value: 1, label: '建材圖片' },\r\n    { value: 2, label: '示意圖片' }\r\n  ];\r\n  selectedCategory: number = 1;\r\n  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private pictureService: PictureService,\r\n    private messageService: MessageService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadImages();\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // 當輸入參數變化時重新載入圖片\r\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // Computed properties for picklist pagination\r\n  get totalAvailableRecords(): number {\r\n    return this.availableImages.length;\r\n  }\r\n\r\n  get availablePageCount(): number {\r\n    return Math.ceil(this.totalAvailableRecords / this.picklistPageSize);\r\n  }\r\n\r\n  get selectedPageCount(): number {\r\n    return Math.ceil(this.tempSelectedImages.length / this.picklistPageSize);\r\n  }\r\n\r\n  // 主要分頁相關計算屬性\r\n  get totalPages(): number {\r\n    return Math.ceil(this.totalRecords / this.pageSize);\r\n  }\r\n\r\n  get hasMorePages(): boolean {\r\n    return this.currentPage < this.totalPages;\r\n  }\r\n\r\n  get hasPreviousPage(): boolean {\r\n    return this.currentPage > 1;\r\n  }\r\n\r\n  get paginatedAvailableImages(): ImageItem[] {\r\n    const startIndex = (this.availableCurrentPage - 1) * this.picklistPageSize;\r\n    const endIndex = startIndex + this.picklistPageSize;\r\n    return this.availableImages.slice(startIndex, endIndex);\r\n  }\r\n\r\n  get paginatedSelectedImages(): ImageItem[] {\r\n    const startIndex = (this.selectedCurrentPage - 1) * this.picklistPageSize;\r\n    const endIndex = startIndex + this.picklistPageSize;\r\n    return this.tempSelectedImages.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 載入圖片的主要方法\r\n  loadImages(): void {\r\n    if (!this.buildCaseId && !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n  // 載入可選擇的圖片\r\n  loadAvailableImages(): void {\r\n    if (!this.buildCaseId) {\r\n      this.availableImages = [];\r\n      return;\r\n    }\r\n\r\n    // 載入較大量的資料以支援前端分頁，或者根據需求調整\r\n    const apiPageSize = this.pageSize; // 使用 pageSize 屬性 (50)\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: this.currentPage,\r\n        PageSize: apiPageSize,\r\n        CName: this.searchTerm || undefined\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 轉換 API 回應為 ImageItem 格式\r\n          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n\r\n          this.totalRecords = res.TotalItems || 0;\r\n\r\n          // 保存完整圖片列表\r\n          this.images = newImages;\r\n\r\n          // 更新可選圖片（排除已選擇的）\r\n          this.updateAvailableImages();\r\n\r\n          // 設定初始預覽圖片\r\n          this.setInitialPreviewImage();\r\n        } else {\r\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 載入已選擇的圖片（如果有 materialId）\r\n  loadSelectedImages(): void {\r\n    if (!this.buildCaseId || !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CMaterialId: this.materialId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: 1,\r\n        PageSize: 999 // 載入所有已選圖片\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入已選擇圖片失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 設定初始預覽圖片\r\n  setInitialPreviewImage(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    } else {\r\n      this.previewingImage = null;\r\n      this.currentPreviewIndex = 0;\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    // 每次開啟時重新載入圖片，確保資料是最新的\r\n    this.loadImages();\r\n\r\n    // 開啟對話框\r\n    const template = imagePreviewRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 使用 CGuid 取得圖片資料的方法\r\n  getPictureByGuid(guid: string): void {\r\n    if (!guid) {\r\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\r\n      return;\r\n    }\r\n\r\n    // this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\r\n    //   .subscribe({\r\n    //     next: (response) => {\r\n    //       // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\r\n    //       console.log('取得圖片資料:', response);\r\n    //     },\r\n    //     error: (error) => {\r\n    //       this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\r\n    //     }\r\n    //   });\r\n  }\r\n\r\n  // 獲取圖片 URL，優先使用 CGuid\r\n  getImageUrl(imageItem: ImageItem): string {\r\n    if (imageItem.guid && !imageItem.fullUrl) {\r\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\r\n      this.getPictureByGuid(imageItem.guid);\r\n    }\r\n\r\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\r\n  }\r\n\r\n  // 綁定模式相關方法\r\n  onCategoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.categoryChange.emit(category);\r\n    if (this.buildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 開啟綁定界面\r\n  openBindingInterface(imageBindingRef?: TemplateRef<any>) {\r\n    this.showBindingInterface = true;\r\n    this.tempSelectedImages = [...this.selectedImages];\r\n\r\n    // 重置分頁狀態\r\n    this.availableCurrentPage = 1;\r\n    this.selectedCurrentPage = 1;\r\n\r\n    // 每次開啟時重新載入圖片，確保資料是最新的\r\n    this.loadImages();\r\n\r\n    const template = imageBindingRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 確認圖片綁定\r\n  onConfirmBinding() {\r\n    this.confirmImageBinding.emit(this.tempSelectedImages);\r\n    this.showBindingInterface = false;\r\n  }\r\n\r\n  // 取消綁定\r\n  onCancelBinding() {\r\n    this.tempSelectedImages = [];\r\n    this.showBindingInterface = false;\r\n    this.onClose();\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n    } else {\r\n      this.tempSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否被選中\r\n  isImageTempSelected(image: ImageItem): boolean {\r\n    return this.tempSelectedImages.some(img => img.id === image.id);\r\n  }\r\n\r\n  // Picklist 相關方法\r\n  moveToSelected(image: ImageItem): void {\r\n    if (!this.isImageTempSelected(image)) {\r\n      this.tempSelectedImages.push(image);\r\n      this.updateAvailableImages();\r\n      this.resetSelectedPageIfNeeded();\r\n    }\r\n  }\r\n\r\n  moveToAvailable(image: ImageItem): void {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n      this.updateAvailableImages();\r\n      this.resetSelectedPageIfNeeded();\r\n    }\r\n  }\r\n\r\n  moveAllToSelected(): void {\r\n    // 將目前頁面的可選圖片全部移到已選擇\r\n    this.paginatedAvailableImages.forEach(image => {\r\n      if (!this.isImageTempSelected(image)) {\r\n        this.tempSelectedImages.push(image);\r\n      }\r\n    });\r\n    this.updateAvailableImages();\r\n    this.resetSelectedPageIfNeeded();\r\n  }\r\n\r\n  moveAllToAvailable(): void {\r\n    this.tempSelectedImages = [];\r\n    this.updateAvailableImages();\r\n    this.selectedCurrentPage = 1;\r\n  }\r\n\r\n  updateAvailableImages(): void {\r\n    // 重新計算可選圖片（排除已選擇的）\r\n    const selectedIds = this.tempSelectedImages.map(img => img.id);\r\n    this.availableImages = this.images.filter(image => !selectedIds.includes(image.id));\r\n    this.resetAvailablePageIfNeeded();\r\n  }\r\n\r\n  resetAvailablePageIfNeeded(): void {\r\n    if (this.availableCurrentPage > this.availablePageCount && this.availablePageCount > 0) {\r\n      this.availableCurrentPage = this.availablePageCount;\r\n    }\r\n  }\r\n\r\n  resetSelectedPageIfNeeded(): void {\r\n    if (this.selectedCurrentPage > this.selectedPageCount && this.selectedPageCount > 0) {\r\n      this.selectedCurrentPage = this.selectedPageCount;\r\n    }\r\n  }\r\n\r\n  // 分頁導航方法\r\n  goToAvailablePage(page: number): void {\r\n    if (page >= 1 && page <= this.availablePageCount) {\r\n      this.availableCurrentPage = page;\r\n    }\r\n  }\r\n\r\n  goToSelectedPage(page: number): void {\r\n    if (page >= 1 && page <= this.selectedPageCount) {\r\n      this.selectedCurrentPage = page;\r\n    }\r\n  }\r\n\r\n  // 主要分頁導航方法\r\n  goToPage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  goToNextPage(): void {\r\n    if (this.hasMorePages) {\r\n      this.currentPage++;\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  goToPreviousPage(): void {\r\n    if (this.hasPreviousPage) {\r\n      this.currentPage--;\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  goToFirstPage(): void {\r\n    if (this.currentPage !== 1) {\r\n      this.currentPage = 1;\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  goToLastPage(): void {\r\n    if (this.currentPage !== this.totalPages) {\r\n      this.currentPage = this.totalPages;\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 搜尋變更處理\r\n  onSearchTermChange(): void {\r\n    // 重新載入圖片並重置分頁\r\n    this.availableCurrentPage = 1;\r\n    this.loadImages();\r\n  }\r\n}\r\n", "<!-- 圖片預覽/綁定對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <!-- 綁定模式 - Picklist 風格 -->\r\n  <nb-card *ngIf=\"showBindingInterface\" class=\"w-[95vw] max-w-[1200px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ materialName || '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"d-flex flex-column\" style=\"height: calc(100% - 120px); overflow: hidden;\">\r\n\r\n      <!-- 控制區 -->\r\n      <div class=\"d-flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">圖片類別</label>\r\n          <nb-select [(selected)]=\"selectedCategory\" placeholder=\"選擇圖片類別\" (selectedChange)=\"onCategoryChanged($event)\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"searchTerm\"\r\n            (ngModelChange)=\"onSearchTermChange()\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Picklist 容器 -->\r\n      <div class=\"flex-1 d-flex gap-3\" style=\"overflow: hidden;\">\r\n\r\n        <!-- 可選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">可選擇圖片</h6>\r\n            <span class=\"badge badge-secondary\">{{ totalAvailableRecords }} 筆</span>\r\n          </div>\r\n\r\n          <!-- 載入中狀態 -->\r\n          <div *ngIf=\"isLoading\" class=\"flex-1 d-flex align-items-center justify-content-center\">\r\n            <div class=\"text-center\">\r\n              <nb-spinner size=\"large\"></nb-spinner>\r\n              <div class=\"mt-2\">載入圖片中...</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 可選擇圖片列表 -->\r\n          <div *ngIf=\"!isLoading\" class=\"flex-1 d-flex flex-column\">\r\n            <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n              <div *ngFor=\"let image of paginatedAvailableImages\"\r\n                class=\"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\" style=\"cursor: pointer;\">\r\n                <!-- 圖片縮圖 -->\r\n                <div class=\"me-3\" style=\"width: 60px; height: 60px; flex-shrink: 0;\">\r\n                  <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                    class=\"img-fluid rounded\" style=\"width: 100%; height: 100%; object-fit: cover;\" />\r\n                  <div *ngIf=\"!getImageUrl(image)\"\r\n                    class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                    <i class=\"fas fa-image text-muted\"></i>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 圖片資訊 -->\r\n                <div class=\"flex-1 me-3\">\r\n                  <div class=\"fw-bold text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                  <div class=\"small text-muted\">ID: {{ image.id }}</div>\r\n                </div>\r\n\r\n                <!-- 選擇按鈕 -->\r\n                <button class=\"btn btn-sm btn-outline-primary\" (click)=\"moveToSelected(image)\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"availableImages.length === 0\"\r\n              class=\"flex-1 d-flex align-items-center justify-content-center text-muted\">\r\n              <div class=\"text-center\">\r\n                <i class=\"fas fa-images fa-2x mb-2\"></i>\r\n                <div>找不到可選擇的圖片</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分頁控制 -->\r\n            <div class=\"mt-3 d-flex justify-content-between align-items-center\">\r\n              <div class=\"small text-muted\">\r\n                第 {{ availableCurrentPage }} 頁，共 {{ availablePageCount }} 頁\r\n              </div>\r\n              <div class=\"d-flex gap-1\">\r\n                <button class=\"btn btn-sm btn-outline-secondary\" [disabled]=\"availableCurrentPage <= 1\"\r\n                  (click)=\"goToAvailablePage(availableCurrentPage - 1)\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n                <button class=\"btn btn-sm btn-outline-secondary\" [disabled]=\"availableCurrentPage >= availablePageCount\"\r\n                  (click)=\"goToAvailablePage(availableCurrentPage + 1)\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中間控制按鈕 -->\r\n        <div class=\"d-flex flex-column justify-content-center gap-2\" style=\"width: 80px;\">\r\n          <button class=\"btn btn-primary btn-sm\" [disabled]=\"availableImages.length === 0\" (click)=\"moveAllToSelected()\"\r\n            title=\"全部選擇\">\r\n            <i class=\"fas fa-angle-double-right\"></i>\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" [disabled]=\"tempSelectedImages.length === 0\"\r\n            (click)=\"moveAllToAvailable()\" title=\"全部移除\">\r\n            <i class=\"fas fa-angle-double-left\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 已選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">已選擇圖片</h6>\r\n            <span class=\"badge badge-primary\">{{ tempSelectedImages.length }} 筆</span>\r\n          </div>\r\n\r\n          <!-- 已選擇圖片列表 -->\r\n          <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n            <div *ngFor=\"let image of paginatedSelectedImages\"\r\n              class=\"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\" style=\"cursor: pointer;\">\r\n              <!-- 移除按鈕 -->\r\n              <button class=\"btn btn-sm btn-outline-danger me-3\" (click)=\"moveToAvailable(image)\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n\r\n              <!-- 圖片縮圖 -->\r\n              <div class=\"me-3\" style=\"width: 60px; height: 60px; flex-shrink: 0;\">\r\n                <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\" class=\"img-fluid rounded\"\r\n                  style=\"width: 100%; height: 100%; object-fit: cover;\" />\r\n                <div *ngIf=\"!getImageUrl(image)\"\r\n                  class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                  <i class=\"fas fa-image text-muted\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 圖片資訊 -->\r\n              <div class=\"flex-1\">\r\n                <div class=\"fw-bold text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                <div class=\"small text-muted\">ID: {{ image.id }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 空狀態 -->\r\n          <div *ngIf=\"tempSelectedImages.length === 0\"\r\n            class=\"flex-1 d-flex align-items-center justify-content-center text-muted\">\r\n            <div class=\"text-center\">\r\n              <i class=\"fas fa-inbox fa-2x mb-2\"></i>\r\n              <div>尚未選擇任何圖片</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 分頁控制 -->\r\n          <div *ngIf=\"tempSelectedImages.length > 0\" class=\"mt-3 d-flex justify-content-between align-items-center\">\r\n            <div class=\"small text-muted\">\r\n              第 {{ selectedCurrentPage }} 頁，共 {{ selectedPageCount }} 頁\r\n            </div>\r\n            <div class=\"d-flex gap-1\">\r\n              <button class=\"btn btn-sm btn-outline-secondary\" [disabled]=\"selectedCurrentPage <= 1\"\r\n                (click)=\"goToSelectedPage(selectedCurrentPage - 1)\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              <button class=\"btn btn-sm btn-outline-secondary\" [disabled]=\"selectedCurrentPage >= selectedPageCount\"\r\n                (click)=\"goToSelectedPage(selectedCurrentPage + 1)\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onCancelBinding(); ref.close()\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onConfirmBinding(); ref.close()\"\r\n        [disabled]=\"tempSelectedImages.length === 0\">\r\n        確定選擇 ({{ tempSelectedImages.length }})\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n\r\n  <!-- 預覽模式 -->\r\n  <nb-card *ngIf=\"!showBindingInterface\" class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\"\r\n          (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n\r\n      <!-- 圖片顯示 -->\r\n      <img *ngIf=\"!isLoading && previewingImage && getImageUrl(previewingImage)\" [src]=\"getImageUrl(previewingImage)\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n\r\n      <!-- 無圖片狀態 -->\r\n      <div *ngIf=\"!isLoading && (!previewingImage || !getImageUrl(previewingImage))\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>無可預覽的圖片</div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"d-flex align-items-center gap-3\">\r\n        <div class=\"text-sm text-gray-600\">\r\n          {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n        </div>\r\n        <!-- 主要分頁控制 -->\r\n        <div *ngIf=\"totalPages > 1\" class=\"d-flex align-items-center gap-2\">\r\n          <span class=\"text-sm text-gray-600\">|</span>\r\n          <span class=\"text-sm text-gray-600\">第 {{ currentPage }} 頁，共 {{ totalPages }} 頁</span>\r\n          <div class=\"btn-group btn-group-sm\">\r\n            <button class=\"btn btn-outline-secondary btn-sm\" [disabled]=\"!hasPreviousPage\"\r\n              (click)=\"goToPreviousPage()\" title=\"上一頁\">\r\n              <i class=\"fas fa-chevron-left\"></i>\r\n            </button>\r\n            <button class=\"btn btn-outline-secondary btn-sm\" [disabled]=\"!hasMorePages\"\r\n              (click)=\"goToNextPage()\" title=\"下一頁\">\r\n              <i class=\"fas fa-chevron-right\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAmD,eAAe;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAA0BC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;ICW1DC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAsBAT,EADF,CAAAC,cAAA,cAAuF,cAC5D;IACvBD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAE9BF,EAF8B,CAAAG,YAAA,EAAM,EAC5B,EACF;;;;;IASEH,EAAA,CAAAU,SAAA,cACoF;;;;;IADzBV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAb,EAAA,CAAAc,aAAA,CAA0B,QAAAD,QAAA,CAAAE,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cACwF;IACtFD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IANRH,EAHF,CAAAC,cAAA,cACqG,cAE9B;IAGnED,EAFA,CAAAgB,UAAA,IAAAC,yEAAA,kBACoF,IAAAC,yEAAA,kBAEI;IAG1FlB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAyB,cACiC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAClDF,EADkD,CAAAG,YAAA,EAAM,EAClD;IAGNH,EAAA,CAAAC,cAAA,iBAA+E;IAAhCD,EAAA,CAAAmB,UAAA,mBAAAC,4FAAA;MAAA,MAAAP,QAAA,GAAAb,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAe,cAAA,CAAAb,QAAA,CAAqB;IAAA,EAAC;IAC5Eb,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAlBIH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAwB;IAExBb,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAyB;IAQIb,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAS,QAAA,CAAAE,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA2B,iBAAA,CAAAd,QAAA,CAAAE,IAAA,CAAgB;IAC1Cf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,kBAAA,SAAAK,QAAA,CAAAe,EAAA,KAAkB;;;;;IAapD5B,EAFF,CAAAC,cAAA,cAC6E,cAClD;IACvBD,EAAA,CAAAU,SAAA,YAAwC;IACxCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAElBF,EAFkB,CAAAG,YAAA,EAAM,EAChB,EACF;;;;;;IAjCNH,EADF,CAAAC,cAAA,cAA0D,cACV;IAC5CD,EAAA,CAAAgB,UAAA,IAAAa,mEAAA,mBACqG;IAsBvG7B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgB,UAAA,IAAAc,mEAAA,kBAC6E;IAS3E9B,EADF,CAAAC,cAAA,cAAoE,cACpC;IAC5BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,iBAEgC;IAAtDD,EAAA,CAAAmB,UAAA,mBAAAY,sFAAA;MAAA/B,EAAA,CAAAqB,aAAA,CAAAW,GAAA;MAAA,MAAArB,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsB,iBAAA,CAAAtB,MAAA,CAAAuB,oBAAA,GAAyC,CAAC,CAAC;IAAA,EAAC;IACrDlC,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwD;IAAtDD,EAAA,CAAAmB,UAAA,mBAAAgB,uFAAA;MAAAnC,EAAA,CAAAqB,aAAA,CAAAW,GAAA;MAAA,MAAArB,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsB,iBAAA,CAAAtB,MAAA,CAAAuB,oBAAA,GAAyC,CAAC,CAAC;IAAA,EAAC;IACrDlC,EAAA,CAAAU,SAAA,aAAoC;IAI5CV,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAlDqBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAyB,wBAAA,CAA2B;IA0B9CpC,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0B,eAAA,CAAAC,MAAA,OAAkC;IAWpCtC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,aAAA5B,MAAA,CAAAuB,oBAAA,0BAAAvB,MAAA,CAAA6B,kBAAA,aACF;IAEmDxC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuB,oBAAA,MAAsC;IAItClC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuB,oBAAA,IAAAvB,MAAA,CAAA6B,kBAAA,CAAuD;;;;;IAuCxGxC,EAAA,CAAAU,SAAA,cAC0D;;;;;IADCV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,GAAAzC,EAAA,CAAAc,aAAA,CAA0B,QAAA2B,QAAA,CAAA1B,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cACwF;IACtFD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IAXRH,EAHF,CAAAC,cAAA,cACqG,iBAEf;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAuB,sFAAA;MAAA,MAAAD,QAAA,GAAAzC,EAAA,CAAAqB,aAAA,CAAAsB,GAAA,EAAApB,SAAA;MAAA,MAAAZ,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAiC,eAAA,CAAAH,QAAA,CAAsB;IAAA,EAAC;IACjFzC,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAAqE;IAGnED,EAFA,CAAAgB,UAAA,IAAA6B,mEAAA,kBAC0D,IAAAC,mEAAA,kBAE8B;IAG1F9C,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAAoB,cACsC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAEpDF,EAFoD,CAAAG,YAAA,EAAM,EAClD,EACF;;;;;IAbIH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,EAAwB;IAExBzC,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,EAAyB;IAQIzC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAqC,QAAA,CAAA1B,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA2B,iBAAA,CAAAc,QAAA,CAAA1B,IAAA,CAAgB;IAC1Cf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,kBAAA,SAAAiC,QAAA,CAAAb,EAAA,KAAkB;;;;;IAQpD5B,EAFF,CAAAC,cAAA,cAC6E,cAClD;IACvBD,EAAA,CAAAU,SAAA,YAAuC;IACvCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAEjBF,EAFiB,CAAAG,YAAA,EAAM,EACf,EACF;;;;;;IAIJH,EADF,CAAAC,cAAA,cAA0G,cAC1E;IAC5BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,iBAE8B;IAApDD,EAAA,CAAAmB,UAAA,mBAAA4B,sFAAA;MAAA/C,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsC,gBAAA,CAAAtC,MAAA,CAAAuC,mBAAA,GAAuC,CAAC,CAAC;IAAA,EAAC;IACnDlD,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBACsD;IAApDD,EAAA,CAAAmB,UAAA,mBAAAgC,sFAAA;MAAAnD,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsC,gBAAA,CAAAtC,MAAA,CAAAuC,mBAAA,GAAuC,CAAC,CAAC;IAAA,EAAC;IACnDlD,EAAA,CAAAU,SAAA,YAAoC;IAG1CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAZFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,aAAA5B,MAAA,CAAAuC,mBAAA,0BAAAvC,MAAA,CAAAyC,iBAAA,aACF;IAEmDpD,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuC,mBAAA,MAAqC;IAIrClD,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuC,mBAAA,IAAAvC,MAAA,CAAAyC,iBAAA,CAAqD;;;;;;IAjKhHpD,EADF,CAAAC,cAAA,iBAA+E,qBAC7D;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,sBAA+F,aAGhD,aACvB,eACQ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,mBAA6G;IAAlGD,EAAA,CAAAqD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAyD,kBAAA,CAAA9C,MAAA,CAAA+C,gBAAA,EAAAH,MAAA,MAAA5C,MAAA,CAAA+C,gBAAA,GAAAH,MAAA;MAAA,OAAAvD,EAAA,CAAAyB,WAAA,CAAA8B,MAAA;IAAA,EAA+B;IAAsBvD,EAAA,CAAAmB,UAAA,4BAAAmC,2FAAAC,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAkBd,MAAA,CAAAgD,iBAAA,CAAAJ,MAAA,CAAyB;IAAA,EAAC;IAC1GvD,EAAA,CAAAgB,UAAA,IAAA4C,kEAAA,uBAAyE;IAI7E5D,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,iBAC2C;IADqBD,EAAA,CAAAqD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAyD,kBAAA,CAAA9C,MAAA,CAAAmD,UAAA,EAAAP,MAAA,MAAA5C,MAAA,CAAAmD,UAAA,GAAAP,MAAA;MAAA,OAAAvD,EAAA,CAAAyB,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IACtFvD,EAAA,CAAAmB,UAAA,2BAAA0C,uFAAA;MAAA7D,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAiBd,MAAA,CAAAoD,kBAAA,EAAoB;IAAA,EAAC;IAE5C/D,EAHI,CAAAG,YAAA,EAC2C,EACvC,EACF;IAQAH,EALN,CAAAC,cAAA,eAA2D,eAGC,eACY,cACjD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;IAWNH,EARA,CAAAgB,UAAA,KAAAgD,6DAAA,kBAAuF,KAAAC,6DAAA,mBAQ7B;IAqD5DjE,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAkF,kBAEjE;IADkED,EAAA,CAAAmB,UAAA,mBAAA+C,gFAAA;MAAAlE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAwD,iBAAA,EAAmB;IAAA,EAAC;IAE5GnE,EAAA,CAAAU,SAAA,aAAyC;IAC3CV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAC8C;IAA5CD,EAAA,CAAAmB,UAAA,mBAAAiD,gFAAA;MAAApE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA0D,kBAAA,EAAoB;IAAA,EAAC;IAC9BrE,EAAA,CAAAU,SAAA,aAAwC;IAE5CV,EADE,CAAAG,YAAA,EAAS,EACL;IAKFH,EAFJ,CAAAC,cAAA,eAA0D,eACY,cACjD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAGNH,EAAA,CAAAC,cAAA,eAA8C;IAC5CD,EAAA,CAAAgB,UAAA,KAAAsD,6DAAA,mBACqG;IAsBvGtE,EAAA,CAAAG,YAAA,EAAM;IAYNH,EATA,CAAAgB,UAAA,KAAAuD,6DAAA,kBAC6E,KAAAC,6DAAA,kBAQ6B;IAiBhHxE,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAuD,kBACsB;IAAzCD,EAAA,CAAAmB,UAAA,mBAAAsD,gFAAA;MAAAzE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAAkB,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAiE,eAAA,EAAiB;MAAA,OAAA5E,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC7E,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtFH,EAAA,CAAAC,cAAA,kBAC+C;IADfD,EAAA,CAAAmB,UAAA,mBAAA2D,gFAAA;MAAA9E,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAAkB,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAoE,gBAAA,EAAkB;MAAA,OAAA/E,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEvE7E,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;IAhLNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAG,MAAA,CAAAqE,YAAA,gDACF;IAOiBhF,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAiF,gBAAA,aAAAtE,MAAA,CAAA+C,gBAAA,CAA+B;IACV1D,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAuE,eAAA,CAAkB;IAOclF,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAiF,gBAAA,YAAAtE,MAAA,CAAAmD,UAAA,CAAwB;IAYlD9D,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,kBAAA,KAAAG,MAAA,CAAAwE,qBAAA,YAA6B;IAI7DnF,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,SAAA,CAAe;IAQfpF,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,CAAgB;IAyDiBpF,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0B,eAAA,CAAAC,MAAA,OAAyC;IAIvCtC,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAA4C;IAUjDtC,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,kBAAA,KAAAG,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,YAAiC;IAK5CtC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA2E,uBAAA,CAA0B;IA0B7CtF,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAAqC;IASrCtC,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,KAAmC;IAqB3CtC,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAA4C;IAC5CtC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OACF;;;;;IAsBAtC,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAGNH,EAAA,CAAAU,SAAA,cAC8E;;;;IAA5EV,EADyE,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,GAAAvF,EAAA,CAAAc,aAAA,CAAoC,QAAAH,MAAA,CAAA4E,eAAA,CAAAxE,IAAA,CACjF;;;;;IAG9Bf,EAAA,CAAAC,cAAA,cAAiH;IAC/GD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACdF,EADc,CAAAG,YAAA,EAAM,EACd;;;;;;IAUFH,EADF,CAAAC,cAAA,cAAoE,eAC9B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEnFH,EADF,CAAAC,cAAA,cAAoC,iBAES;IAAzCD,EAAA,CAAAmB,UAAA,mBAAAqE,sFAAA;MAAAxF,EAAA,CAAAqB,aAAA,CAAAoE,IAAA;MAAA,MAAA9E,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IAC5B1F,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBACuC;IAArCD,EAAA,CAAAmB,UAAA,mBAAAwE,sFAAA;MAAA3F,EAAA,CAAAqB,aAAA,CAAAoE,IAAA;MAAA,MAAA9E,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAiF,YAAA,EAAc;IAAA,EAAC;IACxB5F,EAAA,CAAAU,SAAA,YAAoC;IAG1CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAXgCH,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAuC,kBAAA,YAAA5B,MAAA,CAAAkF,WAAA,0BAAAlF,MAAA,CAAAmF,UAAA,YAA0C;IAE3B9F,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,cAAAO,MAAA,CAAAoF,eAAA,CAA6B;IAI7B/F,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,cAAAO,MAAA,CAAAqF,YAAA,CAA0B;;;;;;IAQ/EhG,EAAA,CAAAC,cAAA,iBAA2G;IAAnCD,EAAA,CAAAmB,UAAA,mBAAA8E,yFAAA;MAAAjG,EAAA,CAAAqB,aAAA,CAAA6E,IAAA;MAAA,MAAAvF,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAwF,sBAAA,EAAwB;IAAA,EAAC;IACxGnG,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,MAAA,CAAA4E,eAAA,IAAA5E,MAAA,CAAAyF,eAAA,CAAAzF,MAAA,CAAA4E,eAAA,uEACF;;;;;;IAvDFvF,EAFJ,CAAAC,cAAA,kBAAmE,yBACS,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,iBAEM;IAA5BD,EAAA,CAAAmB,UAAA,mBAAAkF,+EAAA;MAAArG,EAAA,CAAAqB,aAAA,CAAAiF,IAAA;MAAA,MAAA3F,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA4F,eAAA,EAAiB;IAAA,EAAC;IAC3BvG,EAAA,CAAAU,SAAA,YAAmC;IAACV,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC0B;IAAxBD,EAAA,CAAAmB,UAAA,mBAAAqF,+EAAA;MAAAxG,EAAA,CAAAqB,aAAA,CAAAiF,IAAA;MAAA,MAAA3F,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA8F,WAAA,EAAa;IAAA,EAAC;IACvBzG,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAU,SAAA,aAAoC;IAG9CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAkG;IAYhGD,EAVA,CAAAgB,UAAA,KAAA0F,6DAAA,kBAA2C,KAAAC,6DAAA,kBAOmC,KAAAC,6DAAA,kBAGmC;IAInH5G,EAAA,CAAAG,YAAA,EAAe;IAIXH,EAFJ,CAAAC,cAAA,0BAA0E,eAC3B,eACR;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAgB,UAAA,KAAA6F,6DAAA,mBAAoE;IActE7G,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,UAAA,KAAA8F,gEAAA,qBAA2G;IAG3G9G,EAAA,CAAAC,cAAA,kBAAuE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAA4F,gFAAA;MAAA/G,EAAA,CAAAqB,aAAA,CAAAiF,IAAA;MAAA,MAAA5B,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAqG,OAAA,EAAS;MAAA,OAAAhH,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC7E,EAAA,CAAAE,MAAA,oBAAE;IAG/EF,EAH+E,CAAAG,YAAA,EAAS,EAC9E,EACS,EACT;;;;IA3DAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA4E,eAAA,kBAAA5E,MAAA,CAAA4E,eAAA,CAAAxE,IAAA,KAAkC;IAESf,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAsG,mBAAA,MAAqC;IAIrCjH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAsG,mBAAA,IAAAtG,MAAA,CAAAuG,MAAA,CAAA5E,MAAA,KAAqD;IAShGtC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,SAAA,CAAe;IAMfpF,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,IAAAzE,MAAA,CAAA4E,eAAA,IAAA5E,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,EAAmE;IAInEvF,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,MAAAzE,MAAA,CAAA4E,eAAA,KAAA5E,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,GAAuE;IASzEvF,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,MAAA5B,MAAA,CAAAsG,mBAAA,aAAAtG,MAAA,CAAAuG,MAAA,CAAA5E,MAAA,MACF;IAEMtC,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAmF,UAAA,KAAoB;IAgBjB9F,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAwG,mBAAA,CAAyB;;;;;IAvDxCnH,EArLA,CAAAgB,UAAA,IAAAoG,sDAAA,uBAA+E,IAAAC,sDAAA,uBAqLZ;;;;IArLzDrH,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA2G,oBAAA,CAA0B;IAqL1BtH,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAA2G,oBAAA,CAA2B;;;AD9JvC,OAAM,MAAOC,qBAAqB;EAoDhCC,YACUC,aAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAtDf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAX,mBAAmB,GAAY,IAAI;IAQ5C;IACS,KAAAG,oBAAoB,GAAY,KAAK;IAGpC,KAAAS,oBAAoB,GAAG,IAAInI,YAAY,EAAa;IACpD,KAAAiF,KAAK,GAAG,IAAIjF,YAAY,EAAQ;IAChC,KAAAoI,aAAa,GAAG,IAAIpI,YAAY,EAAU;IAC1C,KAAAqI,SAAS,GAAG,IAAIrI,YAAY,EAAU;IAEhD;IACU,KAAAsI,mBAAmB,GAAG,IAAItI,YAAY,EAAe;IACrD,KAAAuI,cAAc,GAAG,IAAIvI,YAAY,EAAU;IAIrD;IACA,KAAAsH,MAAM,GAAgB,EAAE;IACxB,KAAA7E,eAAe,GAAgB,EAAE;IACjC,KAAAkD,eAAe,GAAqB,IAAI;IACxC,KAAA0B,mBAAmB,GAAW,CAAC;IAC/B,KAAA7B,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAS,WAAW,GAAW,CAAC;IACvB,KAAAuC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,CAAC;IAExB;IACA,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAApG,oBAAoB,GAAW,CAAC;IAChC,KAAAgB,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAgC,eAAe,GAAG,CAChB;MAAE5E,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IACD,KAAAiD,gBAAgB,GAAW,CAAC;IAC5B,KAAA2B,kBAAkB,GAAgB,EAAE,CAAC,CAAC;EAMlC;EAEJkD,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MACzE,IAAI,CAACL,UAAU,EAAE;IACnB;EACF;EAEA;EACA,IAAIrD,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAAC9C,eAAe,CAACC,MAAM;EACpC;EAEA,IAAIE,kBAAkBA,CAAA;IACpB,OAAOsG,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC5D,qBAAqB,GAAG,IAAI,CAACmD,gBAAgB,CAAC;EACtE;EAEA,IAAIlF,iBAAiBA,CAAA;IACnB,OAAO0F,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC1D,kBAAkB,CAAC/C,MAAM,GAAG,IAAI,CAACgG,gBAAgB,CAAC;EAC1E;EAEA;EACA,IAAIxC,UAAUA,CAAA;IACZ,OAAOgD,IAAI,CAACC,IAAI,CAAC,IAAI,CAACV,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;EAEA,IAAIpC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACH,WAAW,GAAG,IAAI,CAACC,UAAU;EAC3C;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACF,WAAW,GAAG,CAAC;EAC7B;EAEA,IAAIzD,wBAAwBA,CAAA;IAC1B,MAAM4G,UAAU,GAAG,CAAC,IAAI,CAAC9G,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACoG,gBAAgB;IAC1E,MAAMW,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACV,gBAAgB;IACnD,OAAO,IAAI,CAACjG,eAAe,CAAC6G,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACzD;EAEA,IAAI3D,uBAAuBA,CAAA;IACzB,MAAM0D,UAAU,GAAG,CAAC,IAAI,CAAC9F,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAACoF,gBAAgB;IACzE,MAAMW,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACV,gBAAgB;IACnD,OAAO,IAAI,CAACjD,kBAAkB,CAAC6D,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC5D;EAEA;EACAT,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAACvD,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC+D,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACT,WAAW,EAAE;MACrB,IAAI,CAACrG,eAAe,GAAG,EAAE;MACzB;IACF;IAEA;IACA,MAAMgH,WAAW,GAAG,IAAI,CAACjB,QAAQ,CAAC,CAAC;IAEnC,IAAI,CAACV,cAAc,CAAC4B,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACd,WAAW;QAC9Be,YAAY,EAAE,IAAI,CAACb,WAAW;QAC9Bc,SAAS,EAAE,IAAI,CAAC7D,WAAW;QAC3B8D,QAAQ,EAAEN,WAAW;QACrBO,KAAK,EAAE,IAAI,CAAC9F,UAAU,IAAI+E;;KAE7B,CAAC,CAACgB,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YACvExI,EAAE,EAAEwI,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBtJ,IAAI,EAAEqJ,OAAO,CAACE,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEN,OAAO,CAACK,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEV,OAAO,CAACW;WACf,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAAC1C,YAAY,GAAG0B,GAAG,CAACiB,UAAU,IAAI,CAAC;UAEvC;UACA,IAAI,CAAC9D,MAAM,GAAG+C,SAAS;UAEvB;UACA,IAAI,CAACgB,qBAAqB,EAAE;UAE5B;UACA,IAAI,CAACC,sBAAsB,EAAE;QAC/B,CAAC,MAAM;UACL,IAAI,CAACvD,cAAc,CAACwD,YAAY,CAACpB,GAAG,CAACqB,OAAO,IAAI,QAAQ,CAAC;QAC3D;QACA,IAAI,CAAChG,SAAS,GAAG,KAAK;MACxB,CAAC;MACDiG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1D,cAAc,CAACwD,YAAY,CAAC,UAAU,IAAIE,KAAK,CAACC,OAAO,IAAI,MAAM,CAAC,CAAC;QACxE,IAAI,CAAClG,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACAgE,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACV,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAACjB,cAAc,CAAC4B,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACd,WAAW;QAC9B6C,WAAW,EAAE,IAAI,CAAC5C,UAAU;QAC5Bc,YAAY,EAAE,IAAI,CAACb,WAAW;QAC9Bc,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACE,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnC,cAAc,GAAGkC,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YAC3ExI,EAAE,EAAEwI,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBtJ,IAAI,EAAEqJ,OAAO,CAACE,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEN,OAAO,CAACK,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEV,OAAO,CAACW;WACf,CAAC,CAAC,IAAI,EAAE;QACX;MACF,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfG,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACAH,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAChE,MAAM,CAAC5E,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC2E,mBAAmB,GAAG6B,IAAI,CAAC2C,GAAG,CAAC,CAAC,EAAE3C,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAAC5D,iBAAiB,EAAE,IAAI,CAACZ,MAAM,CAAC5E,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACiD,eAAe,GAAG,IAAI,CAAC2B,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAC1B,eAAe,GAAG,IAAI;MAC3B,IAAI,CAAC0B,mBAAmB,GAAG,CAAC;IAC9B;EACF;EAEAV,eAAeA,CAAA;IACb,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAC1B,eAAe,GAAG,IAAI,CAAC2B,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACe,aAAa,CAAC2D,IAAI,CAAC,IAAI,CAAC1E,mBAAmB,CAAC;IACnD;EACF;EAEAR,WAAWA,CAAA;IACT,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAAC5E,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAAC2E,mBAAmB,EAAE;MAC1B,IAAI,CAAC1B,eAAe,GAAG,IAAI,CAAC2B,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACgB,SAAS,CAAC0D,IAAI,CAAC,IAAI,CAAC1E,mBAAmB,CAAC;IAC/C;EACF;EAEAd,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB,IAAI,CAACwC,oBAAoB,CAAC4D,IAAI,CAAC,IAAI,CAACpG,eAAe,CAAC;IACtD;EACF;EAEAa,eAAeA,CAACwF,KAAgB;IAC9B,OAAO,IAAI,CAAC/D,cAAc,CAACgE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAClK,EAAE,KAAKgK,KAAK,CAAChK,EAAE,CAAC;EACvE;EAEAoF,OAAOA,CAAA;IACL,IAAI,CAACnC,KAAK,CAAC8G,IAAI,EAAE;EACnB;EAEA;EACAI,WAAWA,CAACC,eAAkC;IAC5C;IACA,IAAI,CAACxD,UAAU,EAAE;IAEjB;IACA,MAAMyD,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;IACrD,IAAI,CAACzE,aAAa,CAAC0E,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAG,gBAAgBA,CAACtB,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACnD,cAAc,CAACwD,YAAY,CAAC,cAAc,CAAC;MAChD;IACF;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA;EACAvK,WAAWA,CAACyL,SAAoB;IAC9B,IAAIA,SAAS,CAACvB,IAAI,IAAI,CAACuB,SAAS,CAAC3B,OAAO,EAAE;MACxC;MACA,IAAI,CAAC0B,gBAAgB,CAACC,SAAS,CAACvB,IAAI,CAAC;IACvC;IAEA,OAAOuB,SAAS,CAAC3B,OAAO,IAAI2B,SAAS,CAAC7B,YAAY,IAAI,EAAE;EAC1D;EAEA;EACA7G,iBAAiBA,CAAC2I,QAAgB;IAChC,IAAI,CAAC5I,gBAAgB,GAAG4I,QAAQ;IAChC,IAAI,CAACnE,cAAc,CAACwD,IAAI,CAACW,QAAQ,CAAC;IAClC,IAAI,IAAI,CAAC5D,WAAW,EAAE;MACpB,IAAI,CAACF,UAAU,EAAE;IACnB;EACF;EAEA;EACA+D,oBAAoBA,CAACC,eAAkC;IACrD,IAAI,CAAClF,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACjC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACwC,cAAc,CAAC;IAElD;IACA,IAAI,CAAC3F,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACgB,mBAAmB,GAAG,CAAC;IAE5B;IACA,IAAI,CAACsF,UAAU,EAAE;IAEjB,MAAMyD,QAAQ,GAAGO,eAAe,IAAI,IAAI,CAACN,YAAY;IACrD,IAAI,CAACzE,aAAa,CAAC0E,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAlH,gBAAgBA,CAAA;IACd,IAAI,CAACmD,mBAAmB,CAACyD,IAAI,CAAC,IAAI,CAACtG,kBAAkB,CAAC;IACtD,IAAI,CAACiC,oBAAoB,GAAG,KAAK;EACnC;EAEA;EACA1C,eAAeA,CAAA;IACb,IAAI,CAACS,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACiC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACN,OAAO,EAAE;EAChB;EAEA;EACAyF,oBAAoBA,CAACb,KAAgB;IACnC,MAAMc,KAAK,GAAG,IAAI,CAACrH,kBAAkB,CAACsH,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAChL,EAAE,KAAKgK,KAAK,CAAChK,EAAE,CAAC;IAC3E,IAAI8K,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACrH,kBAAkB,CAACwH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACrH,kBAAkB,CAACyH,IAAI,CAAClB,KAAK,CAAC;IACrC;EACF;EAEA;EACAmB,mBAAmBA,CAACnB,KAAgB;IAClC,OAAO,IAAI,CAACvG,kBAAkB,CAACwG,IAAI,CAACe,GAAG,IAAIA,GAAG,CAAChL,EAAE,KAAKgK,KAAK,CAAChK,EAAE,CAAC;EACjE;EAEA;EACAF,cAAcA,CAACkK,KAAgB;IAC7B,IAAI,CAAC,IAAI,CAACmB,mBAAmB,CAACnB,KAAK,CAAC,EAAE;MACpC,IAAI,CAACvG,kBAAkB,CAACyH,IAAI,CAAClB,KAAK,CAAC;MACnC,IAAI,CAACX,qBAAqB,EAAE;MAC5B,IAAI,CAAC+B,yBAAyB,EAAE;IAClC;EACF;EAEApK,eAAeA,CAACgJ,KAAgB;IAC9B,MAAMc,KAAK,GAAG,IAAI,CAACrH,kBAAkB,CAACsH,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAChL,EAAE,KAAKgK,KAAK,CAAChK,EAAE,CAAC;IAC3E,IAAI8K,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACrH,kBAAkB,CAACwH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACxC,IAAI,CAACzB,qBAAqB,EAAE;MAC5B,IAAI,CAAC+B,yBAAyB,EAAE;IAClC;EACF;EAEA7I,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC/B,wBAAwB,CAAC6K,OAAO,CAACrB,KAAK,IAAG;MAC5C,IAAI,CAAC,IAAI,CAACmB,mBAAmB,CAACnB,KAAK,CAAC,EAAE;QACpC,IAAI,CAACvG,kBAAkB,CAACyH,IAAI,CAAClB,KAAK,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAACX,qBAAqB,EAAE;IAC5B,IAAI,CAAC+B,yBAAyB,EAAE;EAClC;EAEA3I,kBAAkBA,CAAA;IAChB,IAAI,CAACgB,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC4F,qBAAqB,EAAE;IAC5B,IAAI,CAAC/H,mBAAmB,GAAG,CAAC;EAC9B;EAEA+H,qBAAqBA,CAAA;IACnB;IACA,MAAMiC,WAAW,GAAG,IAAI,CAAC7H,kBAAkB,CAAC8E,GAAG,CAACyC,GAAG,IAAIA,GAAG,CAAChL,EAAE,CAAC;IAC9D,IAAI,CAACS,eAAe,GAAG,IAAI,CAAC6E,MAAM,CAACiG,MAAM,CAACvB,KAAK,IAAI,CAACsB,WAAW,CAACE,QAAQ,CAACxB,KAAK,CAAChK,EAAE,CAAC,CAAC;IACnF,IAAI,CAACyL,0BAA0B,EAAE;EACnC;EAEAA,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACnL,oBAAoB,GAAG,IAAI,CAACM,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,GAAG,CAAC,EAAE;MACtF,IAAI,CAACN,oBAAoB,GAAG,IAAI,CAACM,kBAAkB;IACrD;EACF;EAEAwK,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAAC9J,mBAAmB,GAAG,IAAI,CAACE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,GAAG,CAAC,EAAE;MACnF,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACE,iBAAiB;IACnD;EACF;EAEA;EACAnB,iBAAiBA,CAACqL,IAAY;IAC5B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC9K,kBAAkB,EAAE;MAChD,IAAI,CAACN,oBAAoB,GAAGoL,IAAI;IAClC;EACF;EAEArK,gBAAgBA,CAACqK,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAClK,iBAAiB,EAAE;MAC/C,IAAI,CAACF,mBAAmB,GAAGoK,IAAI;IACjC;EACF;EAEA;EACAC,QAAQA,CAACD,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACxH,UAAU,EAAE;MACxC,IAAI,CAACD,WAAW,GAAGyH,IAAI;MACvB,IAAI,CAAC9E,UAAU,EAAE;IACnB;EACF;EAEA5C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,YAAY,EAAE;MACrB,IAAI,CAACH,WAAW,EAAE;MAClB,IAAI,CAAC2C,UAAU,EAAE;IACnB;EACF;EAEA9C,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACK,eAAe,EAAE;MACxB,IAAI,CAACF,WAAW,EAAE;MAClB,IAAI,CAAC2C,UAAU,EAAE;IACnB;EACF;EAEAgF,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC3H,WAAW,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACA,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC2C,UAAU,EAAE;IACnB;EACF;EAEAiF,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5H,WAAW,KAAK,IAAI,CAACC,UAAU,EAAE;MACxC,IAAI,CAACD,WAAW,GAAG,IAAI,CAACC,UAAU;MAClC,IAAI,CAAC0C,UAAU,EAAE;IACnB;EACF;EAEA;EACAzE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC7B,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACsG,UAAU,EAAE;EACnB;;;uCA/bWjB,qBAAqB,EAAAvH,EAAA,CAAA0N,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA5N,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9N,EAAA,CAAA0N,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArBzG,qBAAqB;MAAA0G,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCzBlCpO,EAAA,CAAAgB,UAAA,IAAAsN,4CAAA,gCAAAtO,EAAA,CAAAuO,sBAAA,CAA0D;;;qBDuB9C1O,YAAY,EAAA2O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3O,YAAY,EAAA4O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAnB,EAAA,CAAAoB,eAAA,EAAApB,EAAA,CAAAqB,mBAAA,EAAArB,EAAA,CAAAsB,qBAAA,EAAAtB,EAAA,CAAAuB,qBAAA,EAAAvB,EAAA,CAAAwB,iBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAEtP,eAAe,EAAA6N,EAAA,CAAA0B,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}