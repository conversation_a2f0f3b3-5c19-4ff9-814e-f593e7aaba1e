{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbSpinnerModule } from '@nebular/theme';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"nb-spinner\", 34);\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 50);\n  }\n  if (rf & 2) {\n    const image_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r6), i0.ɵɵsanitizeUrl)(\"alt\", image_r6.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵtemplate(2, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template, 1, 2, \"img\", 45)(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"div\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template_button_click_9_listener() {\n      const image_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.moveToSelected(image_r6));\n    });\n    i0.ɵɵelement(10, \"i\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const image_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", image_r6.id, \"\");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4, \"\\u627E\\u4E0D\\u5230\\u53EF\\u9078\\u64C7\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template, 11, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template, 5, 0, \"div\", 26);\n    i0.ɵɵelementStart(4, \"div\", 37)(5, \"div\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToAvailablePage(ctx_r1.availableCurrentPage - 1));\n    });\n    i0.ɵɵelement(9, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToAvailablePage(ctx_r1.availableCurrentPage + 1));\n    });\n    i0.ɵɵelement(11, \"i\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedAvailableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.availableCurrentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.availablePageCount, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableCurrentPage <= 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableCurrentPage >= ctx_r1.availablePageCount);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 50);\n  }\n  if (rf & 2) {\n    const image_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(image_r8), i0.ɵɵsanitizeUrl)(\"alt\", image_r8.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template_button_click_1_listener() {\n      const image_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.moveToAvailable(image_r8));\n    });\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template, 1, 2, \"img\", 45)(5, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getImageUrl(image_r8));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getImageUrl(image_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r8.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", image_r8.id, \"\");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4, \"\\u5C1A\\u672A\\u9078\\u64C7\\u4EFB\\u4F55\\u5716\\u7247\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToSelectedPage(ctx_r1.selectedCurrentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToSelectedPage(ctx_r1.selectedCurrentPage + 1));\n    });\n    i0.ɵɵelement(7, \"i\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.selectedCurrentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.selectedPageCount, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedCurrentPage <= 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedCurrentPage >= ctx_r1.selectedPageCount);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"label\", 7);\n    i0.ɵɵtext(7, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-select\", 8);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChanged($event));\n    });\n    i0.ɵɵtemplate(9, ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"label\", 7);\n    i0.ɵɵtext(12, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchTermChange());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13)(17, \"h6\", 14);\n    i0.ɵɵtext(18, \"\\u53EF\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 15);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template, 5, 0, \"div\", 16)(22, ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template, 12, 6, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 18)(24, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveAllToSelected());\n    });\n    i0.ɵɵelement(25, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveAllToAvailable());\n    });\n    i0.ɵɵelement(27, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 12)(29, \"div\", 13)(30, \"h6\", 14);\n    i0.ɵɵtext(31, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 23);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 24);\n    i0.ɵɵtemplate(35, ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template, 11, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template, 5, 0, \"div\", 26)(37, ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template, 8, 4, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"nb-card-footer\", 28)(39, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onCancelBinding();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(40, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onConfirmBinding();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r1.materialName || \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.totalAvailableRecords, \" \\u7B46\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.tempSelectedImages.length, \" \\u7B46\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedSelectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tempSelectedImages.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.tempSelectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.tempSelectedImages.length, \") \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"nb-spinner\", 34);\n    i0.ɵɵelementStart(2, \"div\", 35);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5716\\u7247\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(ctx_r1.previewingImage), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u53EF\\u9810\\u89BD\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_nb_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 57)(1, \"nb-card-header\", 58)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 41);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 61);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template, 4, 0, \"div\", 62)(13, ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template, 1, 2, \"img\", 63)(14, ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template, 4, 0, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 58)(16, \"div\", 65);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 59);\n    i0.ɵɵtemplate(19, ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template, 2, 1, \"button\", 66);\n    i0.ɵɵelementStart(20, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ref_r10 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r10.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.previewingImage && ctx_r1.getImageUrl(ctx_r1.previewingImage));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && (!ctx_r1.previewingImage || !ctx_r1.getImageUrl(ctx_r1.previewingImage)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_nb_card_0_Template, 43, 15, \"nb-card\", 1)(1, ImagePreviewComponent_ng_template_0_nb_card_1_Template, 22, 9, \"nb-card\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBindingInterface);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showBindingInterface);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService, pictureService, messageService) {\n    this.dialogService = dialogService;\n    this.pictureService = pictureService;\n    this.messageService = messageService;\n    this.isVisible = false;\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    // 新增綁定功能相關輸入參數\n    this.showBindingInterface = false;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    // 新增綁定功能相關輸出事件\n    this.confirmImageBinding = new EventEmitter();\n    this.categoryChange = new EventEmitter();\n    // 內部屬性，不再依賴外部傳入\n    this.images = [];\n    this.availableImages = [];\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    this.isLoading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalRecords = 0;\n    // 綁定模式相關屬性\n    this.categoryOptions = [{\n      value: 1,\n      label: '建材圖片'\n    }, {\n      value: 2,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = 1;\n    this.tempSelectedImages = []; // 臨時選擇的圖片\n  }\n  ngOnInit() {\n    this.loadImages();\n  }\n  ngOnChanges() {\n    // 當輸入參數變化時重新載入圖片\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\n      this.loadImages();\n    }\n  }\n  // 載入圖片的主要方法\n  loadImages() {\n    if (!this.buildCaseId && !this.materialId) {\n      return;\n    }\n    this.isLoading = true;\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n  }\n  // 載入可選擇的圖片\n  loadAvailableImages() {\n    if (!this.buildCaseId) {\n      this.availableImages = [];\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        cPictureType: this.pictureType,\n        PageIndex: this.currentPage,\n        PageSize: this.pageSize,\n        CName: this.searchTerm || undefined\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          // 轉換 API 回應為 ImageItem 格式\n          const newImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n          this.totalRecords = res.TotalItems || 0;\n          // 排除已選擇的圖片\n          const selectedIds = this.selectedImages.map(img => img.id);\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\n          // 合併可選和已選圖片作為完整圖片列表\n          this.images = [...this.availableImages, ...this.selectedImages];\n          // 設定初始預覽圖片\n          this.setInitialPreviewImage();\n        } else {\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\n        this.isLoading = false;\n      }\n    });\n  }\n  // 載入已選擇的圖片（如果有 materialId）\n  loadSelectedImages() {\n    if (!this.buildCaseId || !this.materialId) {\n      return;\n    }\n    this.pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CMaterialId: this.materialId,\n        cPictureType: this.pictureType,\n        PageIndex: 1,\n        PageSize: 999 // 載入所有已選圖片\n      }\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0) {\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid\n          })) || [];\n        }\n      },\n      error: error => {\n        console.error('載入已選擇圖片失敗:', error);\n      }\n    });\n  }\n  // 設定初始預覽圖片\n  setInitialPreviewImage() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    } else {\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    // 如果還沒有圖片，先載入圖片\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    // 開啟對話框\n    const template = imagePreviewRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 使用 CGuid 取得圖片資料的方法\n  getPictureByGuid(guid) {\n    if (!guid) {\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\n      return;\n    }\n    this.pictureService.apiPictureGetPictureGuidGet({\n      guid: guid\n    }).subscribe({\n      next: response => {\n        // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\n        console.log('取得圖片資料:', response);\n      },\n      error: error => {\n        this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\n      }\n    });\n  }\n  // 獲取圖片 URL，優先使用 CGuid\n  getImageUrl(imageItem) {\n    if (imageItem.guid && !imageItem.fullUrl) {\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\n      this.getPictureByGuid(imageItem.guid);\n    }\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\n  }\n  // 綁定模式相關方法\n  onCategoryChanged(category) {\n    this.selectedCategory = category;\n    this.categoryChange.emit(category);\n    if (this.buildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 開啟綁定界面\n  openBindingInterface(imageBindingRef) {\n    this.showBindingInterface = true;\n    this.tempSelectedImages = [...this.selectedImages];\n    if (this.images.length === 0) {\n      this.loadImages();\n    }\n    const template = imageBindingRef || this.imagePreview;\n    this.dialogService.open(template);\n  }\n  // 確認圖片綁定\n  onConfirmBinding() {\n    this.confirmImageBinding.emit(this.tempSelectedImages);\n    this.showBindingInterface = false;\n  }\n  // 取消綁定\n  onCancelBinding() {\n    this.tempSelectedImages = [];\n    this.showBindingInterface = false;\n    this.onClose();\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.tempSelectedImages.splice(index, 1);\n    } else {\n      this.tempSelectedImages.push(image);\n    }\n  }\n  // 檢查圖片是否被選中\n  isImageTempSelected(image) {\n    return this.tempSelectedImages.some(img => img.id === image.id);\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.PictureService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\",\n        buildCaseId: \"buildCaseId\",\n        materialId: \"materialId\",\n        pictureType: \"pictureType\",\n        searchTerm: \"searchTerm\",\n        showBindingInterface: \"showBindingInterface\",\n        materialName: \"materialName\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\",\n        confirmImageBinding: \"confirmImageBinding\",\n        categoryChange: \"categoryChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [\"class\", \"w-[95vw] max-w-[1200px] h-[85vh]\", 4, \"ngIf\"], [\"class\", \"w-[800px] h-[600px]\", 4, \"ngIf\"], [1, \"w-[95vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\"], [1, \"d-flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [1, \"form-label\"], [\"placeholder\", \"\\u9078\\u64C7\\u5716\\u7247\\u985E\\u5225\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex-1\", \"d-flex\", \"gap-3\", 2, \"overflow\", \"hidden\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"badge\", \"badge-secondary\"], [\"class\", \"flex-1 d-flex align-items-center justify-content-center\", 4, \"ngIf\"], [\"class\", \"flex-1 d-flex flex-column\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"80px\"], [\"title\", \"\\u5168\\u90E8\\u9078\\u64C7\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u9664\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"badge\", \"badge-primary\"], [1, \"flex-1\", 2, \"overflow-y\", \"auto\"], [\"class\", \"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\", \"style\", \"cursor: pointer;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex-1 d-flex align-items-center justify-content-center text-muted\", 4, \"ngIf\"], [\"class\", \"mt-3 d-flex justify-content-between align-items-center\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [3, \"value\"], [1, \"flex-1\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\"], [\"size\", \"large\"], [1, \"mt-2\"], [1, \"flex-1\", \"d-flex\", \"flex-column\"], [1, \"mt-3\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"small\", \"text-muted\"], [1, \"d-flex\", \"gap-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"d-flex\", \"align-items-center\", \"p-2\", \"mb-2\", \"border\", \"rounded\", \"hover-highlight\", 2, \"cursor\", \"pointer\"], [1, \"me-3\", 2, \"width\", \"60px\", \"height\", \"60px\", \"flex-shrink\", \"0\"], [\"class\", \"img-fluid rounded\", \"style\", \"width: 100%; height: 100%; object-fit: cover;\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\", 4, \"ngIf\"], [1, \"flex-1\", \"me-3\"], [1, \"fw-bold\", \"text-truncate\", 3, \"title\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"img-fluid\", \"rounded\", 2, \"width\", \"100%\", \"height\", \"100%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [1, \"w-100\", \"h-100\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"bg-light\", \"rounded\"], [1, \"fas\", \"fa-image\", \"text-muted\"], [1, \"flex-1\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"text-muted\"], [1, \"fas\", \"fa-images\", \"fa-2x\", \"mb-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"me-3\", 3, \"click\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"mb-2\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbSelectComponent, i1.NbOptionComponent, NbSpinnerModule, i1.NbSpinnerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\n.hover-highlight[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.hover-highlight[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.picklist-container[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .rounded[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .bg-light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.75rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.375rem;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #0d6efd;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #6c757d;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .text-truncate[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.picklist-container[_ngcontent-%COMP%]   .small[_ngcontent-%COMP%] {\\n  font-size: 0.875em;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-column[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\nnb-card[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-1[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.25rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-3[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-2[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-3[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .me-3[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mt-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\nnb-card[_ngcontent-%COMP%]   .h-100[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbSpinnerModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ctx_r1", "getImageUrl", "image_r6", "ɵɵsanitizeUrl", "name", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_img_2_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_div_3_Template", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template_button_click_9_listener", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "moveToSelected", "ɵɵtextInterpolate", "id", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_2_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_div_3_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_8_listener", "_r4", "goToAvailablePage", "availableCurrentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template_button_click_10_listener", "paginatedAvailableImages", "availableImages", "length", "ɵɵtextInterpolate2", "availablePageCount", "image_r8", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template_button_click_1_listener", "_r7", "moveToAvailable", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_img_4_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_div_5_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_4_listener", "_r9", "goToSelectedPage", "selectedCurrentPage", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template_button_click_6_listener", "selectedPageCount", "ɵɵtwoWayListener", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_nb_select_selectedChange_8_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "onCategoryChanged", "ImagePreviewComponent_ng_template_0_nb_card_0_nb_option_9_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_input_ngModelChange_13_listener", "searchTerm", "onSearchTermChange", "ImagePreviewComponent_ng_template_0_nb_card_0_div_21_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_22_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_24_listener", "moveAllToSelected", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_26_listener", "moveAllToAvailable", "ImagePreviewComponent_ng_template_0_nb_card_0_div_35_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_36_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_div_37_Template", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_39_listener", "ref_r10", "dialogRef", "onCancelBinding", "close", "ImagePreviewComponent_ng_template_0_nb_card_0_Template_button_click_41_listener", "onConfirmBinding", "materialName", "ɵɵtwoWayProperty", "categoryOptions", "totalAvailableRecords", "isLoading", "tempSelectedImages", "paginatedSelectedImages", "previewingImage", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template_button_click_0_listener", "_r12", "onToggleImageSelection", "isImageSelected", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_5_listener", "_r11", "onPreviousImage", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_8_listener", "onNextImage", "ImagePreviewComponent_ng_template_0_nb_card_1_div_12_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_img_13_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_div_14_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_button_19_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template_button_click_20_listener", "onClose", "currentPreviewIndex", "images", "showSelectionToggle", "ImagePreviewComponent_ng_template_0_nb_card_0_Template", "ImagePreviewComponent_ng_template_0_nb_card_1_Template", "showBindingInterface", "ImagePreviewComponent", "constructor", "dialogService", "pictureService", "messageService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "confirmImageBinding", "categoryChange", "currentPage", "pageSize", "totalRecords", "ngOnInit", "loadImages", "ngOnChanges", "buildCaseId", "materialId", "pictureType", "undefined", "loadAvailableImages", "loadSelectedImages", "apiPictureGetPictureListPost$Json", "body", "CBuildCaseId", "cPictureType", "PageIndex", "PageSize", "CName", "subscribe", "next", "res", "StatusCode", "newImages", "Entries", "map", "picture", "CId", "CPictureCode", "size", "thumbnailUrl", "CFile", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "TotalItems", "selectedIds", "img", "filter", "image", "includes", "setInitialPreviewImage", "showErrorMSG", "Message", "error", "message", "CMaterialId", "console", "Math", "max", "min", "emit", "some", "selected", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "getPictureByGuid", "apiPictureGetPictureGuidGet", "response", "log", "imageItem", "category", "openBindingInterface", "imageBindingRef", "toggleImageSelection", "index", "findIndex", "splice", "push", "isImageTempSelected", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "PictureService", "i3", "MessageService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "NbSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService, NbSpinnerModule } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\nimport { PictureService } from 'src/services/api/services';\r\nimport { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbSpinnerModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit, OnChanges {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n\r\n  // 新增輸入參數來接收圖片載入所需的資訊\r\n  @Input() buildCaseId?: number;\r\n  @Input() materialId?: number;\r\n  @Input() pictureType?: number;\r\n  @Input() searchTerm?: string;\r\n\r\n  // 新增綁定功能相關輸入參數\r\n  @Input() showBindingInterface: boolean = false;\r\n  @Input() materialName?: string;\r\n\r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  // 新增綁定功能相關輸出事件\r\n  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();\r\n  @Output() categoryChange = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  // 內部屬性，不再依賴外部傳入\r\n  images: ImageItem[] = [];\r\n  availableImages: ImageItem[] = [];\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n  isLoading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 50;\r\n  totalRecords: number = 0;\r\n\r\n  // 綁定模式相關屬性\r\n  categoryOptions = [\r\n    { value: 1, label: '建材圖片' },\r\n    { value: 2, label: '示意圖片' }\r\n  ];\r\n  selectedCategory: number = 1;\r\n  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private pictureService: PictureService,\r\n    private messageService: MessageService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadImages();\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // 當輸入參數變化時重新載入圖片\r\n    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 載入圖片的主要方法\r\n  loadImages(): void {\r\n    if (!this.buildCaseId && !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n  // 載入可選擇的圖片\r\n  loadAvailableImages(): void {\r\n    if (!this.buildCaseId) {\r\n      this.availableImages = [];\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: this.currentPage,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchTerm || undefined\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 轉換 API 回應為 ImageItem 格式\r\n          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n\r\n          this.totalRecords = res.TotalItems || 0;\r\n\r\n          // 排除已選擇的圖片\r\n          const selectedIds = this.selectedImages.map(img => img.id);\r\n          this.availableImages = newImages.filter(image => !selectedIds.includes(image.id));\r\n\r\n          // 合併可選和已選圖片作為完整圖片列表\r\n          this.images = [...this.availableImages, ...this.selectedImages];\r\n\r\n          // 設定初始預覽圖片\r\n          this.setInitialPreviewImage();\r\n        } else {\r\n          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 載入已選擇的圖片（如果有 materialId）\r\n  loadSelectedImages(): void {\r\n    if (!this.buildCaseId || !this.materialId) {\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CMaterialId: this.materialId,\r\n        cPictureType: this.pictureType,\r\n        PageIndex: 1,\r\n        PageSize: 999 // 載入所有已選圖片\r\n      }\r\n    }).subscribe({\r\n      next: (res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid\r\n          })) || [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入已選擇圖片失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 設定初始預覽圖片\r\n  setInitialPreviewImage(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    } else {\r\n      this.previewingImage = null;\r\n      this.currentPreviewIndex = 0;\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    // 如果還沒有圖片，先載入圖片\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    // 開啟對話框\r\n    const template = imagePreviewRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 使用 CGuid 取得圖片資料的方法\r\n  getPictureByGuid(guid: string): void {\r\n    if (!guid) {\r\n      this.messageService.showErrorMSG('圖片 GUID 不能為空');\r\n      return;\r\n    }\r\n\r\n    this.pictureService.apiPictureGetPictureGuidGet({ guid: guid })\r\n      .subscribe({\r\n        next: (response) => {\r\n          // TODO: 處理從 GetPictureByGuid API 取得的圖片資料\r\n          console.log('取得圖片資料:', response);\r\n        },\r\n        error: (error) => {\r\n          this.messageService.showErrorMSG(`取得圖片失敗: ${error.message || '未知錯誤'}`);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 獲取圖片 URL，優先使用 CGuid\r\n  getImageUrl(imageItem: ImageItem): string {\r\n    if (imageItem.guid && !imageItem.fullUrl) {\r\n      // 如果有 CGuid 但沒有 fullUrl，可以使用 CGuid 載入\r\n      this.getPictureByGuid(imageItem.guid);\r\n    }\r\n\r\n    return imageItem.fullUrl || imageItem.thumbnailUrl || '';\r\n  }\r\n\r\n  // 綁定模式相關方法\r\n  onCategoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.categoryChange.emit(category);\r\n    if (this.buildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 開啟綁定界面\r\n  openBindingInterface(imageBindingRef?: TemplateRef<any>) {\r\n    this.showBindingInterface = true;\r\n    this.tempSelectedImages = [...this.selectedImages];\r\n\r\n    if (this.images.length === 0) {\r\n      this.loadImages();\r\n    }\r\n\r\n    const template = imageBindingRef || this.imagePreview;\r\n    this.dialogService.open(template);\r\n  }\r\n\r\n  // 確認圖片綁定\r\n  onConfirmBinding() {\r\n    this.confirmImageBinding.emit(this.tempSelectedImages);\r\n    this.showBindingInterface = false;\r\n  }\r\n\r\n  // 取消綁定\r\n  onCancelBinding() {\r\n    this.tempSelectedImages = [];\r\n    this.showBindingInterface = false;\r\n    this.onClose();\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.tempSelectedImages.splice(index, 1);\r\n    } else {\r\n      this.tempSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否被選中\r\n  isImageTempSelected(image: ImageItem): boolean {\r\n    return this.tempSelectedImages.some(img => img.id === image.id);\r\n  }\r\n}\r\n", "<!-- 圖片預覽/綁定對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <!-- 綁定模式 - Picklist 風格 -->\r\n  <nb-card *ngIf=\"showBindingInterface\" class=\"w-[95vw] max-w-[1200px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ materialName || '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"d-flex flex-column\" style=\"height: calc(100% - 120px); overflow: hidden;\">\r\n      \r\n      <!-- 控制區 -->\r\n      <div class=\"d-flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">圖片類別</label>\r\n          <nb-select [(selected)]=\"selectedCategory\" placeholder=\"選擇圖片類別\"\r\n            (selectedChange)=\"onCategoryChanged($event)\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"form-label\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"form-control\" placeholder=\"搜尋圖片名稱...\" \r\n            [(ngModel)]=\"searchTerm\" (ngModelChange)=\"onSearchTermChange()\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Picklist 容器 -->\r\n      <div class=\"flex-1 d-flex gap-3\" style=\"overflow: hidden;\">\r\n        \r\n        <!-- 可選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">可選擇圖片</h6>\r\n            <span class=\"badge badge-secondary\">{{ totalAvailableRecords }} 筆</span>\r\n          </div>\r\n          \r\n          <!-- 載入中狀態 -->\r\n          <div *ngIf=\"isLoading\" class=\"flex-1 d-flex align-items-center justify-content-center\">\r\n            <div class=\"text-center\">\r\n              <nb-spinner size=\"large\"></nb-spinner>\r\n              <div class=\"mt-2\">載入圖片中...</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 可選擇圖片列表 -->\r\n          <div *ngIf=\"!isLoading\" class=\"flex-1 d-flex flex-column\">\r\n            <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n              <div *ngFor=\"let image of paginatedAvailableImages\" \r\n                class=\"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\" \r\n                style=\"cursor: pointer;\">\r\n                <!-- 圖片縮圖 -->\r\n                <div class=\"me-3\" style=\"width: 60px; height: 60px; flex-shrink: 0;\">\r\n                  <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                    class=\"img-fluid rounded\" style=\"width: 100%; height: 100%; object-fit: cover;\" />\r\n                  <div *ngIf=\"!getImageUrl(image)\" class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                    <i class=\"fas fa-image text-muted\"></i>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- 圖片資訊 -->\r\n                <div class=\"flex-1 me-3\">\r\n                  <div class=\"fw-bold text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                  <div class=\"small text-muted\">ID: {{ image.id }}</div>\r\n                </div>\r\n                \r\n                <!-- 選擇按鈕 -->\r\n                <button class=\"btn btn-sm btn-outline-primary\" (click)=\"moveToSelected(image)\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"availableImages.length === 0\" class=\"flex-1 d-flex align-items-center justify-content-center text-muted\">\r\n              <div class=\"text-center\">\r\n                <i class=\"fas fa-images fa-2x mb-2\"></i>\r\n                <div>找不到可選擇的圖片</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分頁控制 -->\r\n            <div class=\"mt-3 d-flex justify-content-between align-items-center\">\r\n              <div class=\"small text-muted\">\r\n                第 {{ availableCurrentPage }} 頁，共 {{ availablePageCount }} 頁\r\n              </div>\r\n              <div class=\"d-flex gap-1\">\r\n                <button class=\"btn btn-sm btn-outline-secondary\" \r\n                  [disabled]=\"availableCurrentPage <= 1\"\r\n                  (click)=\"goToAvailablePage(availableCurrentPage - 1)\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n                <button class=\"btn btn-sm btn-outline-secondary\"\r\n                  [disabled]=\"availableCurrentPage >= availablePageCount\"\r\n                  (click)=\"goToAvailablePage(availableCurrentPage + 1)\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中間控制按鈕 -->\r\n        <div class=\"d-flex flex-column justify-content-center gap-2\" style=\"width: 80px;\">\r\n          <button class=\"btn btn-primary btn-sm\" \r\n            [disabled]=\"availableImages.length === 0\"\r\n            (click)=\"moveAllToSelected()\"\r\n            title=\"全部選擇\">\r\n            <i class=\"fas fa-angle-double-right\"></i>\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\"\r\n            [disabled]=\"tempSelectedImages.length === 0\"\r\n            (click)=\"moveAllToAvailable()\"\r\n            title=\"全部移除\">\r\n            <i class=\"fas fa-angle-double-left\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 已選擇區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0\">已選擇圖片</h6>\r\n            <span class=\"badge badge-primary\">{{ tempSelectedImages.length }} 筆</span>\r\n          </div>\r\n          \r\n          <!-- 已選擇圖片列表 -->\r\n          <div class=\"flex-1\" style=\"overflow-y: auto;\">\r\n            <div *ngFor=\"let image of paginatedSelectedImages\" \r\n              class=\"d-flex align-items-center p-2 mb-2 border rounded hover-highlight\" \r\n              style=\"cursor: pointer;\">\r\n              <!-- 移除按鈕 -->\r\n              <button class=\"btn btn-sm btn-outline-danger me-3\" (click)=\"moveToAvailable(image)\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              \r\n              <!-- 圖片縮圖 -->\r\n              <div class=\"me-3\" style=\"width: 60px; height: 60px; flex-shrink: 0;\">\r\n                <img *ngIf=\"getImageUrl(image)\" [src]=\"getImageUrl(image)\" [alt]=\"image.name\"\r\n                  class=\"img-fluid rounded\" style=\"width: 100%; height: 100%; object-fit: cover;\" />\r\n                <div *ngIf=\"!getImageUrl(image)\" class=\"w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded\">\r\n                  <i class=\"fas fa-image text-muted\"></i>\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- 圖片資訊 -->\r\n              <div class=\"flex-1\">\r\n                <div class=\"fw-bold text-truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                <div class=\"small text-muted\">ID: {{ image.id }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 空狀態 -->\r\n          <div *ngIf=\"tempSelectedImages.length === 0\" class=\"flex-1 d-flex align-items-center justify-content-center text-muted\">\r\n            <div class=\"text-center\">\r\n              <i class=\"fas fa-inbox fa-2x mb-2\"></i>\r\n              <div>尚未選擇任何圖片</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 分頁控制 -->\r\n          <div *ngIf=\"tempSelectedImages.length > 0\" class=\"mt-3 d-flex justify-content-between align-items-center\">\r\n            <div class=\"small text-muted\">\r\n              第 {{ selectedCurrentPage }} 頁，共 {{ selectedPageCount }} 頁\r\n            </div>\r\n            <div class=\"d-flex gap-1\">\r\n              <button class=\"btn btn-sm btn-outline-secondary\" \r\n                [disabled]=\"selectedCurrentPage <= 1\"\r\n                (click)=\"goToSelectedPage(selectedCurrentPage - 1)\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              <button class=\"btn btn-sm btn-outline-secondary\"\r\n                [disabled]=\"selectedCurrentPage >= selectedPageCount\"\r\n                (click)=\"goToSelectedPage(selectedCurrentPage + 1)\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onCancelBinding(); ref.close()\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onConfirmBinding(); ref.close()\" \r\n        [disabled]=\"tempSelectedImages.length === 0\">\r\n        確定選擇 ({{ tempSelectedImages.length }})\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n\r\n  <!-- 預覽模式 -->\r\n  <nb-card *ngIf=\"!showBindingInterface\" class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <!-- 載入中狀態 -->\r\n      <div *ngIf=\"isLoading\" class=\"text-center\">\r\n        <nb-spinner size=\"large\"></nb-spinner>\r\n        <div class=\"mt-2\">載入圖片中...</div>\r\n      </div>\r\n      \r\n      <!-- 圖片顯示 -->\r\n      <img *ngIf=\"!isLoading && previewingImage && getImageUrl(previewingImage)\" \r\n        [src]=\"getImageUrl(previewingImage)\"\r\n        [alt]=\"previewingImage.name\" \r\n        class=\"max-w-full max-h-full object-contain\" />\r\n      \r\n      <!-- 無圖片狀態 -->\r\n      <div *ngIf=\"!isLoading && (!previewingImage || !getImageUrl(previewingImage))\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>無可預覽的圖片</div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAmD,eAAe;AACjH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAA0BC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;ICY1DC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAsBAT,EADF,CAAAC,cAAA,cAAuF,cAC5D;IACvBD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAE9BF,EAF8B,CAAAG,YAAA,EAAM,EAC5B,EACF;;;;;IAUEH,EAAA,CAAAU,SAAA,cACoF;;;;;IADzBV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAb,EAAA,CAAAc,aAAA,CAA0B,QAAAD,QAAA,CAAAE,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IALRH,EAJF,CAAAC,cAAA,cAE2B,cAE4C;IAGnED,EAFA,CAAAgB,UAAA,IAAAC,yEAAA,kBACoF,IAAAC,yEAAA,kBACmC;IAGzHlB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAyB,cACiC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAClDF,EADkD,CAAAG,YAAA,EAAM,EAClD;IAGNH,EAAA,CAAAC,cAAA,iBAA+E;IAAhCD,EAAA,CAAAmB,UAAA,mBAAAC,4FAAA;MAAA,MAAAP,QAAA,GAAAb,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAe,cAAA,CAAAb,QAAA,CAAqB;IAAA,EAAC;IAC5Eb,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAjBIH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAwB;IAExBb,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAAC,QAAA,EAAyB;IAOIb,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAS,QAAA,CAAAE,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA2B,iBAAA,CAAAd,QAAA,CAAAE,IAAA,CAAgB;IAC1Cf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,kBAAA,SAAAK,QAAA,CAAAe,EAAA,KAAkB;;;;;IAYpD5B,EADF,CAAAC,cAAA,cAAqH,cAC1F;IACvBD,EAAA,CAAAU,SAAA,YAAwC;IACxCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAElBF,EAFkB,CAAAG,YAAA,EAAM,EAChB,EACF;;;;;;IAhCNH,EADF,CAAAC,cAAA,cAA0D,cACV;IAC5CD,EAAA,CAAAgB,UAAA,IAAAa,mEAAA,mBAE2B;IAqB7B7B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgB,UAAA,IAAAc,mEAAA,kBAAqH;IASnH9B,EADF,CAAAC,cAAA,cAAoE,cACpC;IAC5BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,iBAGgC;IAAtDD,EAAA,CAAAmB,UAAA,mBAAAY,sFAAA;MAAA/B,EAAA,CAAAqB,aAAA,CAAAW,GAAA;MAAA,MAAArB,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsB,iBAAA,CAAAtB,MAAA,CAAAuB,oBAAA,GAAyC,CAAC,CAAC;IAAA,EAAC;IACrDlC,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEwD;IAAtDD,EAAA,CAAAmB,UAAA,mBAAAgB,uFAAA;MAAAnC,EAAA,CAAAqB,aAAA,CAAAW,GAAA;MAAA,MAAArB,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsB,iBAAA,CAAAtB,MAAA,CAAAuB,oBAAA,GAAyC,CAAC,CAAC;IAAA,EAAC;IACrDlC,EAAA,CAAAU,SAAA,aAAoC;IAI5CV,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAnDqBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAyB,wBAAA,CAA2B;IA0B9CpC,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0B,eAAA,CAAAC,MAAA,OAAkC;IAUpCtC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,aAAA5B,MAAA,CAAAuB,oBAAA,0BAAAvB,MAAA,CAAA6B,kBAAA,aACF;IAGIxC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuB,oBAAA,MAAsC;IAKtClC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuB,oBAAA,IAAAvB,MAAA,CAAA6B,kBAAA,CAAuD;;;;;IA4CzDxC,EAAA,CAAAU,SAAA,cACoF;;;;;IADzBV,EAA3B,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,GAAAzC,EAAA,CAAAc,aAAA,CAA0B,QAAA2B,QAAA,CAAA1B,IAAA,CAAmB;;;;;IAE7Ef,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAU,SAAA,YAAuC;IACzCV,EAAA,CAAAG,YAAA,EAAM;;;;;;IAVRH,EAJF,CAAAC,cAAA,cAE2B,iBAE2D;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAuB,sFAAA;MAAA,MAAAD,QAAA,GAAAzC,EAAA,CAAAqB,aAAA,CAAAsB,GAAA,EAAApB,SAAA;MAAA,MAAAZ,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAiC,eAAA,CAAAH,QAAA,CAAsB;IAAA,EAAC;IACjFzC,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAAqE;IAGnED,EAFA,CAAAgB,UAAA,IAAA6B,mEAAA,kBACoF,IAAAC,mEAAA,kBACmC;IAGzH9C,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAAoB,cACsC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAEpDF,EAFoD,CAAAG,YAAA,EAAM,EAClD,EACF;;;;;IAZIH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,EAAwB;IAExBzC,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAC,WAAA,CAAA6B,QAAA,EAAyB;IAOIzC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAqC,QAAA,CAAA1B,IAAA,CAAoB;IAACf,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA2B,iBAAA,CAAAc,QAAA,CAAA1B,IAAA,CAAgB;IAC1Cf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,kBAAA,SAAAiC,QAAA,CAAAb,EAAA,KAAkB;;;;;IAOpD5B,EADF,CAAAC,cAAA,cAAwH,cAC7F;IACvBD,EAAA,CAAAU,SAAA,YAAuC;IACvCV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAEjBF,EAFiB,CAAAG,YAAA,EAAM,EACf,EACF;;;;;;IAIJH,EADF,CAAAC,cAAA,cAA0G,cAC1E;IAC5BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,iBAG8B;IAApDD,EAAA,CAAAmB,UAAA,mBAAA4B,sFAAA;MAAA/C,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsC,gBAAA,CAAAtC,MAAA,CAAAuC,mBAAA,GAAuC,CAAC,CAAC;IAAA,EAAC;IACnDlD,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAEsD;IAApDD,EAAA,CAAAmB,UAAA,mBAAAgC,sFAAA;MAAAnD,EAAA,CAAAqB,aAAA,CAAA2B,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAsC,gBAAA,CAAAtC,MAAA,CAAAuC,mBAAA,GAAuC,CAAC,CAAC;IAAA,EAAC;IACnDlD,EAAA,CAAAU,SAAA,YAAoC;IAG1CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAdFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,aAAA5B,MAAA,CAAAuC,mBAAA,0BAAAvC,MAAA,CAAAyC,iBAAA,aACF;IAGIpD,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuC,mBAAA,MAAqC;IAKrClD,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAuC,mBAAA,IAAAvC,MAAA,CAAAyC,iBAAA,CAAqD;;;;;;IAxKjEpD,EADF,CAAAC,cAAA,iBAA+E,qBAC7D;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,sBAA+F,aAGhD,aACvB,eACQ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,mBAC+C;IADpCD,EAAA,CAAAqD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAyD,kBAAA,CAAA9C,MAAA,CAAA+C,gBAAA,EAAAH,MAAA,MAAA5C,MAAA,CAAA+C,gBAAA,GAAAH,MAAA;MAAA,OAAAvD,EAAA,CAAAyB,WAAA,CAAA8B,MAAA;IAAA,EAA+B;IACxCvD,EAAA,CAAAmB,UAAA,4BAAAmC,2FAAAC,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAkBd,MAAA,CAAAgD,iBAAA,CAAAJ,MAAA,CAAyB;IAAA,EAAC;IAC5CvD,EAAA,CAAAgB,UAAA,IAAA4C,kEAAA,uBAAyE;IAI7E5D,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,iBACoE;IAAlED,EAAA,CAAAqD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAvD,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAyD,kBAAA,CAAA9C,MAAA,CAAAmD,UAAA,EAAAP,MAAA,MAAA5C,MAAA,CAAAmD,UAAA,GAAAP,MAAA;MAAA,OAAAvD,EAAA,CAAAyB,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IAACvD,EAAA,CAAAmB,UAAA,2BAAA0C,uFAAA;MAAA7D,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAiBd,MAAA,CAAAoD,kBAAA,EAAoB;IAAA,EAAC;IAErE/D,EAHI,CAAAG,YAAA,EACoE,EAChE,EACF;IAQAH,EALN,CAAAC,cAAA,eAA2D,eAGC,eACY,cACjD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;IAWNH,EARA,CAAAgB,UAAA,KAAAgD,6DAAA,kBAAuF,KAAAC,6DAAA,mBAQ7B;IAsD5DjE,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAkF,kBAIjE;IADbD,EAAA,CAAAmB,UAAA,mBAAA+C,gFAAA;MAAAlE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAwD,iBAAA,EAAmB;IAAA,EAAC;IAE7BnE,EAAA,CAAAU,SAAA,aAAyC;IAC3CV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGe;IADbD,EAAA,CAAAmB,UAAA,mBAAAiD,gFAAA;MAAApE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA0D,kBAAA,EAAoB;IAAA,EAAC;IAE9BrE,EAAA,CAAAU,SAAA,aAAwC;IAE5CV,EADE,CAAAG,YAAA,EAAS,EACL;IAKFH,EAFJ,CAAAC,cAAA,eAA0D,eACY,cACjD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAGNH,EAAA,CAAAC,cAAA,eAA8C;IAC5CD,EAAA,CAAAgB,UAAA,KAAAsD,6DAAA,mBAE2B;IAqB7BtE,EAAA,CAAAG,YAAA,EAAM;IAWNH,EARA,CAAAgB,UAAA,KAAAuD,6DAAA,kBAAwH,KAAAC,6DAAA,kBAQd;IAmBhHxE,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAuD,kBACsB;IAAzCD,EAAA,CAAAmB,UAAA,mBAAAsD,gFAAA;MAAAzE,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAAkB,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAiE,eAAA,EAAiB;MAAA,OAAA5E,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC7E,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtFH,EAAA,CAAAC,cAAA,kBAC+C;IADfD,EAAA,CAAAmB,UAAA,mBAAA2D,gFAAA;MAAA9E,EAAA,CAAAqB,aAAA,CAAAmC,GAAA;MAAA,MAAAkB,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAAoE,gBAAA,EAAkB;MAAA,OAAA/E,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEvE7E,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;IAvLNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAG,MAAA,CAAAqE,YAAA,gDACF;IAOiBhF,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAiF,gBAAA,aAAAtE,MAAA,CAAA+C,gBAAA,CAA+B;IAEV1D,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAuE,eAAA,CAAkB;IAQhDlF,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAiF,gBAAA,YAAAtE,MAAA,CAAAmD,UAAA,CAAwB;IAWY9D,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,kBAAA,KAAAG,MAAA,CAAAwE,qBAAA,YAA6B;IAI7DnF,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,SAAA,CAAe;IAQfpF,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,CAAgB;IA2DpBpF,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0B,eAAA,CAAAC,MAAA,OAAyC;IAMzCtC,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAA4C;IAWVtC,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,kBAAA,KAAAG,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,YAAiC;IAK5CtC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA2E,uBAAA,CAA0B;IA0B7CtF,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAAqC;IAQrCtC,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,KAAmC;IAuB3CtC,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OAA4C;IAC5CtC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA0E,kBAAA,CAAA/C,MAAA,OACF;;;;;IAqBAtC,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAU,SAAA,qBAAsC;IACtCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,wCAAQ;IAC5BF,EAD4B,CAAAG,YAAA,EAAM,EAC5B;;;;;IAGNH,EAAA,CAAAU,SAAA,cAGiD;;;;IAD/CV,EADA,CAAAI,UAAA,QAAAO,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,GAAAvF,EAAA,CAAAc,aAAA,CAAoC,QAAAH,MAAA,CAAA4E,eAAA,CAAAxE,IAAA,CACR;;;;;IAI9Bf,EAAA,CAAAC,cAAA,cAAiH;IAC/GD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACdF,EADc,CAAAG,YAAA,EAAM,EACd;;;;;;IAQJH,EAAA,CAAAC,cAAA,iBAA2G;IAAnCD,EAAA,CAAAmB,UAAA,mBAAAqE,yFAAA;MAAAxF,EAAA,CAAAqB,aAAA,CAAAoE,IAAA;MAAA,MAAA9E,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAA+E,sBAAA,EAAwB;IAAA,EAAC;IACxG1F,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,MAAA,CAAA4E,eAAA,IAAA5E,MAAA,CAAAgF,eAAA,CAAAhF,MAAA,CAAA4E,eAAA,uEACF;;;;;;IAvCFvF,EAFJ,CAAAC,cAAA,kBAAmE,yBACS,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,iBACyF;IAA5BD,EAAA,CAAAmB,UAAA,mBAAAyE,+EAAA;MAAA5F,EAAA,CAAAqB,aAAA,CAAAwE,IAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAmF,eAAA,EAAiB;IAAA,EAAC;IAC9G9F,EAAA,CAAAU,SAAA,YAAmC;IAACV,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC0B;IAAxBD,EAAA,CAAAmB,UAAA,mBAAA4E,+EAAA;MAAA/F,EAAA,CAAAqB,aAAA,CAAAwE,IAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASd,MAAA,CAAAqF,WAAA,EAAa;IAAA,EAAC;IACvBhG,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAU,SAAA,aAAoC;IAG9CV,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAkG;IAchGD,EAZA,CAAAgB,UAAA,KAAAiF,6DAAA,kBAA2C,KAAAC,6DAAA,kBASM,KAAAC,6DAAA,kBAGgE;IAInHnG,EAAA,CAAAG,YAAA,EAAe;IAGbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,UAAA,KAAAoF,gEAAA,qBAA2G;IAG3GpG,EAAA,CAAAC,cAAA,kBAAuE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAkF,gFAAA;MAAArG,EAAA,CAAAqB,aAAA,CAAAwE,IAAA;MAAA,MAAAnB,OAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAAmD,SAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAwB,aAAA;MAASb,MAAA,CAAA2F,OAAA,EAAS;MAAA,OAAAtG,EAAA,CAAAyB,WAAA,CAAEiD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAC7E,EAAA,CAAAE,MAAA,oBAAE;IAG/EF,EAH+E,CAAAG,YAAA,EAAS,EAC9E,EACS,EACT;;;;IA3CAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAG,MAAA,CAAA4E,eAAA,kBAAA5E,MAAA,CAAA4E,eAAA,CAAAxE,IAAA,KAAkC;IAESf,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA4F,mBAAA,MAAqC;IAGrCvG,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA4F,mBAAA,IAAA5F,MAAA,CAAA6F,MAAA,CAAAlE,MAAA,KAAqD;IAShGtC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,SAAA,CAAe;IAMfpF,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,IAAAzE,MAAA,CAAA4E,eAAA,IAAA5E,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,EAAmE;IAMnEvF,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAyE,SAAA,MAAAzE,MAAA,CAAA4E,eAAA,KAAA5E,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAA4E,eAAA,GAAuE;IAQ3EvF,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAuC,kBAAA,MAAA5B,MAAA,CAAA4F,mBAAA,aAAA5F,MAAA,CAAA6F,MAAA,CAAAlE,MAAA,MACF;IAEWtC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA8F,mBAAA,CAAyB;;;;;IAvCxCzG,EA5LA,CAAAgB,UAAA,IAAA0F,sDAAA,uBAA+E,IAAAC,sDAAA,sBA4LZ;;;;IA5LzD3G,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAiG,oBAAA,CAA0B;IA4L1B5G,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAiG,oBAAA,CAA2B;;;ADrKvC,OAAM,MAAOC,qBAAqB;EA+ChCC,YACUC,aAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAjDf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAX,mBAAmB,GAAY,IAAI;IAQ5C;IACS,KAAAG,oBAAoB,GAAY,KAAK;IAGpC,KAAAS,oBAAoB,GAAG,IAAIzH,YAAY,EAAa;IACpD,KAAAiF,KAAK,GAAG,IAAIjF,YAAY,EAAQ;IAChC,KAAA0H,aAAa,GAAG,IAAI1H,YAAY,EAAU;IAC1C,KAAA2H,SAAS,GAAG,IAAI3H,YAAY,EAAU;IAEhD;IACU,KAAA4H,mBAAmB,GAAG,IAAI5H,YAAY,EAAe;IACrD,KAAA6H,cAAc,GAAG,IAAI7H,YAAY,EAAU;IAIrD;IACA,KAAA4G,MAAM,GAAgB,EAAE;IACxB,KAAAnE,eAAe,GAAgB,EAAE;IACjC,KAAAkD,eAAe,GAAqB,IAAI;IACxC,KAAAgB,mBAAmB,GAAW,CAAC;IAC/B,KAAAnB,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAsC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,CAAC;IAExB;IACA,KAAA1C,eAAe,GAAG,CAChB;MAAE5E,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IACD,KAAAiD,gBAAgB,GAAW,CAAC;IAC5B,KAAA2B,kBAAkB,GAAgB,EAAE,CAAC,CAAC;EAMlC;EAEJwC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MACzE,IAAI,CAACL,UAAU,EAAE;IACnB;EACF;EAEA;EACAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgD,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;MACrB,IAAI,CAAC3F,eAAe,GAAG,EAAE;MACzB;IACF;IAEA,IAAI,CAAC2E,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9BS,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,IAAI,CAAChB,WAAW;QAC3BiB,QAAQ,EAAE,IAAI,CAAChB,QAAQ;QACvBiB,KAAK,EAAE,IAAI,CAAC9E,UAAU,IAAIqE;;KAE7B,CAAC,CAACU,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YACvExH,EAAE,EAAEwH,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBtI,IAAI,EAAEqI,OAAO,CAACE,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEN,OAAO,CAACK,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEV,OAAO,CAACW;WACf,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACnC,YAAY,GAAGmB,GAAG,CAACiB,UAAU,IAAI,CAAC;UAEvC;UACA,MAAMC,WAAW,GAAG,IAAI,CAAC9C,cAAc,CAACgC,GAAG,CAACe,GAAG,IAAIA,GAAG,CAACtI,EAAE,CAAC;UAC1D,IAAI,CAACS,eAAe,GAAG4G,SAAS,CAACkB,MAAM,CAACC,KAAK,IAAI,CAACH,WAAW,CAACI,QAAQ,CAACD,KAAK,CAACxI,EAAE,CAAC,CAAC;UAEjF;UACA,IAAI,CAAC4E,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnE,eAAe,EAAE,GAAG,IAAI,CAAC8E,cAAc,CAAC;UAE/D;UACA,IAAI,CAACmD,sBAAsB,EAAE;QAC/B,CAAC,MAAM;UACL,IAAI,CAACrD,cAAc,CAACsD,YAAY,CAACxB,GAAG,CAACyB,OAAO,IAAI,QAAQ,CAAC;QAC3D;QACA,IAAI,CAACpF,SAAS,GAAG,KAAK;MACxB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxD,cAAc,CAACsD,YAAY,CAAC,UAAU,IAAIE,KAAK,CAACC,OAAO,IAAI,MAAM,CAAC,CAAC;QACxE,IAAI,CAACtF,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACAiD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACL,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC;IACF;IAEA,IAAI,CAACjB,cAAc,CAACsB,iCAAiC,CAAC;MACpDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACR,WAAW;QAC9B2C,WAAW,EAAE,IAAI,CAAC1C,UAAU;QAC5BQ,YAAY,EAAE,IAAI,CAACP,WAAW;QAC9BQ,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACE,SAAS,CAAC;MACXC,IAAI,EAAGC,GAA2C,IAAI;QACpD,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7B,cAAc,GAAG4B,GAAG,CAACG,OAAO,EAAEC,GAAG,CAAEC,OAA+B,KAAM;YAC3ExH,EAAE,EAAEwH,OAAO,CAACC,GAAG,IAAI,CAAC;YACpBtI,IAAI,EAAEqI,OAAO,CAACE,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,KAAK,IAAI,EAAE;YACjCC,OAAO,EAAEN,OAAO,CAACK,KAAK,IAAI,EAAE;YAC5BE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAEV,OAAO,CAACW;WACf,CAAC,CAAC,IAAI,EAAE;QACX;MACF,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfG,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACAH,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC9D,MAAM,CAAClE,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACiE,mBAAmB,GAAGsE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC3D,iBAAiB,EAAE,IAAI,CAACZ,MAAM,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACiD,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAChB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgB,mBAAmB,GAAG,CAAC;IAC9B;EACF;EAEAT,eAAeA,CAAA;IACb,IAAI,IAAI,CAACS,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACe,aAAa,CAAC0D,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;IACnD;EACF;EAEAP,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAAClE,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACiE,mBAAmB,EAAE;MAC1B,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACgB,SAAS,CAACyD,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;IAC/C;EACF;EAEAb,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACH,eAAe,EAAE;MACxB,IAAI,CAAC8B,oBAAoB,CAAC2D,IAAI,CAAC,IAAI,CAACzF,eAAe,CAAC;IACtD;EACF;EAEAI,eAAeA,CAACyE,KAAgB;IAC9B,OAAO,IAAI,CAACjD,cAAc,CAAC8D,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACtJ,EAAE,KAAKwI,KAAK,CAACxI,EAAE,CAAC;EACvE;EAEA0E,OAAOA,CAAA;IACL,IAAI,CAACzB,KAAK,CAACmG,IAAI,EAAE;EACnB;EAEA;EACAG,WAAWA,CAACC,eAAkC;IAC5C;IACA,IAAI,IAAI,CAAC5E,MAAM,CAAClE,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACwF,UAAU,EAAE;IACnB;IAEA;IACA,MAAMuD,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;IACrD,IAAI,CAACvE,aAAa,CAACwE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAG,gBAAgBA,CAAC1B,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAC7C,cAAc,CAACsD,YAAY,CAAC,cAAc,CAAC;MAChD;IACF;IAEA,IAAI,CAACvD,cAAc,CAACyE,2BAA2B,CAAC;MAAE3B,IAAI,EAAEA;IAAI,CAAE,CAAC,CAC5DjB,SAAS,CAAC;MACTC,IAAI,EAAG4C,QAAQ,IAAI;QACjB;QACAd,OAAO,CAACe,GAAG,CAAC,SAAS,EAAED,QAAQ,CAAC;MAClC,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxD,cAAc,CAACsD,YAAY,CAAC,WAAWE,KAAK,CAACC,OAAO,IAAI,MAAM,EAAE,CAAC;MACxE;KACD,CAAC;EACN;EAEA;EACA9J,WAAWA,CAACgL,SAAoB;IAC9B,IAAIA,SAAS,CAAC9B,IAAI,IAAI,CAAC8B,SAAS,CAAClC,OAAO,EAAE;MACxC;MACA,IAAI,CAAC8B,gBAAgB,CAACI,SAAS,CAAC9B,IAAI,CAAC;IACvC;IAEA,OAAO8B,SAAS,CAAClC,OAAO,IAAIkC,SAAS,CAACpC,YAAY,IAAI,EAAE;EAC1D;EAEA;EACA7F,iBAAiBA,CAACkI,QAAgB;IAChC,IAAI,CAACnI,gBAAgB,GAAGmI,QAAQ;IAChC,IAAI,CAACpE,cAAc,CAACuD,IAAI,CAACa,QAAQ,CAAC;IAClC,IAAI,IAAI,CAAC7D,WAAW,EAAE;MACpB,IAAI,CAACF,UAAU,EAAE;IACnB;EACF;EAEA;EACAgE,oBAAoBA,CAACC,eAAkC;IACrD,IAAI,CAACnF,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACvB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC8B,cAAc,CAAC;IAElD,IAAI,IAAI,CAACX,MAAM,CAAClE,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACwF,UAAU,EAAE;IACnB;IAEA,MAAMuD,QAAQ,GAAGU,eAAe,IAAI,IAAI,CAACT,YAAY;IACrD,IAAI,CAACvE,aAAa,CAACwE,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEA;EACAtG,gBAAgBA,CAAA;IACd,IAAI,CAACyC,mBAAmB,CAACwD,IAAI,CAAC,IAAI,CAAC3F,kBAAkB,CAAC;IACtD,IAAI,CAACuB,oBAAoB,GAAG,KAAK;EACnC;EAEA;EACAhC,eAAeA,CAAA;IACb,IAAI,CAACS,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACuB,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACN,OAAO,EAAE;EAChB;EAEA;EACA0F,oBAAoBA,CAAC5B,KAAgB;IACnC,MAAM6B,KAAK,GAAG,IAAI,CAAC5G,kBAAkB,CAAC6G,SAAS,CAAChC,GAAG,IAAIA,GAAG,CAACtI,EAAE,KAAKwI,KAAK,CAACxI,EAAE,CAAC;IAC3E,IAAIqK,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC5G,kBAAkB,CAAC8G,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC5G,kBAAkB,CAAC+G,IAAI,CAAChC,KAAK,CAAC;IACrC;EACF;EAEA;EACAiC,mBAAmBA,CAACjC,KAAgB;IAClC,OAAO,IAAI,CAAC/E,kBAAkB,CAAC4F,IAAI,CAACf,GAAG,IAAIA,GAAG,CAACtI,EAAE,KAAKwI,KAAK,CAACxI,EAAE,CAAC;EACjE;;;uCAnSWiF,qBAAqB,EAAA7G,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArB/F,qBAAqB;MAAAgG,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCzBlChN,EAAA,CAAAgB,UAAA,IAAAkM,4CAAA,gCAAAlN,EAAA,CAAAmN,sBAAA,CAA0D;;;qBDuB9CtN,YAAY,EAAAuN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvN,YAAY,EAAAwN,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAnB,EAAA,CAAAoB,eAAA,EAAApB,EAAA,CAAAqB,mBAAA,EAAArB,EAAA,CAAAsB,qBAAA,EAAAtB,EAAA,CAAAuB,qBAAA,EAAAvB,EAAA,CAAAwB,iBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAElO,eAAe,EAAAyM,EAAA,CAAA0B,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}