<!-- 圖片預覽/綁定對話框 -->
<ng-template #imagePreview let-dialog let-ref="dialogRef">
  <!-- 綁定模式 - Picklist 風格 -->
  <nb-card *ngIf="showBindingInterface" class="w-[95vw] max-w-[1200px] h-[85vh] d-flex flex-column">
    <nb-card-header class="flex-shrink-0">
      圖片綁定 - {{ materialName || '選擇建材圖片' }}
    </nb-card-header>
    <nb-card-body class="d-flex flex-column flex-1" style="overflow: hidden; min-height: 0;">

      <!-- 控制區 -->
      <div class="d-flex gap-3 mb-4 flex-shrink-0">
        <div class="flex-1">
          <label class="form-label">圖片類別</label>
          <nb-select [(selected)]="selectedCategory" placeholder="選擇圖片類別" (selectedChange)="onCategoryChanged($event)">
            <nb-option *ngFor="let option of categoryOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
        <div class="flex-1">
          <label class="form-label">搜尋圖片</label>
          <input type="text" class="form-control" placeholder="搜尋圖片名稱..." [(ngModel)]="searchTerm"
            (ngModelChange)="onSearchTermChange()" />
        </div>
      </div>

      <!-- Picklist 容器 -->
      <div class="flex-1 d-flex gap-3" style="min-height: 0; overflow: hidden;">

        <!-- 可選擇區塊 -->
        <div class="flex-1 d-flex flex-column border rounded p-3" style="min-height: 0;">
          <div class="d-flex justify-content-between align-items-center mb-3 flex-shrink-0">
            <h6 class="mb-0">可選擇圖片</h6>
            <span class="badge badge-secondary">{{ totalAvailableRecords }} 筆</span>
          </div>

          <!-- 載入中狀態 -->
          <div *ngIf="isLoading" class="flex-1 d-flex align-items-center justify-content-center">
            <div class="text-center">
              <nb-spinner size="large"></nb-spinner>
              <div class="mt-2">載入圖片中...</div>
            </div>
          </div>

          <!-- 可選擇圖片列表 -->
          <div *ngIf="!isLoading" class="flex-1 d-flex flex-column" style="min-height: 0;">
            <!-- 圖片列表容器 -->
            <div class="flex-1" style="overflow-y: auto; min-height: 0;">
              <div *ngFor="let image of paginatedAvailableImages"
                class="d-flex align-items-center p-2 mb-2 border rounded hover-highlight" style="cursor: pointer;">
                <!-- 圖片縮圖 -->
                <div class="me-3" style="width: 60px; height: 60px; flex-shrink: 0;">
                  <img *ngIf="getImageUrl(image)" [src]="getImageUrl(image)" [alt]="image.name"
                    class="img-fluid rounded" style="width: 100%; height: 100%; object-fit: cover;" />
                  <div *ngIf="!getImageUrl(image)"
                    class="w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded">
                    <i class="fas fa-image text-muted"></i>
                  </div>
                </div>

                <!-- 圖片資訊 -->
                <div class="flex-1 me-3">
                  <div class="fw-bold text-truncate" [title]="image.name">{{ image.name }}</div>
                  <div class="small text-muted">ID: {{ image.id }}</div>
                </div>

                <!-- 選擇按鈕 -->
                <button class="btn btn-sm btn-outline-primary" (click)="moveToSelected(image)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>

            <!-- 空狀態 -->
            <div *ngIf="availableImages.length === 0"
              class="flex-1 d-flex align-items-center justify-content-center text-muted">
              <div class="text-center">
                <i class="fas fa-images fa-2x mb-2"></i>
                <div>找不到可選擇的圖片</div>
              </div>
            </div>

            <!-- 分頁控制 -->
            <div class="mt-3 flex-shrink-0">
              <ngx-pagination [Page]="availableCurrentPage" [PageSize]="picklistPageSize"
                [CollectionSize]="totalAvailableRecords" (PageChange)="goToAvailablePage($event)">
              </ngx-pagination>
            </div>
          </div>
        </div>

        <!-- 中間控制按鈕 -->
        <div class="d-flex flex-column justify-content-center gap-2" style="width: 80px;">
          <button class="btn btn-primary btn-sm" [disabled]="availableImages.length === 0" (click)="moveAllToSelected()"
            title="全部選擇">
            <i class="fas fa-angle-double-right"></i>
          </button>
          <button class="btn btn-secondary btn-sm" [disabled]="tempSelectedImages.length === 0"
            (click)="moveAllToAvailable()" title="全部移除">
            <i class="fas fa-angle-double-left"></i>
          </button>
        </div>

        <!-- 已選擇區塊 -->
        <div class="flex-1 d-flex flex-column border rounded p-3" style="min-height: 0;">
          <div class="d-flex justify-content-between align-items-center mb-3 flex-shrink-0">
            <h6 class="mb-0">已選擇圖片</h6>
            <span class="badge badge-primary">{{ tempSelectedImages.length }} 筆</span>
          </div>

          <!-- 已選擇圖片列表 -->
          <div class="flex-1" style="overflow-y: auto; min-height: 0;">
            <div *ngFor="let image of paginatedSelectedImages"
              class="d-flex align-items-center p-2 mb-2 border rounded hover-highlight" style="cursor: pointer;">
              <!-- 移除按鈕 -->
              <button class="btn btn-sm btn-outline-danger me-3" (click)="moveToAvailable(image)">
                <i class="fas fa-chevron-left"></i>
              </button>

              <!-- 圖片縮圖 -->
              <div class="me-3" style="width: 60px; height: 60px; flex-shrink: 0;">
                <img *ngIf="getImageUrl(image)" [src]="getImageUrl(image)" [alt]="image.name" class="img-fluid rounded"
                  style="width: 100%; height: 100%; object-fit: cover;" />
                <div *ngIf="!getImageUrl(image)"
                  class="w-100 h-100 d-flex align-items-center justify-content-center bg-light rounded">
                  <i class="fas fa-image text-muted"></i>
                </div>
              </div>

              <!-- 圖片資訊 -->
              <div class="flex-1">
                <div class="fw-bold text-truncate" [title]="image.name">{{ image.name }}</div>
                <div class="small text-muted">ID: {{ image.id }}</div>
              </div>
            </div>
          </div>

          <!-- 空狀態 -->
          <div *ngIf="tempSelectedImages.length === 0"
            class="flex-1 d-flex align-items-center justify-content-center text-muted">
            <div class="text-center">
              <i class="fas fa-inbox fa-2x mb-2"></i>
              <div>尚未選擇任何圖片</div>
            </div>
          </div>

          <!-- 分頁控制 -->
          <div *ngIf="tempSelectedImages.length > 0" class="mt-3 flex-shrink-0">
            <ngx-pagination [Page]="selectedCurrentPage" [PageSize]="picklistPageSize"
              [CollectionSize]="tempSelectedImages.length" (PageChange)="goToSelectedPage($event)">
            </ngx-pagination>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-between flex-shrink-0">
      <button class="btn btn-secondary" (click)="onCancelBinding(); ref.close()">取消</button>
      <button class="btn btn-primary" (click)="onConfirmBinding(); ref.close()"
        [disabled]="tempSelectedImages.length === 0">
        確定選擇 ({{ tempSelectedImages.length }})
      </button>
    </nb-card-footer>
  </nb-card>

  <!-- 預覽模式 -->
  <nb-card *ngIf="!showBindingInterface" class="w-[800px] h-[600px]">
    <nb-card-header class="d-flex justify-content-between align-items-center">
      <span>圖片預覽 - {{ previewingImage?.name }}</span>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" [disabled]="currentPreviewIndex <= 0"
          (click)="onPreviousImage()">
          <i class="fas fa-chevron-left"></i> 上一張
        </button>
        <button class="btn btn-outline-primary btn-sm" [disabled]="currentPreviewIndex >= images.length - 1"
          (click)="onNextImage()">
          下一張 <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </nb-card-header>

    <nb-card-body class="p-0 d-flex justify-content-center align-items-center" style="height: 500px;">
      <!-- 載入中狀態 -->
      <div *ngIf="isLoading" class="text-center">
        <nb-spinner size="large"></nb-spinner>
        <div class="mt-2">載入圖片中...</div>
      </div>

      <!-- 圖片顯示 -->
      <img *ngIf="!isLoading && previewingImage && getImageUrl(previewingImage)" [src]="getImageUrl(previewingImage)"
        [alt]="previewingImage.name" class="max-w-full max-h-full object-contain" />

      <!-- 無圖片狀態 -->
      <div *ngIf="!isLoading && (!previewingImage || !getImageUrl(previewingImage))" class="text-gray-400 text-center">
        <i class="fas fa-image text-4xl mb-3"></i>
        <div>無可預覽的圖片</div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center gap-3">
        <div class="text-sm text-gray-600">
          {{ currentPreviewIndex + 1 }} / {{ images.length }}
        </div>
        <!-- 主要分頁控制 -->
        <div *ngIf="totalPages > 1" class="d-flex align-items-center">
          <span class="text-sm text-gray-600">|</span>
          <ngx-pagination [Page]="currentPage" [PageSize]="pageSize" [CollectionSize]="totalRecords"
            (PageChange)="goToPage($event)">
          </ngx-pagination>
        </div>
      </div>
      <div class="d-flex gap-2">
        <button *ngIf="showSelectionToggle" class="btn btn-outline-info btn-sm" (click)="onToggleImageSelection()">
          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}
        </button>
        <button class="btn btn-danger btn-sm" (click)="onClose(); ref.close()">關閉</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>