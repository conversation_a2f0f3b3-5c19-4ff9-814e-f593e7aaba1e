{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../../pages/components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"imagePreview\"];\nfunction ImagePreviewComponent_ng_template_0_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.previewingImage.name);\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onToggleImageSelection());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.previewingImage && ctx_r1.isImageSelected(ctx_r1.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction ImagePreviewComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\", 2)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPreviousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 5);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onNextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 7);\n    i0.ɵɵtemplate(12, ImagePreviewComponent_ng_template_0_img_12_Template, 1, 2, \"img\", 8)(13, ImagePreviewComponent_ng_template_0_div_13_Template, 4, 0, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 2)(15, \"div\", 10);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 3);\n    i0.ɵɵtemplate(18, ImagePreviewComponent_ng_template_0_button_18_Template, 2, 1, \"button\", 11);\n    i0.ɵɵelementStart(19, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ImagePreviewComponent_ng_template_0_Template_button_click_19_listener() {\n      const ref_r4 = i0.ɵɵrestoreView(_r1).dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClose();\n      return i0.ɵɵresetView(ref_r4.close());\n    });\n    i0.ɵɵtext(20, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r1.previewingImage == null ? null : ctx_r1.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPreviewIndex >= ctx_r1.images.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewingImage && ctx_r1.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previewingImage || !ctx_r1.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentPreviewIndex + 1, \" / \", ctx_r1.images.length, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSelectionToggle);\n  }\n}\nexport class ImagePreviewComponent {\n  constructor(dialogService) {\n    this.dialogService = dialogService;\n    this.isVisible = false;\n    this.images = [];\n    this.selectedImages = [];\n    this.initialImageIndex = 0;\n    this.showSelectionToggle = true;\n    this.imageSelectionToggle = new EventEmitter();\n    this.close = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n  }\n  ngOnInit() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    }\n  }\n  ngOnChanges() {\n    if (this.images.length > 0) {\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\n      this.previewingImage = this.images[this.currentPreviewIndex];\n    }\n  }\n  onPreviousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.previousImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onNextImage() {\n    if (this.currentPreviewIndex < this.images.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.images[this.currentPreviewIndex];\n      this.nextImage.emit(this.currentPreviewIndex);\n    }\n  }\n  onToggleImageSelection() {\n    if (this.previewingImage) {\n      this.imageSelectionToggle.emit(this.previewingImage);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onClose() {\n    this.close.emit();\n  }\n  // 開啟預覽對話框的方法\n  openPreview(imagePreviewRef) {\n    if (this.images.length > 0) {\n      const template = imagePreviewRef || this.imagePreview;\n      this.dialogService.open(template);\n    }\n  }\n  static {\n    this.ɵfac = function ImagePreviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePreviewComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImagePreviewComponent,\n      selectors: [[\"app-image-preview\"]],\n      viewQuery: function ImagePreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreview = _t.first);\n        }\n      },\n      inputs: {\n        isVisible: \"isVisible\",\n        images: \"images\",\n        selectedImages: \"selectedImages\",\n        initialImageIndex: \"initialImageIndex\",\n        showSelectionToggle: \"showSelectionToggle\"\n      },\n      outputs: {\n        imageSelectionToggle: \"imageSelectionToggle\",\n        close: \"close\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"imagePreview\", \"\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"btn btn-outline-info btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"]],\n      template: function ImagePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImagePreviewComponent_ng_template_0_Template, 21, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, SharedModule, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.image-preview-container[_ngcontent-%COMP%]   .max-w-full[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .max-h-full[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .object-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-400[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-gray-600[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-4xl[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.image-preview-container[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n\\n\\nnb-card[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\nnb-card[_ngcontent-%COMP%]   .justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\nnb-card[_ngcontent-%COMP%]   .gap-2[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\nnb-card[_ngcontent-%COMP%]   .p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "SharedModule", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "previewingImage", "fullUrl", "ɵɵsanitizeUrl", "name", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ImagePreviewComponent_ng_template_0_button_18_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onToggleImageSelection", "ɵɵadvance", "ɵɵtextInterpolate1", "isImageSelected", "ImagePreviewComponent_ng_template_0_Template_button_click_5_listener", "_r1", "onPreviousImage", "ImagePreviewComponent_ng_template_0_Template_button_click_8_listener", "onNextImage", "ɵɵtemplate", "ImagePreviewComponent_ng_template_0_img_12_Template", "ImagePreviewComponent_ng_template_0_div_13_Template", "ImagePreviewComponent_ng_template_0_button_18_Template", "ImagePreviewComponent_ng_template_0_Template_button_click_19_listener", "ref_r4", "dialogRef", "onClose", "close", "currentPreviewIndex", "images", "length", "ɵɵtextInterpolate2", "showSelectionToggle", "ImagePreviewComponent", "constructor", "dialogService", "isVisible", "selectedImages", "initialImageIndex", "imageSelectionToggle", "previousImage", "nextImage", "ngOnInit", "Math", "max", "min", "ngOnChanges", "emit", "image", "some", "selected", "id", "openPreview", "imagePreviewRef", "template", "imagePreview", "open", "ɵɵdirectiveInject", "i1", "NbDialogService", "selectors", "viewQuery", "ImagePreviewComponent_Query", "rf", "ctx", "ImagePreviewComponent_ng_template_0_Template", "ɵɵtemplateRefExtractor", "i2", "NgIf", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\image-preview\\image-preview.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { SharedModule } from '../../../pages/components/shared.module';\r\n\r\n// 圖片項目介面\r\nexport interface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  guid?: string | null; // 新增 CGuid 欄位支援\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-preview',\r\n  templateUrl: './image-preview.component.html',\r\n  styleUrls: ['./image-preview.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule]\r\n})\r\nexport class ImagePreviewComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() images: ImageItem[] = [];\r\n  @Input() selectedImages: ImageItem[] = [];\r\n  @Input() initialImageIndex: number = 0;\r\n  @Input() showSelectionToggle: boolean = true;\r\n  \r\n  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;\r\n\r\n  previewingImage: ImageItem | null = null;\r\n  currentPreviewIndex: number = 0;\r\n\r\n  constructor(private dialogService: NbDialogService) {}\r\n\r\n  ngOnInit(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    if (this.images.length > 0) {\r\n      this.currentPreviewIndex = Math.max(0, Math.min(this.initialImageIndex, this.images.length - 1));\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  onPreviousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.previousImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onNextImage() {\r\n    if (this.currentPreviewIndex < this.images.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.images[this.currentPreviewIndex];\r\n      this.nextImage.emit(this.currentPreviewIndex);\r\n    }\r\n  }\r\n\r\n  onToggleImageSelection() {\r\n    if (this.previewingImage) {\r\n      this.imageSelectionToggle.emit(this.previewingImage);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 開啟預覽對話框的方法\r\n  openPreview(imagePreviewRef?: TemplateRef<any>) {\r\n    if (this.images.length > 0) {\r\n      const template = imagePreviewRef || this.imagePreview;\r\n      this.dialogService.open(template);\r\n    }\r\n  }\r\n}", "<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"onPreviousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= images.length - 1\"\r\n          (click)=\"onNextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage && previewingImage.fullUrl\" [src]=\"previewingImage.fullUrl\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage || !previewingImage.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ images.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button *ngIf=\"showSelectionToggle\" class=\"btn btn-outline-info btn-sm\" (click)=\"onToggleImageSelection()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onClose(); ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,QAAwC,eAAe;AACtG,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;ICchEC,EAAA,CAAAC,SAAA,cAC8E;;;;IAA5ED,EADsD,CAAAE,UAAA,QAAAC,MAAA,CAAAC,eAAA,CAAAC,OAAA,EAAAL,EAAA,CAAAM,aAAA,CAA+B,QAAAH,MAAA,CAAAC,eAAA,CAAAG,IAAA,CACzD;;;;;IAC9BP,EAAA,CAAAQ,cAAA,cAA4F;IAC1FR,EAAA,CAAAC,SAAA,YAA0C;IAC1CD,EAAA,CAAAQ,cAAA,UAAK;IAAAR,EAAA,CAAAS,MAAA,2CAAM;IACbT,EADa,CAAAU,YAAA,EAAM,EACb;;;;;;IAQJV,EAAA,CAAAQ,cAAA,iBAA2G;IAAnCR,EAAA,CAAAW,UAAA,mBAAAC,+EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,sBAAA,EAAwB;IAAA,EAAC;IACxGjB,EAAA,CAAAS,MAAA,GACF;IAAAT,EAAA,CAAAU,YAAA,EAAS;;;;IADPV,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAhB,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAiB,eAAA,CAAAjB,MAAA,CAAAC,eAAA,uEACF;;;;;;IA5BFJ,EAFJ,CAAAQ,cAAA,iBAAqC,wBACuC,WAClE;IAAAR,EAAA,CAAAS,MAAA,GAAkC;IAAAT,EAAA,CAAAU,YAAA,EAAO;IAE7CV,EADF,CAAAQ,cAAA,aAA0B,gBACyF;IAA5BR,EAAA,CAAAW,UAAA,mBAAAU,qEAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAoB,eAAA,EAAiB;IAAA,EAAC;IAC9GvB,EAAA,CAAAC,SAAA,WAAmC;IAACD,EAAA,CAAAS,MAAA,2BACtC;IAAAT,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAQ,cAAA,gBAC0B;IAAxBR,EAAA,CAAAW,UAAA,mBAAAa,qEAAA;MAAAxB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAsB,WAAA,EAAa;IAAA,EAAC;IACvBzB,EAAA,CAAAS,MAAA,2BAAI;IAAAT,EAAA,CAAAC,SAAA,YAAoC;IAG9CD,EAFI,CAAAU,YAAA,EAAS,EACL,EACS;IAEjBV,EAAA,CAAAQ,cAAA,uBAAkG;IAGhGR,EAFA,CAAA0B,UAAA,KAAAC,mDAAA,iBAC8E,KAAAC,mDAAA,iBACc;IAI9F5B,EAAA,CAAAU,YAAA,EAAe;IAGbV,EADF,CAAAQ,cAAA,yBAA0E,eACrC;IACjCR,EAAA,CAAAS,MAAA,IACF;IAAAT,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAQ,cAAA,cAA0B;IACxBR,EAAA,CAAA0B,UAAA,KAAAG,sDAAA,qBAA2G;IAG3G7B,EAAA,CAAAQ,cAAA,kBAAuE;IAAjCR,EAAA,CAAAW,UAAA,mBAAAmB,sEAAA;MAAA,MAAAC,MAAA,GAAA/B,EAAA,CAAAa,aAAA,CAAAS,GAAA,EAAAU,SAAA;MAAA,MAAA7B,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAASZ,MAAA,CAAA8B,OAAA,EAAS;MAAA,OAAAjC,EAAA,CAAAgB,WAAA,CAAEe,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAAClC,EAAA,CAAAS,MAAA,oBAAE;IAG/ET,EAH+E,CAAAU,YAAA,EAAS,EAC9E,EACS,EACT;;;;IAhCAV,EAAA,CAAAkB,SAAA,GAAkC;IAAlClB,EAAA,CAAAmB,kBAAA,gCAAAhB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAG,IAAA,KAAkC;IAESP,EAAA,CAAAkB,SAAA,GAAqC;IAArClB,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAgC,mBAAA,MAAqC;IAGrCnC,EAAA,CAAAkB,SAAA,GAAqD;IAArDlB,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAgC,mBAAA,IAAAhC,MAAA,CAAAiC,MAAA,CAAAC,MAAA,KAAqD;IAQhGrC,EAAA,CAAAkB,SAAA,GAAgD;IAAhDlB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAC,eAAA,CAAAC,OAAA,CAAgD;IAEhDL,EAAA,CAAAkB,SAAA,EAAkD;IAAlDlB,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,eAAA,KAAAD,MAAA,CAAAC,eAAA,CAAAC,OAAA,CAAkD;IAQtDL,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAnC,MAAA,CAAAgC,mBAAA,aAAAhC,MAAA,CAAAiC,MAAA,CAAAC,MAAA,MACF;IAEWrC,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoC,mBAAA,CAAyB;;;ADP1C,OAAM,MAAOC,qBAAqB;EAiBhCC,YAAoBC,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;IAhBxB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAP,MAAM,GAAgB,EAAE;IACxB,KAAAQ,cAAc,GAAgB,EAAE;IAChC,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAN,mBAAmB,GAAY,IAAI;IAElC,KAAAO,oBAAoB,GAAG,IAAIjD,YAAY,EAAa;IACpD,KAAAqC,KAAK,GAAG,IAAIrC,YAAY,EAAQ;IAChC,KAAAkD,aAAa,GAAG,IAAIlD,YAAY,EAAU;IAC1C,KAAAmD,SAAS,GAAG,IAAInD,YAAY,EAAU;IAIhD,KAAAO,eAAe,GAAqB,IAAI;IACxC,KAAA+B,mBAAmB,GAAW,CAAC;EAEsB;EAErDc,QAAQA,CAAA;IACN,IAAI,IAAI,CAACb,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACF,mBAAmB,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACP,iBAAiB,EAAE,IAAI,CAACT,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACjC,eAAe,GAAG,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D;EACF;EAEAkB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACjB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACF,mBAAmB,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACP,iBAAiB,EAAE,IAAI,CAACT,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;MAChG,IAAI,CAACjC,eAAe,GAAG,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;IAC9D;EACF;EAEAZ,eAAeA,CAAA;IACb,IAAI,IAAI,CAACY,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAAC/B,eAAe,GAAG,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACY,aAAa,CAACO,IAAI,CAAC,IAAI,CAACnB,mBAAmB,CAAC;IACnD;EACF;EAEAV,WAAWA,CAAA;IACT,IAAI,IAAI,CAACU,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACF,mBAAmB,EAAE;MAC1B,IAAI,CAAC/B,eAAe,GAAG,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACD,mBAAmB,CAAC;MAC5D,IAAI,CAACa,SAAS,CAACM,IAAI,CAAC,IAAI,CAACnB,mBAAmB,CAAC;IAC/C;EACF;EAEAlB,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACb,eAAe,EAAE;MACxB,IAAI,CAAC0C,oBAAoB,CAACQ,IAAI,CAAC,IAAI,CAAClD,eAAe,CAAC;IACtD;EACF;EAEAgB,eAAeA,CAACmC,KAAgB;IAC9B,OAAO,IAAI,CAACX,cAAc,CAACY,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,EAAE,KAAKH,KAAK,CAACG,EAAE,CAAC;EACvE;EAEAzB,OAAOA,CAAA;IACL,IAAI,CAACC,KAAK,CAACoB,IAAI,EAAE;EACnB;EAEA;EACAK,WAAWA,CAACC,eAAkC;IAC5C,IAAI,IAAI,CAACxB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMwB,QAAQ,GAAGD,eAAe,IAAI,IAAI,CAACE,YAAY;MACrD,IAAI,CAACpB,aAAa,CAACqB,IAAI,CAACF,QAAQ,CAAC;IACnC;EACF;;;uCArEWrB,qBAAqB,EAAAxC,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAArB1B,qBAAqB;MAAA2B,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCtBlCtE,EAAA,CAAA0B,UAAA,IAAA8C,4CAAA,iCAAAxE,EAAA,CAAAyE,sBAAA,CAA0D;;;qBDoB9C3E,YAAY,EAAA4E,EAAA,CAAAC,IAAA,EAAE5E,YAAY,EAAAkE,EAAA,CAAAW,eAAA,EAAAX,EAAA,CAAAY,mBAAA,EAAAZ,EAAA,CAAAa,qBAAA,EAAAb,EAAA,CAAAc,qBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}