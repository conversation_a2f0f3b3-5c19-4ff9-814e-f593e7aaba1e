import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, PictureService } from 'src/services/api/services';
import { BuildCaseGetListReponse, GetPictureListResponse } from 'src/services/api/models';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { concatMap, finalize, of, tap } from 'rxjs';
import { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';
import { MessageService } from 'src/app/shared/services/message.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';
import * as JSZip from 'jszip';

// 圖片類別枚舉
enum PictureCategory {
  NONE = 0,           // 未選擇
  BUILDING_MATERIAL = 1,  // 建材圖片
  SCHEMATIC = 2       // 示意圖片
}

@Component({
  selector: 'ngx-picture-material',
  templateUrl: './picture-material.component.html',
  styleUrls: ['./picture-material.component.scss'],
  standalone: true, imports: [
    CommonModule,
    SharedModule,
    Base64ImagePipe,
    BuildCaseSelectComponent
  ],
})

export class PictureMaterialComponent extends BaseComponent implements OnInit {
  images: (GetPictureListResponse)[] = [];
  listUserBuildCases: BuildCaseGetListReponse[] = []
  selectedBuildCaseId: number
  selectedCategory: PictureCategory = PictureCategory.NONE;
  isCategorySelected: boolean = false; // 追蹤用戶是否已經明確選擇類別

  // 批次選擇相關屬性
  selectedItems: Set<number> = new Set<number>(); // 選中的項目 ID
  selectAll: boolean = false; // 全選狀態
  get selectedCount(): number {
    return this.selectedItems.size;
  }
  currentImageShowing: string = ""

  // 輪播預覽相關屬性
  currentPreviewImages: GetPictureListResponse[] = [];
  currentImageIndex: number = 0;
  isPreviewMode: boolean = false;

  listPictures: any[] = []

  isEdit: boolean = false;
  currentEditItem: number;

  // 類別選項
  categoryOptions = [
    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },
    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }
  ]

  // 讓模板可以使用 enum
  PictureCategory = PictureCategory;

  // 獲取類別標籤的方法
  getCategoryLabel(category: number): string {
    const option = this.categoryOptions.find(opt => opt.value === category);
    return option ? option.label : '未知類別';
  }
  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private valid: ValidationHelper,
    private _pictureService: PictureService,
    private _buildCaseService: BuildCaseService,
    private message: MessageService,
    private _utilityService: UtilityService
  ) {
    super(_allow)
  }

  override ngOnInit(): void {
    this.getListBuildCase();
  }

  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})
      .pipe(
        tap(res => {
          if (res.StatusCode === 0) {
            this.listUserBuildCases = res.Entries! ?? []
            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇
            // this.selectedBuildCaseId = this.listUserBuildCases[0].cID!
          }
        })
        // 移除立即載入，等待建案選擇事件觸發
        // concatMap(() => this.getPicturelList(1))
      ).subscribe()
  }

  openPdfInNewTab(CFileUrl?: any) {
    if (CFileUrl) {
      this._utilityService.openFileInNewTab(CFileUrl)
    }
  } getPicturelList(pageIndex: number) {
    // 重置選擇狀態
    this.selectedItems.clear();
    this.selectAll = false;

    if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {
      return this._pictureService.apiPictureGetPictureListPost$Json({
        body: {
          PageIndex: pageIndex,
          PageSize: this.pageSize,
          CBuildCaseId: this.selectedBuildCaseId,
          cPictureType: this.selectedCategory
        }
      }).pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.images = res.Entries! ?? []
            this.totalRecords = res.TotalItems!;
            this.updateSelectAllState();
          }
        })
      )
    } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {
      return this._pictureService.apiPictureGetPictureListPost$Json({
        body: {
          PageIndex: pageIndex,
          PageSize: this.pageSize,
          CBuildCaseId: this.selectedBuildCaseId,
          cPictureType: this.selectedCategory
        }
      }).pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.images = res.Entries! ?? []
            this.totalRecords = res.TotalItems!;
            this.updateSelectAllState();
          }
        })
      )
    } else {
      // 如果沒有選擇類別，清空數據並返回預設的空 observable
      this.images = [];
      this.totalRecords = 0;
      return this._pictureService.apiPictureGetPictureListPost$Json({
        body: {
          PageIndex: 1,
          PageSize: 1,
          CBuildCaseId: -1, // 使用無效 ID 確保返回空結果
          cPictureType: PictureCategory.NONE
        }
      }).pipe(
        tap(() => {
          this.images = [];
          this.totalRecords = 0;
        })
      )
    }
  }

  pageChanged(pageIndex: number) {
    // this.pageIndex = newPage;
    this.getPicturelList(pageIndex).subscribe();
  }
  // 建案選擇事件處理（新）
  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {
    if (selectedBuildCase && selectedBuildCase.cID) {
      this.selectedBuildCaseId = selectedBuildCase.cID;
    } else if (this.listUserBuildCases.length > 0) {
      this.selectedBuildCaseId = this.listUserBuildCases[0].cID!;
    }
    this.getPicturelList(1).subscribe();
  }

  // 保留原有方法
  selectedChange(buildCaseId: number) {
    this.selectedBuildCaseId = buildCaseId;
    this.getPicturelList(1).subscribe();
  } categoryChanged(category: PictureCategory) {
    this.selectedCategory = category;
    this.isCategorySelected = true; // 標記用戶已經選擇了類別
    this.getPicturelList(1).subscribe();
  } addNew(ref: any, item?: GetPictureListResponse) {
    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別
    if (!item && !this.isCategorySelected) {
      this.message.showErrorMSG('請先選擇圖片類別');
      return;
    }

    this.listPictures = []
    this.dialogService.open(ref)
    this.isEdit = false;

    if (!!item) {
      // 預覽模式 - 顯示當前類別的所有圖片並支持輪播
      this.isPreviewMode = true;
      this.currentPreviewImages = [...this.images];
      this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);
      if (this.currentImageIndex === -1) {
        this.currentImageIndex = 0;
      }
      this.currentImageShowing = this.getCurrentPreviewImage();
    } else {
      // 上傳模式
      this.isPreviewMode = false;
      this.currentPreviewImages = [];
      this.currentImageIndex = 0;
      this.currentImageShowing = "";
      this.listPictures = [];
    }
  }
  changePicture(ref: any, item?: GetPictureListResponse) {
    // 檢查是否已選擇類別
    if (!this.selectedCategory) {
      this.message.showErrorMSG('請先選擇圖片類別');
      return;
    }

    if (!!item && item.CId) {
      this.dialogService.open(ref)
      this.isEdit = true;
      this.currentEditItem = item.CId;
      this.listPictures = [];
    }
  }

  validation(CFile: any) {
    this.valid.clear();
    const nameSet = new Set();
    for (const item of CFile) {
      if (nameSet.has(item.name)) {
        this.valid.addErrorMessage('檔名不可重複')
        return;
      }
      nameSet.add(item.name);
    }
  }

  onSubmit(ref: any) {
  }
  detectFiles(event: any) {
    for (let index = 0; index < event.target.files.length; index++) {
      const file = event.target.files[index];
      if (file) {
        // 檢查是否為 ZIP 檔案
        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {
          this.processZipFile(file);
        } else {
          this.processSingleImageFile(file);
        }
      }
    }
    // Reset input file to be able to select the old file again
    event.target.value = null;
  }

  processZipFile(zipFile: File) {
    const zip = new JSZip();

    zip.loadAsync(zipFile).then((contents) => {
      const imageFiles: Promise<any>[] = []; contents.forEach((relativePath, file) => {
        // 只處理圖片檔案，跳過資料夾
        if (!file.dir && this.isImageFile(relativePath)) {
          imageFiles.push(
            file.async('blob').then((blob) => {
              // 只取檔案名稱，移除資料夾路徑
              const fileName = relativePath.split('/').pop() || relativePath.split('\\').pop() || relativePath;
              // 建立 File 物件
              const imageFile = new File([blob], fileName, { type: this.getImageMimeType(relativePath) });
              return this.processImageFileFromZip(imageFile, fileName);
            })
          );
        }
      });

      // 處理所有圖片檔案
      Promise.all(imageFiles).then(() => {
        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);
      }).catch((error) => {
        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);
        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');
      });

    }).catch((error) => {
      console.error('讀取 ZIP 檔案時發生錯誤:', error);
      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');
    });
  }

  processSingleImageFile(file: File) {
    let reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      let base64Str: string = reader.result as string;
      if (!base64Str) {
        return;
      }
      this.addImageToList(file, base64Str);
    };
  }

  processImageFileFromZip(file: File, originalPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        let base64Str: string = reader.result as string;
        if (!base64Str) {
          reject('無法讀取圖片檔案');
          return;
        }
        this.addImageToList(file, base64Str, originalPath);
        resolve();
      };
      reader.onerror = () => {
        reject('讀取圖片檔案時發生錯誤');
      };
    });
  }
  addImageToList(file: File, base64Str: string, originalPath?: string) {
    // Get name file ( no extension)
    let fileName = originalPath || file.name;

    // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑
    if (originalPath) {
      fileName = originalPath.split('/').pop() || originalPath.split('\\').pop() || originalPath;
    }

    const fileNameWithoutExtension = fileName.split('.')[0];

    // Find files with duplicate names
    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);
    if (existingFileIndex !== -1) {
      // If name is duplicate, update file data
      this.listPictures[existingFileIndex] = {
        ...this.listPictures[existingFileIndex],
        data: base64Str,
        CFile: file,
        extension: this._utilityService.getFileExtension(fileName)
      };
    } else {
      // If not duplicate, add new file
      this.listPictures.push({
        id: new Date().getTime() + Math.random(),
        name: fileNameWithoutExtension,
        data: base64Str,
        extension: this._utilityService.getFileExtension(fileName),
        CFile: file
      });
    }
  }

  isImageFile(fileName: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return imageExtensions.includes(extension);
  }

  getImageMimeType(fileName: string): string {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.bmp':
        return 'image/bmp';
      case '.webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  removeImage(pictureId: number) {
    this.listPictures = this.listPictures.filter(x => x.id != pictureId)
  }
  uploadImage(ref: any) {
    if (!this.isEdit) {
      const CFile = this.listPictures.map(x => x.CFile)
      this.validation(CFile)
      if (this.valid.errorMessages.length > 0) {
        this.message.showErrorMSGs(this.valid.errorMessages);
        return
      }      // 統一使用 PictureService 進行上傳
      const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({
        body: {
          CBuildCaseId: this.selectedBuildCaseId,
          CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? "picture" : "infoPicture",
          CFile: CFile,
          CPictureType: this.selectedCategory
        }
      }); uploadRequest.pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.message.showSucessMSG('執行成功')
            this.listPictures = []
          } else {
            this.message.showErrorMSG(res.Message!)
          }
          ref.close();
          this.resetPreviewState();
        }),
        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),
      ).subscribe()
    }
    else {
      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {
        // 統一使用 PictureService 進行更新
        const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({
          body: {
            CBuildCaseID: this.selectedBuildCaseId,
            CPictureID: this.currentEditItem,
            CFile: this.listPictures[0].CFile
          }
        }); updateRequest.pipe(
          tap(res => {
            if (res.StatusCode == 0) {
              this.message.showSucessMSG('執行成功')
              this.listPictures = []
            } else {
              this.message.showErrorMSG(res.Message!)
            }
            ref.close();
            this.resetPreviewState();
          }),
          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),
        ).subscribe()
      }
    }
  }

  renameFile(event: any, index: number) {
    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);
    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });

    this.listPictures[index].CFile = newFile
  }
  // 批次選擇相關方法
  toggleSelectItem(itemId: number) {
    if (this.selectedItems.has(itemId)) {
      this.selectedItems.delete(itemId);
    } else {
      this.selectedItems.add(itemId);
    }
    this.updateSelectAllState();
  }

  toggleSelectAll(checked: boolean) {
    this.selectAll = checked;
    if (checked) {
      // 全選當前頁面的所有項目
      this.images.forEach(item => {
        if (item.CId) {
          this.selectedItems.add(item.CId);
        }
      });
    } else {
      // 取消全選 - 只移除當前頁面的項目
      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];
      currentPageIds.forEach(id => this.selectedItems.delete(id));
    }
  }

  updateSelectAllState() {
    const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];
    this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));
  }
  // 批次刪除方法
  batchDelete() {
    if (this.selectedItems.size === 0) {
      this.message.showErrorMSG('請選擇要刪除的項目');
      return;
    }

    const selectedIds = Array.from(this.selectedItems);

    // 顯示確認對話框
    if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {
      this._pictureService.apiPictureDeletePicturePost$Json({
        body: selectedIds
      }).pipe(
        tap(res => {
          if (res.StatusCode === 0) {
            this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);
            // 清空選擇狀態
            this.selectedItems.clear();
            this.selectAll = false;
          } else {
            this.message.showErrorMSG(res.Message || '刪除失敗');
          }
        }),
        concatMap((res) => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))
      ).subscribe();
    }
  }
  // 檢查項目是否被選中
  isItemSelected(itemId: number): boolean {
    return this.selectedItems.has(itemId);
  }
  // 輪播預覽相關方法
  getCurrentPreviewImage(): string {
    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {
      return '';
    }
    const currentImage = this.currentPreviewImages[this.currentImageIndex];
    // 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料
    // TODO: 可以實作透過 CGuid 取得 base64 資料的功能
    return currentImage.CFile || '';
  }

  previousImage(): void {
    if (this.currentPreviewImages.length === 0) return;
    this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;
    this.currentImageShowing = this.getCurrentPreviewImage();
  }
  nextImage(): void {
    if (this.currentPreviewImages.length === 0) return;
    this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;
    this.currentImageShowing = this.getCurrentPreviewImage();
  }

  goToImage(index: number): void {
    if (index >= 0 && index < this.currentPreviewImages.length) {
      this.currentImageIndex = index;
      this.currentImageShowing = this.getCurrentPreviewImage();
    }
  }

  get currentImageInfo() {
    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {
      return null;
    }
    return this.currentPreviewImages[this.currentImageIndex];
  }
  get imageCounter(): string {
    if (this.currentPreviewImages.length === 0) return '';
    return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;
  }
  // 重置預覽狀態
  resetPreviewState(): void {
    this.isPreviewMode = false;
    this.currentPreviewImages = [];
    this.currentImageIndex = 0;
    this.currentImageShowing = "";
  }

  // 鍵盤導航支持
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        this.previousImage();
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.nextImage();
        break;
      case 'Escape':
        event.preventDefault();
        // 可以在這裡添加關閉對話框的邏輯
        break;
    }
  }
}
