{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\nimport { ImagePreviewComponent } from '../../../shared/components/image-preview';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(60);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext();\n      const inputFile_r8 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(inputFile_r8.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.CSelectPictureId.length);\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_56_tr_1_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const dialog_r6 = i0.ɵɵreference(60);\n      return i0.ɵɵresetView(ctx_r2.onSelectedMaterial(item_r9, dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_56_tr_1_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const imageBinder_r12 = i0.ɵɵreference(62);\n      return i0.ɵɵresetView(ctx_r2.bindImageForMaterial(item_r9, imageBinder_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵtext(2, \" \\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r9.CSelectName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"div\", 41);\n    i0.ɵɵtemplate(11, BuildingMaterialComponent_tbody_56_tr_1_span_11_Template, 2, 1, \"span\", 42)(12, BuildingMaterialComponent_tbody_56_tr_1_span_12_Template, 2, 0, \"span\", 43);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\")(18, \"span\", 44);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"td\", 45);\n    i0.ɵɵtemplate(21, BuildingMaterialComponent_tbody_56_tr_1_button_21_Template, 2, 0, \"button\", 46)(22, BuildingMaterialComponent_tbody_56_tr_1_button_22_Template, 3, 1, \"button\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CMaterialCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r9.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r9.CSelectPictureId && item_r9.CSelectPictureId.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.CSelectPictureId || item_r9.CSelectPictureId.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CSelectPictureId && item_r9.CSelectPictureId.length > 0 ? \"\\u5DF2\\u7D81\\u5B9A\" : \"\\u672A\\u7D81\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r9.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getStatusLabel(item_r9.CStatus || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_56_tr_1_Template, 23, 15, \"tr\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r14.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r14.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u8A2D\\u5B9A\\u5EFA\\u6750\\u4EE3\\u865F: \", ctx_r2.selectedMaterial.CMaterialCode, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 53)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 54)(4, \"h5\", 55);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 56)(7, \"div\", 57)(8, \"label\", 58);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CMaterialCode, $event) || (ctx_r2.selectedMaterial.CMaterialCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 60)(12, \"label\", 58);\n    i0.ɵɵtext(13, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CSelectName, $event) || (ctx_r2.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 60)(16, \"label\", 62);\n    i0.ɵɵtext(17, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"textarea\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_textarea_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CDescription, $event) || (ctx_r2.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 60)(20, \"label\", 58);\n    i0.ɵɵtext(21, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CPrice, $event) || (ctx_r2.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 60)(24, \"label\", 58);\n    i0.ɵɵtext(25, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CStatus, $event) || (ctx_r2.selectedMaterial.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, BuildingMaterialComponent_ng_template_59_nb_option_27_Template, 2, 2, \"nb-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 67)(29, \"label\", 62);\n    i0.ɵɵtext(30, \"\\u5716\\u7247\\u7D81\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 68)(32, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const imageBinder_r12 = i0.ɵɵreference(62);\n      return i0.ɵɵresetView(ctx_r2.openImageBinder(imageBinder_r12));\n    });\n    i0.ɵɵelement(33, \"i\", 70);\n    i0.ɵɵtext(34, \"\\u9078\\u64C7\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, BuildingMaterialComponent_ng_template_59_div_35_Template, 3, 1, \"div\", 71);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"nb-card-footer\", 31)(37, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_37_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵtext(38, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_39_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(40, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CMaterialCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"title\", \"\\u70BA\\u5EFA\\u6750\\u7D81\\u5B9A\\u5716\\u7247\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedMaterial.CMaterialCode);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r17.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r17.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_40_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 133);\n  }\n  if (rf & 2) {\n    const image_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r19.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134);\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵelementStart(2, \"div\", 136);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_div_40_Template_div_click_0_listener() {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.moveToSelected(image_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 123);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_61_div_40_img_2_Template, 1, 2, \"img\", 124)(3, BuildingMaterialComponent_ng_template_61_div_40_div_3_Template, 4, 0, \"div\", 125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 126)(5, \"div\", 127);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 128)(8, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_div_40_Template_button_click_8_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const imagePreviewComponent_r20 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r2.previewImage(image_r19, imagePreviewComponent_r20, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_div_40_Template_button_click_10_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.moveToSelected(image_r19, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 132);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r19.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r19.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r19.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵelement(1, \"i\", 138);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u53EF\\u9078\\u64C7\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_63_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵtext(1, \" \\u5DF2\\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_63_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147);\n    i0.ɵɵtext(1, \" \\u65B0\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_63_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 133);\n  }\n  if (rf & 2) {\n    const image_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r22.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r22.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_63_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134);\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵelementStart(2, \"div\", 136);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"div\", 140);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_61_div_63_div_2_Template, 2, 0, \"div\", 141)(3, BuildingMaterialComponent_ng_template_61_div_63_div_3_Template, 2, 0, \"div\", 142);\n    i0.ɵɵelementStart(4, \"small\", 143);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 123);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_61_div_63_img_7_Template, 1, 2, \"img\", 124)(8, BuildingMaterialComponent_ng_template_61_div_63_div_8_Template, 4, 0, \"div\", 125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 126)(10, \"div\", 127);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 128)(13, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_div_63_Template_button_click_13_listener($event) {\n      const image_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const imagePreviewComponent_r20 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r2.previewImage(image_r22, imagePreviewComponent_r20, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_div_63_Template_button_click_15_listener($event) {\n      const image_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.moveToAvailable(image_r22, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 145);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"border-success\", ctx_r2.isImageBound(image_r22))(\"bg-green-50\", ctx_r2.isImageBound(image_r22));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImageBound(image_r22));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImageBound(image_r22));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", i_r23 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r22.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r22.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r22.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r22.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵelement(1, \"i\", 148);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5C1A\\u672A\\u9078\\u64C7\\u4EFB\\u4F55\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 149);\n    i0.ɵɵtext(5, \"\\u5F9E\\u5DE6\\u5074\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_CollectionSizeChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedImageTotalRecords, $event) || (ctx_r2.selectedImageTotalRecords = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageSizeChange\", function BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_PageSizeChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedImagePageSize, $event) || (ctx_r2.selectedImagePageSize = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageChange\", function BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedImageCurrentPage, $event) || (ctx_r2.selectedImageCurrentPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectedImagePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx_r2.selectedImageTotalRecords)(\"PageSize\", ctx_r2.selectedImagePageSize)(\"Page\", ctx_r2.selectedImageCurrentPage);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 150);\n    i0.ɵɵelement(1, \"i\", 151);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBoundImagesCount(), \" \\u5F35\\u5DF2\\u7D81\\u5B9A \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_span_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 152);\n    i0.ɵɵelement(1, \"i\", 153);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getNewSelectedCount(), \" \\u5F35\\u65B0\\u9078\\u64C7 \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 77)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 78)(4, \"div\", 79)(5, \"div\", 80);\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵelementStart(7, \"div\", 82)(8, \"div\", 83);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 84)(11, \"span\", 85);\n    i0.ɵɵtext(12, \"\\u7576\\u524D\\u5EFA\\u6750\\u4EE3\\u865F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 86);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16, \"\\u9078\\u64C7\\u5716\\u7247\\u5F8C\\uFF0C\\u5EFA\\u6750\\u4EE3\\u865F\\u5C07\\u6703\\u81EA\\u52D5\\u8A2D\\u5B9A\\u70BA\\u6240\\u9078\\u5716\\u7247\\u7684\\u6A94\\u540D\\uFF0C\\u4E26\\u5EFA\\u7ACB\\u5716\\u7247\\u8207\\u5EFA\\u6750\\u7684\\u7D81\\u5B9A\\u95DC\\u4FC2\\u3002\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 87)(18, \"div\", 88)(19, \"label\", 89);\n    i0.ɵɵtext(20, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"nb-select\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_61_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedCategory, $event) || (ctx_r2.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function BuildingMaterialComponent_ng_template_61_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.categoryChanged($event));\n    });\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_ng_template_61_nb_option_22_Template, 2, 2, \"nb-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 91)(24, \"label\", 89);\n    i0.ɵɵtext(25, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_61_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.imageSearchTerm, $event) || (ctx_r2.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_61_Template_input_input_26_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.filterAvailableImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 93)(28, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.loadAvailableImages();\n      return i0.ɵɵresetView(ctx_r2.loadSelectedImages());\n    });\n    i0.ɵɵtext(29, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(30, \"i\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 96)(32, \"div\", 97)(33, \"div\", 98)(34, \"h6\", 99);\n    i0.ɵɵtext(35, \"\\u53EF\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 100);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 101)(39, \"div\", 102);\n    i0.ɵɵtemplate(40, BuildingMaterialComponent_ng_template_61_div_40_Template, 12, 4, \"div\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, BuildingMaterialComponent_ng_template_61_div_41_Template, 4, 0, \"div\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 105)(43, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_CollectionSizeChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.availableImageTotalRecords, $event) || (ctx_r2.availableImageTotalRecords = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageSizeChange\", function BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_PageSizeChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.availableImagePageSize, $event) || (ctx_r2.availableImagePageSize = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageChange\", function BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_PageChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.availableImageCurrentPage, $event) || (ctx_r2.availableImageCurrentPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_PageChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.availableImagePageChanged($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 106)(45, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.moveAllToSelected());\n    });\n    i0.ɵɵelement(46, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.moveAllToAvailable());\n    });\n    i0.ɵɵelement(48, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(49, \"hr\", 111);\n    i0.ɵɵelementStart(50, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearAllSelection());\n    });\n    i0.ɵɵelement(51, \"i\", 113)(52, \"br\");\n    i0.ɵɵelementStart(53, \"small\");\n    i0.ɵɵtext(54, \"\\u6E05\\u9664\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 97)(56, \"div\", 98)(57, \"h6\", 99);\n    i0.ɵɵtext(58, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 100);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 101)(62, \"div\", 102);\n    i0.ɵɵtemplate(63, BuildingMaterialComponent_ng_template_61_div_63_Template, 17, 11, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(64, BuildingMaterialComponent_ng_template_61_div_64_Template, 6, 0, \"div\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(65, BuildingMaterialComponent_ng_template_61_div_65_Template, 2, 3, \"div\", 115);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"nb-card-footer\", 116)(67, \"div\", 100);\n    i0.ɵɵtemplate(68, BuildingMaterialComponent_ng_template_61_span_68_Template, 3, 1, \"span\", 117)(69, BuildingMaterialComponent_ng_template_61_span_69_Template, 3, 1, \"span\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 119)(71, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_71_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCloseImageBinder(ref_r25));\n    });\n    i0.ɵɵtext(72, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_61_Template_button_click_73_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onConfirmImageSelection(ref_r25));\n    });\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r2.selectedMaterial.CSelectName ? \"\\u70BA \" + ctx_r2.selectedMaterial.CSelectName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedMaterial.CMaterialCode || \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.imageSearchTerm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r2.availableImageTotalRecords, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx_r2.availableImageTotalRecords)(\"PageSize\", ctx_r2.availableImagePageSize)(\"Page\", ctx_r2.availableImageCurrentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedImages.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate2(\" \\u5DF2\\u9078\\u53D6: \", ctx_r2.selectedImages.length, \" / \", ctx_r2.selectedImageTotalRecords, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedImageTotalRecords > ctx_r2.selectedImagePageSize);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getBoundImagesCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getNewSelectedCount() > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 154)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 155)(5, \"div\", 156);\n    i0.ɵɵelement(6, \"img\", 157);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 158)(9, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_65_Template_button_click_9_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      return i0.ɵɵresetView(ref_r27.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r2.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    this.CMaterialCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 簡化後只保留必要的\n    this.selectedImages = []; // 右側已選擇的圖片\n    this.imageSearchTerm = \"\";\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\n        // 不立即載入材料列表，等待建案選擇事件觸發\n      }\n    })).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        CMaterialCode: this.CMaterialCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  // 建案選擇事件處理（新）\n  onBuildCaseSelectionChange(selectedBuildCase) {\n    if (selectedBuildCase) {\n      this.selectedBuildCaseId = selectedBuildCase.cID;\n    } else if (this.listBuildCases.length > 0) {\n      this.selectedBuildCaseId = this.listBuildCases[0].cID;\n    }\n    this.search();\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1,\n      // 預設為啟用狀態\n      CPrice: 0 // 預設價格為0\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.required('[價格]', this.selectedMaterial.CPrice);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30);\n    // 價格驗證：必須為數字且大於等於0\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\n      if (this.selectedMaterial.CPrice < 0) {\n        this.valid.errorMessages.push('[價格] 不能小於0');\n      }\n    }\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CFile: target.files[0]\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        }), mergeMap(() => this.getMaterialList(1))).subscribe();\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    // 重置選擇狀態和分頁\n    this.selectedImages = [];\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  // 載入已選擇的圖片（右側），帶入 CMaterialId 參數\n  loadSelectedImages() {\n    if (this.isCategorySelected && this.selectedBuildCaseId && this.selectedMaterial?.CId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CMaterialId: this.selectedMaterial.CId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.selectedImageCurrentPage,\n          PageSize: this.selectedImagePageSize\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || '',\n            size: 0,\n            // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\n            thumbnailUrl: picture.CFile || '',\n            fullUrl: picture.CFile || '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            guid: picture.CGuid // 保存 CGuid 以便後續使用\n          })) || [];\n          this.selectedImageTotalRecords = res.TotalItems || 0;\n          // 更新已綁定的圖片ID\n          this.boundImageIds = this.selectedImages.map(img => img.id);\n        } else {\n          this.message.showErrorMSG(res.Message || '載入已選擇圖片失敗');\n          this.selectedImages = [];\n          this.selectedImageTotalRecords = 0;\n        }\n      });\n    } else {\n      // 如果是新增建材或沒有 MaterialId，初始化為已綁定圖片\n      if (this.boundImageIds.length > 0) {\n        this.loadInitialSelectedImages();\n      } else {\n        this.selectedImages = [];\n        this.selectedImageTotalRecords = 0;\n      }\n    }\n  }\n  // 載入初始已選擇圖片（用於新增建材時的已綁定圖片初始化）\n  loadInitialSelectedImages() {\n    this._pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        cPictureType: this.selectedCategory,\n        PageIndex: 1,\n        PageSize: 9999 // 使用大數字獲取所有圖片\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        const allAvailableImages = res.Entries?.map(picture => ({\n          id: picture.CId || 0,\n          name: picture.CPictureCode || '',\n          size: 0,\n          // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\n          thumbnailUrl: picture.CFile || '',\n          fullUrl: picture.CFile || '',\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n          guid: picture.CGuid // 保存 CGuid 以便後續使用\n        })) || [];\n        // 從所有圖片中找出已綁定的圖片\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\n        this.selectedImages = [...boundImages];\n        this.selectedImageTotalRecords = boundImages.length;\n      }\n    });\n  }\n  // 搜尋過濾可選擇圖片\n  filterAvailableImages() {\n    // 重新載入可選擇圖片（包含搜尋條件）\n    this.availableImageCurrentPage = 1; // 搜尋時重置到第一頁\n    this.loadAvailableImages();\n  }\n  moveToSelected(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從可選移到已選\n    const index = this.availableImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.push(image);\n      this.selectedImageTotalRecords = this.selectedImages.length;\n      // 重新載入兩側資料\n      this.loadAvailableImages();\n    }\n  }\n  moveToAvailable(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n      this.selectedImageTotalRecords = this.selectedImages.length;\n      // 重新載入兩側資料\n      this.loadAvailableImages();\n    }\n  }\n  moveAllToSelected() {\n    // 將當前頁面所有可選圖片移到已選\n    this.selectedImages.push(...this.availableImages);\n    this.selectedImageTotalRecords = this.selectedImages.length;\n    // 重新載入可選圖片\n    this.loadAvailableImages();\n  }\n  moveAllToAvailable() {\n    // 清空所有已選圖片\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    // 重新載入可選圖片\n    this.loadAvailableImages();\n  }\n  isImageBound(image) {\n    return this.boundImageIds.includes(image.id);\n  }\n  getBoundImagesCount() {\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\n  }\n  getNewSelectedCount() {\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\n  }\n  // 清除所有選擇（包括已綁定的圖片）\n  clearAllSelection() {\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    this.loadAvailableImages();\n  }\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\n  previewImage(image, imagePreviewComponent, event) {\n    event.stopPropagation();\n    // 找到點擊圖片的索引\n    const allImages = [...this.availableImages, ...this.selectedImages];\n    const imageIndex = allImages.findIndex(img => img.id === image.id);\n    // 設定預覽元件的輸入參數，讓它自行載入圖片\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\n    imagePreviewComponent.pictureType = this.selectedCategory;\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\n    imagePreviewComponent.selectedImages = this.selectedImages;\n    imagePreviewComponent.initialImageIndex = imageIndex;\n    imagePreviewComponent.showSelectionToggle = true;\n    // 開啟預覽對話框\n    imagePreviewComponent.openPreview();\n  }\n  // 處理圖片選取切換事件\n  onImageSelectionToggle(image) {\n    const isSelected = this.selectedImages.some(img => img.id === image.id);\n    if (isSelected) {\n      this.moveToAvailable(image);\n    } else {\n      this.moveToSelected(image);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    // 清理選擇狀態但不重新載入圖片（對話框即將關閉）\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    this.imageSearchTerm = \"\";\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    ref.close();\n  }\n  // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時重設頁碼並重新載入圖片\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadAvailableImages();\n      this.loadSelectedImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  // 可選擇圖片分頁變更處理方法\n  availableImagePageChanged(page) {\n    this.availableImageCurrentPage = page;\n    this.loadAvailableImages();\n  }\n  // 已選擇圖片分頁變更處理方法\n  selectedImagePageChanged(page) {\n    this.selectedImageCurrentPage = page;\n    this.loadSelectedImages();\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 67,\n      vars: 12,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreviewComponent\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [1, \"w-[78%]\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 1, \"w-full\", 3, \"selectedValueChange\", \"selectionChange\", \"selectedValue\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u4EE3\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"imageSelectionToggle\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-danger mr-2\", 4, \"ngIf\"], [1, \"badge\"], [1, \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-danger\", \"mr-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"20\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"50\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", \"placeholder\", \"0\", \"required\", \"\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\", \"mr-2\"], [\"class\", \"text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [3, \"value\"], [1, \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"text-green-500\", \"mr-2\"], [1, \"w-[90vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"bg-blue-50\", \"border\", \"border-blue-200\", \"rounded-lg\", \"p-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\", \"text-blue-500\", \"mt-1\"], [1, \"text-sm\", \"text-blue-700\"], [1, \"font-medium\", \"mb-1\"], [1, \"mb-2\"], [1, \"font-medium\"], [1, \"bg-white\", \"px-2\", \"py-1\", \"rounded\", \"border\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"w-48\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", \"block\"], [1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-4\", \"flex-1\", 2, \"min-height\", \"0\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\", 2, \"min-height\", \"0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\", \"font-medium\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"flex-1\", 2, \"overflow-y\", \"auto\"], [1, \"grid\", \"grid-cols-3\", \"gap-2\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"mt-3\", \"d-flex\", \"justify-content-center\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"80px\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u5DF2\\u9078\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u53EF\\u9078\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"my-2\"], [\"title\", \"\\u6E05\\u9664\\u6240\\u6709\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"border-success\", \"bg-green-50\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-3 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"text-info ml-3\", 4, \"ngIf\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", \"hover:bg-gray-50\", 3, \"click\"], [1, \"w-full\", \"h-24\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mt-2\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"btn\", \"btn-outline-primary\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"class\", \"badge badge-success text-xs px-2 py-1\", \"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 4, \"ngIf\"], [\"class\", \"badge badge-info text-xs px-2 py-1\", 4, \"ngIf\"], [1, \"text-gray-500\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 1, \"badge\", \"badge-success\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"badge\", \"badge-info\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"text-sm\", \"mt-2\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\"], [1, \"text-info\", \"ml-3\"], [1, \"fas\", \"fa-plus-circle\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"app-build-case-select\", 12);\n          i0.ɵɵtwoWayListener(\"selectedValueChange\", function BuildingMaterialComponent_Template_app_build_case_select_selectedValueChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectionChange\", function BuildingMaterialComponent_Template_app_build_case_select_selectionChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseSelectionChange($event));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 9)(20, \"label\", 10);\n          i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CMaterialCode, $event) || (ctx.CMaterialCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16)(25, \"nb-checkbox\", 17);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(26, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, BuildingMaterialComponent_button_27_Template, 3, 0, \"button\", 18)(28, BuildingMaterialComponent_button_28_Template, 3, 0, \"button\", 19)(29, BuildingMaterialComponent_button_29_Template, 3, 0, \"button\", 20)(30, BuildingMaterialComponent_button_30_Template, 2, 0, \"button\", 21);\n          i0.ɵɵelementStart(31, \"input\", 22, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_33_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(34, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"table\", 26)(38, \"thead\")(39, \"tr\", 27)(40, \"th\", 28);\n          i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 28);\n          i0.ɵɵtext(43, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 28);\n          i0.ɵɵtext(45, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"th\", 29);\n          i0.ɵɵtext(47, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 28);\n          i0.ɵɵtext(49, \"\\u5DF2\\u7D81\\u5B9A\\u5716\\u7247\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 28);\n          i0.ɵɵtext(51, \"\\u50F9\\u683C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"th\", 28);\n          i0.ɵɵtext(53, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"th\", 28);\n          i0.ɵɵtext(55, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(56, BuildingMaterialComponent_tbody_56_Template, 2, 1, \"tbody\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"nb-card-footer\", 31)(58, \"ngx-pagination\", 32);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(59, BuildingMaterialComponent_ng_template_59_Template, 41, 9, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(61, BuildingMaterialComponent_ng_template_61_Template, 75, 23, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(63, \"app-image-preview\", 33, 3);\n          i0.ɵɵlistener(\"imageSelectionToggle\", function BuildingMaterialComponent_Template_app_image_preview_imageSelectionToggle_63_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onImageSelectionToggle($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, BuildingMaterialComponent_ng_template_65_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtwoWayProperty(\"selectedValue\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CMaterialCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.RequiredValidator, i8.MaxLengthValidator, i8.MinValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, BuildCaseSelectComponent, ImagePreviewComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n\\n\\n\\n.picklist-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  height: 100%;\\n  min-height: 400px;\\n}\\n\\n.picklist-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  background-color: #fafafa;\\n}\\n.picklist-panel[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.picklist-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 80px;\\n  gap: 0.5rem;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0.5rem 0;\\n  border-color: #ddd;\\n}\\n\\n.image-grid-item.hover\\\\:bg-gray-50[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n.image-grid-item.border-success[_ngcontent-%COMP%] {\\n  border-color: #28a745 !important;\\n  background-color: #f8fff9;\\n}\\n.image-grid-item.bg-green-50[_ngcontent-%COMP%] {\\n  background-color: #f0fff4;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .picklist-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: auto;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: auto;\\n    min-width: 60px;\\n  }\\n  .grid.grid-cols-3[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "BuildCaseSelectComponent", "ImagePreviewComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "BuildingMaterialComponent_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "BuildingMaterialComponent_button_28_Template_button_click_0_listener", "_r4", "search", "BuildingMaterialComponent_button_29_Template_button_click_0_listener", "_r5", "dialog_r6", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_30_Template_button_click_0_listener", "_r7", "inputFile_r8", "click", "ɵɵadvance", "ɵɵtextInterpolate", "item_r9", "CSelectPictureId", "length", "BuildingMaterialComponent_tbody_56_tr_1_button_21_Template_button_click_0_listener", "_r10", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_56_tr_1_button_22_Template_button_click_0_listener", "_r11", "imageBinder_r12", "bindImageForMaterial", "ɵɵproperty", "CSelectName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_56_tr_1_span_11_Template", "BuildingMaterialComponent_tbody_56_tr_1_span_12_Template", "BuildingMaterialComponent_tbody_56_tr_1_button_21_Template", "BuildingMaterialComponent_tbody_56_tr_1_button_22_Template", "CId", "CMaterialCode", "ɵɵstyleMap", "CIsMapping", "CDescription", "CPrice", "ɵɵclassMap", "CStatus", "ɵɵtextInterpolate1", "getStatusLabel", "isRead", "BuildingMaterialComponent_tbody_56_tr_1_Template", "materialList", "option_r14", "value", "label", "selectedMaterial", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_10_listener", "$event", "_r13", "ɵɵtwoWayBindingSet", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_59_Template_textarea_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_59_Template_nb_select_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_59_nb_option_27_Template", "BuildingMaterialComponent_ng_template_59_Template_button_click_32_listener", "openImageBinder", "BuildingMaterialComponent_ng_template_59_div_35_Template", "BuildingMaterialComponent_ng_template_59_Template_button_click_37_listener", "ref_r15", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_59_Template_button_click_39_listener", "onSubmit", "ɵɵtwoWayProperty", "statusOptions", "option_r17", "image_r19", "thumbnailUrl", "ɵɵsanitizeUrl", "name", "BuildingMaterialComponent_ng_template_61_div_40_Template_div_click_0_listener", "_r18", "moveToSelected", "BuildingMaterialComponent_ng_template_61_div_40_img_2_Template", "BuildingMaterialComponent_ng_template_61_div_40_div_3_Template", "BuildingMaterialComponent_ng_template_61_div_40_Template_button_click_8_listener", "imagePreviewComponent_r20", "previewImage", "BuildingMaterialComponent_ng_template_61_div_40_Template_button_click_10_listener", "image_r22", "BuildingMaterialComponent_ng_template_61_div_63_div_2_Template", "BuildingMaterialComponent_ng_template_61_div_63_div_3_Template", "BuildingMaterialComponent_ng_template_61_div_63_img_7_Template", "BuildingMaterialComponent_ng_template_61_div_63_div_8_Template", "BuildingMaterialComponent_ng_template_61_div_63_Template_button_click_13_listener", "_r21", "BuildingMaterialComponent_ng_template_61_div_63_Template_button_click_15_listener", "moveToAvailable", "ɵɵclassProp", "isImageBound", "i_r23", "BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_CollectionSizeChange_1_listener", "_r24", "selectedImageTotalRecords", "BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_PageSizeChange_1_listener", "selectedImagePageSize", "BuildingMaterialComponent_ng_template_61_div_65_Template_ngx_pagination_PageChange_1_listener", "selectedImageCurrentPage", "selectedImagePageChanged", "getBoundImagesCount", "getNewSelectedCount", "BuildingMaterialComponent_ng_template_61_Template_nb_select_ngModelChange_21_listener", "_r16", "selectedCate<PERSON><PERSON>", "BuildingMaterialComponent_ng_template_61_Template_nb_select_selectedChange_21_listener", "categoryChanged", "BuildingMaterialComponent_ng_template_61_nb_option_22_Template", "BuildingMaterialComponent_ng_template_61_Template_input_ngModelChange_26_listener", "imageSearchTerm", "BuildingMaterialComponent_ng_template_61_Template_input_input_26_listener", "filterAvailableImages", "BuildingMaterialComponent_ng_template_61_Template_button_click_28_listener", "loadAvailableImages", "loadSelectedImages", "BuildingMaterialComponent_ng_template_61_div_40_Template", "BuildingMaterialComponent_ng_template_61_div_41_Template", "BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_CollectionSizeChange_43_listener", "availableImageTotalRecords", "BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_PageSizeChange_43_listener", "availableImagePageSize", "BuildingMaterialComponent_ng_template_61_Template_ngx_pagination_PageChange_43_listener", "availableImageCurrentPage", "availableImagePageChanged", "BuildingMaterialComponent_ng_template_61_Template_button_click_45_listener", "moveAllToSelected", "BuildingMaterialComponent_ng_template_61_Template_button_click_47_listener", "moveAllToAvailable", "BuildingMaterialComponent_ng_template_61_Template_button_click_50_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_61_div_63_Template", "BuildingMaterialComponent_ng_template_61_div_64_Template", "BuildingMaterialComponent_ng_template_61_div_65_Template", "BuildingMaterialComponent_ng_template_61_span_68_Template", "BuildingMaterialComponent_ng_template_61_span_69_Template", "BuildingMaterialComponent_ng_template_61_Template_button_click_71_listener", "ref_r25", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_61_Template_button_click_73_listener", "onConfirmImageSelection", "categoryOptions", "availableImages", "selectedImages", "ɵɵtextInterpolate2", "BuildingMaterialComponent_ng_template_65_Template_button_click_9_listener", "ref_r27", "_r26", "close", "currentImageShowing", "PictureCategory", "BuildingMaterialComponent", "status", "option", "find", "opt", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "ShowPrice", "filterMapping", "BUILDING_MATERIAL", "SCHEMATIC", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "pipe", "res", "StatusCode", "Entries", "subscribe", "getMaterialList", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "selectedBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "CShowPrice", "onBuildCaseSelectionChange", "selectedBuildCase", "cID", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "undefined", "errorMessages", "push", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "utils", "sheet_to_json", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "apiPictureGetPictureListPost$Json", "cPictureType", "map", "picture", "id", "CPictureCode", "size", "fullUrl", "lastModified", "CUpdateDT", "Date", "guid", "CGuid", "boundImageIds", "img", "loadInitialSelectedImages", "allAvailableImages", "boundImages", "filter", "image", "includes", "stopPropagation", "index", "findIndex", "splice", "imagePreviewComponent", "allImages", "imageIndex", "buildCaseId", "materialId", "pictureType", "searchTerm", "initialImageIndex", "showSelectionToggle", "openPreview", "onImageSelectionToggle", "isSelected", "some", "saveImageBinding", "category", "getCategoryLabel", "page", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "PictureService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_app_build_case_select_selectedValueChange_12_listener", "_r1", "BuildingMaterialComponent_Template_app_build_case_select_selectionChange_12_listener", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_25_listener", "BuildingMaterialComponent_button_27_Template", "BuildingMaterialComponent_button_28_Template", "BuildingMaterialComponent_button_29_Template", "BuildingMaterialComponent_button_30_Template", "BuildingMaterialComponent_Template_input_change_31_listener", "BuildingMaterialComponent_Template_button_click_33_listener", "BuildingMaterialComponent_tbody_56_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_58_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_58_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener", "BuildingMaterialComponent_ng_template_59_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_61_Template", "BuildingMaterialComponent_Template_app_image_preview_imageSelectionToggle_63_listener", "BuildingMaterialComponent_ng_template_65_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "RequiredValidator", "MaxLengthValidator", "MinValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\r\nimport { ImagePreviewComponent, ImageItem } from '../../../shared/components/image-preview';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 簡化後只保留必要的\r\n  selectedImages: ImageItem[] = [] // 右側已選擇的圖片\r\n  imageSearchTerm: string = \"\"\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\r\n            // 不立即載入材料列表，等待建案選擇事件觸發\r\n          }\r\n        })\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        CMaterialCode: this.CMaterialCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  // 建案選擇事件處理（新）\r\n  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {\r\n    if (selectedBuildCase) {\r\n      this.selectedBuildCaseId = selectedBuildCase.cID!;\r\n    } else if (this.listBuildCases.length > 0) {\r\n      this.selectedBuildCaseId = this.listBuildCases[0].cID!;\r\n    }\r\n    this.search();\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1, // 預設為啟用狀態\r\n      CPrice: 0   // 預設價格為0\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.required('[價格]', this.selectedMaterial.CPrice)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\r\n    // 價格驗證：必須為數字且大於等於0\r\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\r\n      if (this.selectedMaterial.CPrice < 0) {\r\n        this.valid.errorMessages.push('[價格] 不能小於0')\r\n      }\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n\r\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CFile: target.files[0]\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG(\"執行成功\")\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n          }),\r\n          mergeMap(() => this.getMaterialList(1))\r\n        ).subscribe();\r\n\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    // 重置選擇狀態和分頁\r\n    this.selectedImages = []\r\n    this.availableImageCurrentPage = 1\r\n    this.selectedImageCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n\r\n\r\n  // 載入已選擇的圖片（右側），帶入 CMaterialId 參數\r\n  loadSelectedImages() {\r\n    if (this.isCategorySelected && this.selectedBuildCaseId && this.selectedMaterial?.CId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CMaterialId: this.selectedMaterial.CId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.selectedImageCurrentPage,\r\n          PageSize: this.selectedImagePageSize\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || '',\r\n            size: 0,\r\n            // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\r\n            thumbnailUrl: picture.CFile || '',\r\n            fullUrl: picture.CFile || '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            guid: picture.CGuid // 保存 CGuid 以便後續使用\r\n          })) || [];\r\n\r\n          this.selectedImageTotalRecords = res.TotalItems || 0;\r\n\r\n          // 更新已綁定的圖片ID\r\n          this.boundImageIds = this.selectedImages.map(img => img.id);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入已選擇圖片失敗');\r\n          this.selectedImages = [];\r\n          this.selectedImageTotalRecords = 0;\r\n        }\r\n      });\r\n    } else {\r\n      // 如果是新增建材或沒有 MaterialId，初始化為已綁定圖片\r\n      if (this.boundImageIds.length > 0) {\r\n        this.loadInitialSelectedImages();\r\n      } else {\r\n        this.selectedImages = [];\r\n        this.selectedImageTotalRecords = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 載入初始已選擇圖片（用於新增建材時的已綁定圖片初始化）\r\n  loadInitialSelectedImages() {\r\n    this._pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        cPictureType: this.selectedCategory,\r\n        PageIndex: 1,\r\n        PageSize: 9999 // 使用大數字獲取所有圖片\r\n      }\r\n    }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n      if (res.StatusCode === 0) {\r\n        const allAvailableImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n          id: picture.CId || 0,\r\n          name: picture.CPictureCode || '',\r\n          size: 0,\r\n          // TODO: 使用 CFile 作為圖片路徑，或透過 CGuid 調用 apiPictureGetPictureGuidGet 取得完整圖片資料\r\n          thumbnailUrl: picture.CFile || '',\r\n          fullUrl: picture.CFile || '',\r\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n          guid: picture.CGuid // 保存 CGuid 以便後續使用\r\n        })) || [];\r\n\r\n        // 從所有圖片中找出已綁定的圖片\r\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\r\n        this.selectedImages = [...boundImages];\r\n        this.selectedImageTotalRecords = boundImages.length;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 搜尋過濾可選擇圖片\r\n  filterAvailableImages() {\r\n    // 重新載入可選擇圖片（包含搜尋條件）\r\n    this.availableImageCurrentPage = 1; // 搜尋時重置到第一頁\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  moveToSelected(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從可選移到已選\r\n    const index = this.availableImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.push(image);\r\n      this.selectedImageTotalRecords = this.selectedImages.length;\r\n      // 重新載入兩側資料\r\n      this.loadAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveToAvailable(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\r\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n      this.selectedImageTotalRecords = this.selectedImages.length;\r\n      // 重新載入兩側資料\r\n      this.loadAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveAllToSelected() {\r\n    // 將當前頁面所有可選圖片移到已選\r\n    this.selectedImages.push(...this.availableImages);\r\n    this.selectedImageTotalRecords = this.selectedImages.length;\r\n    // 重新載入可選圖片\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  moveAllToAvailable() {\r\n    // 清空所有已選圖片\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    // 重新載入可選圖片\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  isImageBound(image: ImageItem): boolean {\r\n    return this.boundImageIds.includes(image.id);\r\n  }\r\n\r\n  getBoundImagesCount(): number {\r\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\r\n  }\r\n\r\n  getNewSelectedCount(): number {\r\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\r\n  }\r\n\r\n  // 清除所有選擇（包括已綁定的圖片）\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\r\n  previewImage(image: ImageItem, imagePreviewComponent: ImagePreviewComponent, event: Event) {\r\n    event.stopPropagation();\r\n\r\n    // 找到點擊圖片的索引\r\n    const allImages = [...this.availableImages, ...this.selectedImages];\r\n    const imageIndex = allImages.findIndex((img: ImageItem) => img.id === image.id);\r\n\r\n    // 設定預覽元件的輸入參數，讓它自行載入圖片\r\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\r\n    imagePreviewComponent.pictureType = this.selectedCategory;\r\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\r\n    imagePreviewComponent.selectedImages = this.selectedImages;\r\n    imagePreviewComponent.initialImageIndex = imageIndex;\r\n    imagePreviewComponent.showSelectionToggle = true;\r\n\r\n    // 開啟預覽對話框\r\n    imagePreviewComponent.openPreview();\r\n  }\r\n\r\n  // 處理圖片選取切換事件\r\n  onImageSelectionToggle(image: ImageItem) {\r\n    const isSelected = this.selectedImages.some(img => img.id === image.id);\r\n    if (isSelected) {\r\n      this.moveToAvailable(image);\r\n    } else {\r\n      this.moveToSelected(image);\r\n    }\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID\r\n\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n  onCloseImageBinder(ref: any) {\r\n    // 清理選擇狀態但不重新載入圖片（對話框即將關閉）\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    this.imageSearchTerm = \"\";\r\n    this.availableImageCurrentPage = 1;\r\n    this.selectedImageCurrentPage = 1;\r\n    ref.close();\r\n  }\r\n\r\n  // 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.availableImageCurrentPage = 1;\r\n    this.selectedImageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadAvailableImages();\r\n      this.loadSelectedImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n  // 可選擇圖片分頁變更處理方法\r\n  availableImagePageChanged(page: number) {\r\n    this.availableImageCurrentPage = page;\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  // 已選擇圖片分頁變更處理方法\r\n  selectedImagePageChanged(page: number) {\r\n    this.selectedImageCurrentPage = page;\r\n    this.loadSelectedImages();\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label> \r\n          <div class=\"w-[78%]\">\r\n            <app-build-case-select \r\n              [(selectedValue)]=\"selectedBuildCaseId\"\r\n              (selectionChange)=\"onBuildCaseSelectionChange($event)\"\r\n              placeholder=\"請選擇建案\"\r\n              class=\"w-full\">\r\n            </app-build-case-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\" maxlength=\"50\">\r\n        </div>\r\n      </div>\r\n      <!-- 啟用建材代號欄位 -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號</label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CMaterialCode\" class=\"w-full\" maxlength=\"20\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">建材代號</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\">已綁定圖片</th>\r\n            <th scope=\"col\" class=\"col-1\">價格</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CMaterialCode || '待設定' }}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td>\r\n              <div class=\"d-flex align-items-center\">\r\n                <span *ngIf=\"item.CSelectPictureId && item.CSelectPictureId.length > 0\"\r\n                  class=\"badge badge-success mr-2\">{{ item.CSelectPictureId.length }}</span>\r\n                <span *ngIf=\"!item.CSelectPictureId || item.CSelectPictureId.length === 0\"\r\n                  class=\"badge badge-danger mr-2\">0</span>\r\n                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>\r\n              </div>\r\n            </td>\r\n            <td>{{ item.CPrice}}</td>\r\n            <td>\r\n              <span class=\"badge\" [class]=\"item.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ getStatusLabel(item.CStatus || 0) }}\r\n              </span>\r\n            </td>\r\n            <td class=\"w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button> <button class=\"btn btn-outline-info btn-sm m-1\"\r\n                (click)=\"bindImageForMaterial(item, imageBinder)\" *ngIf=\"isRead\"\r\n                [title]=\"'為 ' + item.CSelectName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i> 綁定\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 啟用建材代號欄位 -->\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"20\"\r\n            [(ngModel)]=\"selectedMaterial.CMaterialCode\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"50\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" min=\"0\" step=\"0.01\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" placeholder=\"0\" required />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">狀態</label>\r\n          <nb-select [(ngModel)]=\"selectedMaterial.CStatus\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <!-- 圖片綁定按鈕 -->\r\n        <div class=\"flex items-center mt-4 pt-3 border-t border-gray-200\">\r\n          <label class=\"w-[150px]\">圖片綁定</label>\r\n          <div class=\"flex gap-2 w-full\">\r\n            <button type=\"button\" class=\"btn btn-outline-info btn-sm\" (click)=\"openImageBinder(imageBinder)\"\r\n              [title]=\"'為建材綁定圖片'\">\r\n              <i class=\"fas fa-images mr-2\"></i>選擇圖片\r\n            </button>\r\n            <div class=\"text-sm text-gray-600 flex items-center\" *ngIf=\"selectedMaterial.CMaterialCode\">\r\n              <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\r\n              已設定建材代號: {{ selectedMaterial.CMaterialCode }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[90vw] max-w-[1200px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ selectedMaterial.CSelectName ? '為 ' + selectedMaterial.CSelectName + ' 選擇建材圖片' : '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 d-flex flex-column\"\r\n      style=\"height: calc(100% - 120px); overflow: hidden; padding-bottom: 0;\">\r\n\r\n      <!-- 自動綁定說明文案 -->\r\n      <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex items-start gap-2\">\r\n          <i class=\"fas fa-info-circle text-blue-500 mt-1\"></i>\r\n          <div class=\"text-sm text-blue-700\">\r\n            <div class=\"font-medium mb-1\">建材代號</div>\r\n            <div class=\"mb-2\">\r\n              <span class=\"font-medium\">當前建材代號：</span>\r\n              <span class=\"bg-white px-2 py-1 rounded border\">{{ selectedMaterial.CMaterialCode || '未設定' }}</span>\r\n            </div>\r\n            <div>選擇圖片後，建材代號將會自動設定為所選圖片的檔名，並建立圖片與建材的綁定關係。</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 類別選擇和搜尋 -->\r\n      <div class=\"flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"w-48\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">圖片類別</label>\r\n          <nb-select [(ngModel)]=\"selectedCategory\" (selectedChange)=\"categoryChanged($event)\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"w-full search-input\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"imageSearchTerm\"\r\n            (input)=\"filterAvailableImages()\" />\r\n        </div>\r\n        <div class=\"flex flex-col justify-end\">\r\n          <button class=\"btn btn-info btn-image-action\" (click)=\"loadAvailableImages(); loadSelectedImages()\">\r\n            重新載入 <i class=\"fas fa-refresh\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Picklist 雙欄布局 -->\r\n      <div class=\"flex gap-4 flex-1\" style=\"min-height: 0;\">\r\n\r\n        <!-- 左側：可選擇的圖片 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\" style=\"min-height: 0;\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0 font-medium\">可選擇圖片</h6>\r\n            <div class=\"text-sm text-gray-600\">\r\n              共 {{ availableImageTotalRecords }} 張圖片\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"image-preview-container flex-1\" style=\"overflow-y: auto;\">\r\n            <div class=\"grid grid-cols-3 gap-2\">\r\n              <div *ngFor=\"let image of availableImages\"\r\n                class=\"image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50\"\r\n                (click)=\"moveToSelected(image)\">\r\n\r\n                <!-- 圖片預覽 -->\r\n                <div class=\"w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n                  <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                    class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n                  <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                    <i class=\"fas fa-image text-xl mb-1\"></i>\r\n                    <div class=\"text-xs\">無預覽</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 圖片資訊 -->\r\n                <div class=\"text-xs text-gray-600\">\r\n                  <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                </div>\r\n\r\n                <!-- 操作按鈕 -->\r\n                <div class=\"flex justify-between items-center mt-2\">\r\n                  <button class=\"btn btn-outline-info btn-xs\" (click)=\"previewImage(image, imagePreviewComponent, $event)\">\r\n                    <i class=\"fas fa-eye\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-primary btn-xs\" (click)=\"moveToSelected(image, $event)\">\r\n                    <i class=\"fas fa-arrow-right\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"availableImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n              <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n              <div>找不到可選擇的圖片</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 分頁控制 -->\r\n          <div class=\"mt-3 d-flex justify-content-center\">\r\n            <ngx-pagination [(CollectionSize)]=\"availableImageTotalRecords\" [(PageSize)]=\"availableImagePageSize\"\r\n              [(Page)]=\"availableImageCurrentPage\" (PageChange)=\"availableImagePageChanged($event)\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中間：操作按鈕 -->\r\n        <div class=\"d-flex flex-column justify-content-center gap-2\" style=\"width: 80px;\">\r\n          <button class=\"btn btn-outline-primary btn-sm\" (click)=\"moveAllToSelected()\"\r\n            [disabled]=\"availableImages.length === 0\" title=\"全部移至已選\">\r\n            <i class=\"fas fa-angle-double-right\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"moveAllToAvailable()\"\r\n            [disabled]=\"selectedImages.length === 0\" title=\"全部移至可選\">\r\n            <i class=\"fas fa-angle-double-left\"></i>\r\n          </button>\r\n          <hr class=\"my-2\">\r\n          <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllSelection()\"\r\n            [disabled]=\"selectedImages.length === 0\" title=\"清除所有選擇\">\r\n            <i class=\"fas fa-times\"></i><br>\r\n            <small>清除</small>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 右側：已選擇的圖片 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\" style=\"min-height: 0;\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0 font-medium\">已選擇圖片</h6>\r\n            <div class=\"text-sm text-gray-600\">\r\n              已選取: {{ selectedImages.length }} / {{ selectedImageTotalRecords }} 張圖片\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"image-preview-container flex-1\" style=\"overflow-y: auto;\">\r\n            <div class=\"grid grid-cols-3 gap-2\">\r\n              <div *ngFor=\"let image of selectedImages; let i = index\"\r\n                class=\"image-grid-item border rounded p-2 cursor-pointer\" [class.border-success]=\"isImageBound(image)\"\r\n                [class.bg-green-50]=\"isImageBound(image)\">\r\n\r\n                <!-- 已綁定標示 -->\r\n                <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                  <div *ngIf=\"isImageBound(image)\" class=\"badge badge-success text-xs px-2 py-1\" title=\"此圖片已經綁定到此建材\">\r\n                    已綁定\r\n                  </div>\r\n                  <div *ngIf=\"!isImageBound(image)\" class=\"badge badge-info text-xs px-2 py-1\">\r\n                    新選擇\r\n                  </div>\r\n                  <small class=\"text-gray-500\">#{{ i + 1 }}</small>\r\n                </div>\r\n\r\n                <!-- 圖片預覽 -->\r\n                <div class=\"w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n                  <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                    class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n                  <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                    <i class=\"fas fa-image text-xl mb-1\"></i>\r\n                    <div class=\"text-xs\">無預覽</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 圖片資訊 -->\r\n                <div class=\"text-xs text-gray-600\">\r\n                  <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                </div>\r\n\r\n                <!-- 操作按鈕 -->\r\n                <div class=\"flex justify-between items-center mt-2\">\r\n                  <button class=\"btn btn-outline-info btn-xs\" (click)=\"previewImage(image, imagePreviewComponent, $event)\">\r\n                    <i class=\"fas fa-eye\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-danger btn-xs\" (click)=\"moveToAvailable(image, $event)\">\r\n                    <i class=\"fas fa-arrow-left\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"selectedImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n              <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n              <div>尚未選擇任何圖片</div>\r\n              <div class=\"text-sm mt-2\">從左側選擇圖片</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 已選擇圖片的分頁控制 -->\r\n          <div class=\"mt-3 d-flex justify-content-center\" *ngIf=\"selectedImageTotalRecords > selectedImagePageSize\">\r\n            <ngx-pagination [(CollectionSize)]=\"selectedImageTotalRecords\" [(PageSize)]=\"selectedImagePageSize\"\r\n              [(Page)]=\"selectedImageCurrentPage\" (PageChange)=\"selectedImagePageChanged($event)\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        <span *ngIf=\"getBoundImagesCount() > 0\" class=\"text-success\">\r\n          <i class=\"fas fa-check-circle\"></i> {{ getBoundImagesCount() }} 張已綁定\r\n        </span>\r\n        <span *ngIf=\"getNewSelectedCount() > 0\" class=\"text-info ml-3\">\r\n          <i class=\"fas fa-plus-circle\"></i> {{ getNewSelectedCount() }} 張新選擇\r\n        </span>\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" [disabled]=\"selectedImages.length === 0\"\r\n          (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽元件 -->\r\n<app-image-preview \r\n  #imagePreviewComponent\r\n  (imageSelectionToggle)=\"onImageSelectionToggle($event)\">\r\n</app-image-preview>\r\n\r\n<!-- 原有的圖片檢視對話框 -->\r\n<ng-template #dialogImage let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        檢視\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing\">\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,qBAAqB,QAAmB,0CAA0C;;;;;;;;;;;;;;;ICoCjFC,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAE,UAAA,mBAAAC,qEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,oBAAG;IAAAV,EAAA,CAAAW,SAAA,YAC5D;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC9CZ,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAE,UAAA,mBAAAW,qEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,MAAA,EAAQ;IAAA,EAAC;IACjFf,EAAA,CAAAU,MAAA,qBAAG;IAAAV,EAAA,CAAAW,SAAA,YAA6B;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC3CZ,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAE,UAAA,mBAAAc,qEAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAAClB,EAAA,CAAAU,MAAA,gCAAK;IAAAV,EAAA,CAAAW,SAAA,YAC/D;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IACrCZ,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmB,qEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAAtB,EAAA,CAAAO,aAAA;MAAA,MAAAgB,YAAA,GAAAvB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASe,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAExB,EAAA,CAAAU,MAAA,iCAAK;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;;IA6B7FZ,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAU,MAAA,GAAkC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;IAAzCZ,EAAA,CAAAyB,SAAA,EAAkC;IAAlCzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,CAAAC,MAAA,CAAkC;;;;;IACrE7B,EAAA,CAAAC,cAAA,eACkC;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;;;IAW5CZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAE,UAAA,mBAAA4B,mFAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAJ,OAAA,GAAA3B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,kBAAA,CAAAN,OAAA,EAAAT,SAAA,CAAgC;IAAA,EAAC;IAC5ElB,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;;;IAACZ,EAAA,CAAAC,cAAA,iBAEiB;IAD5CD,EAAA,CAAAE,UAAA,mBAAAgC,mFAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,IAAA;MAAA,MAAAR,OAAA,GAAA3B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAA6B,eAAA,GAAApC,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+B,oBAAA,CAAAV,OAAA,EAAAS,eAAA,CAAuC;IAAA,EAAC;IAEjDpC,EAAA,CAAAW,SAAA,YAA6B;IAACX,EAAA,CAAAU,MAAA,qBAChC;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;IAFPZ,EAAA,CAAAsC,UAAA,sBAAAX,OAAA,CAAAY,WAAA,+BAA2C;;;;;IAvB/CvC,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAU,MAAA,GAAa;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAC1CZ,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAC7EZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAE7BZ,EADF,CAAAC,cAAA,SAAI,eACqC;IAGrCD,EAFA,CAAAwC,UAAA,KAAAC,wDAAA,mBACmC,KAAAC,wDAAA,mBAED;IAClC1C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiF;IAE3FV,EAF2F,CAAAY,YAAA,EAAO,EAC1F,EACH;IACLZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAgB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAEvBZ,EADF,CAAAC,cAAA,UAAI,gBACqF;IACrFD,EAAA,CAAAU,MAAA,IACF;IACFV,EADE,CAAAY,YAAA,EAAO,EACJ;IACLZ,EAAA,CAAAC,cAAA,cAAiB;IAEcD,EAD7B,CAAAwC,UAAA,KAAAG,0DAAA,qBACiB,KAAAC,0DAAA,qBAE6B;IAIlD5C,EADE,CAAAY,YAAA,EAAK,EACF;;;;;IA3BCZ,EAAA,CAAAyB,SAAA,GAAa;IAAbzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAkB,GAAA,CAAa;IACb7C,EAAA,CAAAyB,SAAA,GAAiC;IAAjCzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAmB,aAAA,yBAAiC;IACjC9C,EAAA,CAAAyB,SAAA,EAA8C;IAA9CzB,EAAA,CAAA+C,UAAA,EAAApB,OAAA,CAAAqB,UAAA,qBAA8C;IAAChD,EAAA,CAAAyB,SAAA,EAAqB;IAArBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAY,WAAA,CAAqB;IACpEvC,EAAA,CAAAyB,SAAA,GAAsB;IAAtBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAsB,YAAA,CAAsB;IAGfjD,EAAA,CAAAyB,SAAA,GAA+D;IAA/DzB,EAAA,CAAAsC,UAAA,SAAAX,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,KAA+D;IAE/D7B,EAAA,CAAAyB,SAAA,EAAkE;IAAlEzB,EAAA,CAAAsC,UAAA,UAAAX,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,OAAkE;IAEnE7B,EAAA,CAAAyB,SAAA,GAAiF;IAAjFzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,mDAAiF;IAGvF7B,EAAA,CAAAyB,SAAA,GAAgB;IAAhBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAuB,MAAA,CAAgB;IAEElD,EAAA,CAAAyB,SAAA,GAAkE;IAAlEzB,EAAA,CAAAmD,UAAA,CAAAxB,OAAA,CAAAyB,OAAA,6CAAkE;IACpFpD,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqD,kBAAA,MAAA/C,MAAA,CAAAgD,cAAA,CAAA3B,OAAA,CAAAyB,OAAA,YACF;IAIGpD,EAAA,CAAAyB,SAAA,GAAY;IAAZzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAiD,MAAA,CAAY;IACsCvD,EAAA,CAAAyB,SAAA,EAAY;IAAZzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAiD,MAAA,CAAY;;;;;IAxBvEvD,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAAwC,UAAA,IAAAgB,gDAAA,mBAAsD;IA6BxDxD,EAAA,CAAAY,YAAA,EAAQ;;;;IA7BeZ,EAAA,CAAAyB,SAAA,EAAkB;IAAlBzB,EAAA,CAAAsC,UAAA,YAAAhC,MAAA,CAAAmD,YAAA,CAAkB;;;;;IA0ErCzD,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAY;;;;IAFoCZ,EAAA,CAAAsC,UAAA,UAAAoB,UAAA,CAAAC,KAAA,CAAsB;IACpE3D,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqD,kBAAA,MAAAK,UAAA,CAAAE,KAAA,MACF;;;;;IAYA5D,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAW,SAAA,YAAuD;IACvDX,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;IADJZ,EAAA,CAAAyB,SAAA,GACF;IADEzB,EAAA,CAAAqD,kBAAA,kDAAA/C,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,MACF;;;;;;IAjDR9C,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAU,MAAA,4DACF;IAAAV,EAAA,CAAAY,YAAA,EAAiB;IAEfZ,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAU,MAAA,+EAAY;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAInCZ,EAHJ,CAAAC,cAAA,cAAyB,cAEQ,gBACW;IAAAD,EAAA,CAAAU,MAAA,+BAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACpDZ,EAAA,CAAAC,cAAA,iBACiD;IAA/CD,EAAA,CAAA8D,gBAAA,2BAAAC,kFAAAC,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,EAAAkB,MAAA,MAAA1D,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,GAAAkB,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA4C;IAChDhE,EAFE,CAAAY,YAAA,EACiD,EAC7C;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,4CAAM;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACtDZ,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAA8D,gBAAA,2BAAAK,kFAAAH,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAuD,gBAAA,CAAAtB,WAAA,EAAAyB,MAAA,MAAA1D,MAAA,CAAAuD,gBAAA,CAAAtB,WAAA,GAAAyB,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA0C;IAC9ChE,EAFE,CAAAY,YAAA,EAC+C,EAC3C;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACrCZ,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAA8D,gBAAA,2BAAAM,qFAAAJ,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAuD,gBAAA,CAAAZ,YAAA,EAAAe,MAAA,MAAA1D,MAAA,CAAAuD,gBAAA,CAAAZ,YAAA,GAAAe,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA2C;IAE/DhE,EADmE,CAAAY,YAAA,EAAW,EACxE;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClDZ,EAAA,CAAAC,cAAA,iBACmE;IAAjED,EAAA,CAAA8D,gBAAA,2BAAAO,kFAAAL,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAuD,gBAAA,CAAAX,MAAA,EAAAc,MAAA,MAAA1D,MAAA,CAAAuD,gBAAA,CAAAX,MAAA,GAAAc,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAAqC;IACzChE,EAFE,CAAAY,YAAA,EACmE,EAC/D;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClDZ,EAAA,CAAAC,cAAA,qBAAiE;IAAtDD,EAAA,CAAA8D,gBAAA,2BAAAQ,sFAAAN,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAuD,gBAAA,CAAAT,OAAA,EAAAY,MAAA,MAAA1D,MAAA,CAAAuD,gBAAA,CAAAT,OAAA,GAAAY,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAAsC;IAC/ChE,EAAA,CAAAwC,UAAA,KAAA+B,8DAAA,wBAAuE;IAI3EvE,EADE,CAAAY,YAAA,EAAY,EACR;IAIJZ,EADF,CAAAC,cAAA,eAAkE,iBACvC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAEnCZ,EADF,CAAAC,cAAA,eAA+B,kBAEP;IADoCD,EAAA,CAAAE,UAAA,mBAAAsE,2EAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAA6B,eAAA,GAAApC,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmE,eAAA,CAAArC,eAAA,CAA4B;IAAA,EAAC;IAE9FpC,EAAA,CAAAW,SAAA,aAAkC;IAAAX,EAAA,CAAAU,MAAA,iCACpC;IAAAV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAwC,UAAA,KAAAkC,wDAAA,kBAA4F;IAOpG1E,EAHM,CAAAY,YAAA,EAAM,EACF,EACF,EACO;IAEbZ,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAE,UAAA,mBAAAyE,2EAAA;MAAA,MAAAC,OAAA,GAAA5E,EAAA,CAAAI,aAAA,CAAA6D,IAAA,EAAAY,SAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwE,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC5E,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAS;IAC7EZ,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAE,UAAA,mBAAA6E,2EAAA;MAAA,MAAAH,OAAA,GAAA5E,EAAA,CAAAI,aAAA,CAAA6D,IAAA,EAAAY,SAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0E,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAC5E,EAAA,CAAAU,MAAA,oBAAE;IAErEV,EAFqE,CAAAY,YAAA,EAAS,EAC3D,EACT;;;;IAhDAZ,EAAA,CAAAyB,SAAA,IAA4C;IAA5CzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,CAA4C;IAK5C9C,EAAA,CAAAyB,SAAA,GAA0C;IAA1CzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAuD,gBAAA,CAAAtB,WAAA,CAA0C;IAK1BvC,EAAA,CAAAyB,SAAA,GAA2C;IAA3CzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAuD,gBAAA,CAAAZ,YAAA,CAA2C;IAACjD,EAAA,CAAAsC,UAAA,WAAU;IAMtEtC,EAAA,CAAAyB,SAAA,GAAqC;IAArCzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAuD,gBAAA,CAAAX,MAAA,CAAqC;IAK5BlD,EAAA,CAAAyB,SAAA,GAAsC;IAAtCzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAuD,gBAAA,CAAAT,OAAA,CAAsC;IACjBpD,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAAsC,UAAA,YAAAhC,MAAA,CAAA4E,aAAA,CAAgB;IAW5ClF,EAAA,CAAAyB,SAAA,GAAmB;IAAnBzB,EAAA,CAAAsC,UAAA,uDAAmB;IAGiCtC,EAAA,CAAAyB,SAAA,GAAoC;IAApCzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,CAAoC;;;;;IA2C1F9C,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAY;;;;IAFsCZ,EAAA,CAAAsC,UAAA,UAAA6C,UAAA,CAAAxB,KAAA,CAAsB;IACtE3D,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqD,kBAAA,MAAA8B,UAAA,CAAAvB,KAAA,MACF;;;;;IAmCM5D,EAAA,CAAAW,SAAA,eACiE;;;;IADNX,EAA3B,CAAAsC,UAAA,QAAA8C,SAAA,CAAAC,YAAA,EAAArF,EAAA,CAAAsF,aAAA,CAA0B,QAAAF,SAAA,CAAAG,IAAA,CAAmB;;;;;IAE7EvF,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAW,SAAA,aAAyC;IACzCX,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAU,MAAA,yBAAG;IAC1BV,EAD0B,CAAAY,YAAA,EAAM,EAC1B;;;;;;IAXVZ,EAAA,CAAAC,cAAA,eAEkC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAsF,8EAAA;MAAA,MAAAJ,SAAA,GAAApF,EAAA,CAAAI,aAAA,CAAAqF,IAAA,EAAAzD,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoF,cAAA,CAAAN,SAAA,CAAqB;IAAA,EAAC;IAG/BpF,EAAA,CAAAC,cAAA,eAAmG;IAGjGD,EAFA,CAAAwC,UAAA,IAAAmD,8DAAA,mBACiE,IAAAC,8DAAA,mBACE;IAIrE5F,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAAmC,eACsB;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IACzEV,EADyE,CAAAY,YAAA,EAAM,EACzE;IAIJZ,EADF,CAAAC,cAAA,eAAoD,kBACuD;IAA7DD,EAAA,CAAAE,UAAA,mBAAA2F,iFAAA7B,MAAA;MAAA,MAAAoB,SAAA,GAAApF,EAAA,CAAAI,aAAA,CAAAqF,IAAA,EAAAzD,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAuF,yBAAA,GAAA9F,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyF,YAAA,CAAAX,SAAA,EAAAU,yBAAA,EAAA9B,MAAA,CAAkD;IAAA,EAAC;IACtGhE,EAAA,CAAAW,SAAA,aAA0B;IAC5BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,mBAAuF;IAAxCD,EAAA,CAAAE,UAAA,mBAAA8F,kFAAAhC,MAAA;MAAA,MAAAoB,SAAA,GAAApF,EAAA,CAAAI,aAAA,CAAAqF,IAAA,EAAAzD,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoF,cAAA,CAAAN,SAAA,EAAApB,MAAA,CAA6B;IAAA,EAAC;IACpFhE,EAAA,CAAAW,SAAA,cAAkC;IAGxCX,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;;IAtBIZ,EAAA,CAAAyB,SAAA,GAAwB;IAAxBzB,EAAA,CAAAsC,UAAA,SAAA8C,SAAA,CAAAC,YAAA,CAAwB;IAExBrF,EAAA,CAAAyB,SAAA,EAAyB;IAAzBzB,EAAA,CAAAsC,UAAA,UAAA8C,SAAA,CAAAC,YAAA,CAAyB;IAQGrF,EAAA,CAAAyB,SAAA,GAAoB;IAApBzB,EAAA,CAAAsC,UAAA,UAAA8C,SAAA,CAAAG,IAAA,CAAoB;IAACvF,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAA0B,iBAAA,CAAA0D,SAAA,CAAAG,IAAA,CAAgB;;;;;IAgB7EvF,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAW,SAAA,aAA2C;IAC3CX,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAU,MAAA,6DAAS;IAChBV,EADgB,CAAAY,YAAA,EAAM,EAChB;;;;;IA8CAZ,EAAA,CAAAC,cAAA,eAAmG;IACjGD,EAAA,CAAAU,MAAA,2BACF;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IACNZ,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAU,MAAA,2BACF;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAMNZ,EAAA,CAAAW,SAAA,eACiE;;;;IADNX,EAA3B,CAAAsC,UAAA,QAAA2D,SAAA,CAAAZ,YAAA,EAAArF,EAAA,CAAAsF,aAAA,CAA0B,QAAAW,SAAA,CAAAV,IAAA,CAAmB;;;;;IAE7EvF,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAW,SAAA,aAAyC;IACzCX,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAU,MAAA,yBAAG;IAC1BV,EAD0B,CAAAY,YAAA,EAAM,EAC1B;;;;;;IAjBRZ,EALF,CAAAC,cAAA,eAE4C,eAG0B;IAIlED,EAHA,CAAAwC,UAAA,IAAA0D,8DAAA,mBAAmG,IAAAC,8DAAA,mBAGtB;IAG7EnG,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAU,MAAA,GAAY;IAC3CV,EAD2C,CAAAY,YAAA,EAAQ,EAC7C;IAGNZ,EAAA,CAAAC,cAAA,eAAmG;IAGjGD,EAFA,CAAAwC,UAAA,IAAA4D,8DAAA,mBACiE,IAAAC,8DAAA,mBACE;IAIrErG,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAAmC,gBACsB;IAAAD,EAAA,CAAAU,MAAA,IAAgB;IACzEV,EADyE,CAAAY,YAAA,EAAM,EACzE;IAIJZ,EADF,CAAAC,cAAA,gBAAoD,mBACuD;IAA7DD,EAAA,CAAAE,UAAA,mBAAAoG,kFAAAtC,MAAA;MAAA,MAAAiC,SAAA,GAAAjG,EAAA,CAAAI,aAAA,CAAAmG,IAAA,EAAAvE,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAuF,yBAAA,GAAA9F,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyF,YAAA,CAAAE,SAAA,EAAAH,yBAAA,EAAA9B,MAAA,CAAkD;IAAA,EAAC;IACtGhE,EAAA,CAAAW,SAAA,cAA0B;IAC5BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,mBAAuF;IAAzCD,EAAA,CAAAE,UAAA,mBAAAsG,kFAAAxC,MAAA;MAAA,MAAAiC,SAAA,GAAAjG,EAAA,CAAAI,aAAA,CAAAmG,IAAA,EAAAvE,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmG,eAAA,CAAAR,SAAA,EAAAjC,MAAA,CAA8B;IAAA,EAAC;IACpFhE,EAAA,CAAAW,SAAA,cAAiC;IAGvCX,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;;;;IArCJZ,EAD0D,CAAA0G,WAAA,mBAAApG,MAAA,CAAAqG,YAAA,CAAAV,SAAA,EAA4C,gBAAA3F,MAAA,CAAAqG,YAAA,CAAAV,SAAA,EAC7D;IAIjCjG,EAAA,CAAAyB,SAAA,GAAyB;IAAzBzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAqG,YAAA,CAAAV,SAAA,EAAyB;IAGzBjG,EAAA,CAAAyB,SAAA,EAA0B;IAA1BzB,EAAA,CAAAsC,UAAA,UAAAhC,MAAA,CAAAqG,YAAA,CAAAV,SAAA,EAA0B;IAGHjG,EAAA,CAAAyB,SAAA,GAAY;IAAZzB,EAAA,CAAAqD,kBAAA,MAAAuD,KAAA,SAAY;IAKnC5G,EAAA,CAAAyB,SAAA,GAAwB;IAAxBzB,EAAA,CAAAsC,UAAA,SAAA2D,SAAA,CAAAZ,YAAA,CAAwB;IAExBrF,EAAA,CAAAyB,SAAA,EAAyB;IAAzBzB,EAAA,CAAAsC,UAAA,UAAA2D,SAAA,CAAAZ,YAAA,CAAyB;IAQGrF,EAAA,CAAAyB,SAAA,GAAoB;IAApBzB,EAAA,CAAAsC,UAAA,UAAA2D,SAAA,CAAAV,IAAA,CAAoB;IAACvF,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAA0B,iBAAA,CAAAuE,SAAA,CAAAV,IAAA,CAAgB;;;;;IAgB7EvF,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAW,SAAA,aAA0C;IAC1CX,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAU,MAAA,uDAAQ;IAAAV,EAAA,CAAAY,YAAA,EAAM;IACnBZ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,iDAAO;IACnCV,EADmC,CAAAY,YAAA,EAAM,EACnC;;;;;;IAKNZ,EADF,CAAAC,cAAA,eAA0G,yBAElB;IAApFD,EADc,CAAA8D,gBAAA,kCAAA+C,wGAAA7C,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAyG,yBAAA,EAAA/C,MAAA,MAAA1D,MAAA,CAAAyG,yBAAA,GAAA/C,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA8C,4BAAAgD,kGAAAhD,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAA2G,qBAAA,EAAAjD,MAAA,MAAA1D,MAAA,CAAA2G,qBAAA,GAAAjD,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAAqC,wBAAAkD,8FAAAlD,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAA6G,wBAAA,EAAAnD,MAAA,MAAA1D,MAAA,CAAA6G,wBAAA,GAAAnD,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAC9D;IAAChE,EAAA,CAAAE,UAAA,wBAAAgH,8FAAAlD,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAA8G,wBAAA,CAAApD,MAAA,CAAgC;IAAA,EAAC;IAEvFhE,EADE,CAAAY,YAAA,EAAiB,EACb;;;;IAHYZ,EAAA,CAAAyB,SAAA,EAA8C;IAC5DzB,EADc,CAAAiF,gBAAA,mBAAA3E,MAAA,CAAAyG,yBAAA,CAA8C,aAAAzG,MAAA,CAAA2G,qBAAA,CAAqC,SAAA3G,MAAA,CAAA6G,wBAAA,CAC9D;;;;;IASzCnH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAW,SAAA,aAAmC;IAACX,EAAA,CAAAU,MAAA,GACtC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;IAD+BZ,EAAA,CAAAyB,SAAA,GACtC;IADsCzB,EAAA,CAAAqD,kBAAA,MAAA/C,MAAA,CAAA+G,mBAAA,iCACtC;;;;;IACArH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAW,SAAA,aAAkC;IAACX,EAAA,CAAAU,MAAA,GACrC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;IAD8BZ,EAAA,CAAAyB,SAAA,GACrC;IADqCzB,EAAA,CAAAqD,kBAAA,MAAA/C,MAAA,CAAAgH,mBAAA,iCACrC;;;;;;IAvMJtH,EADF,CAAAC,cAAA,kBAAkD,qBAChC;IACdD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAiB;IAMbZ,EALJ,CAAAC,cAAA,uBAC2E,cAGQ,cAC3C;IAClCD,EAAA,CAAAW,SAAA,YAAqD;IAEnDX,EADF,CAAAC,cAAA,cAAmC,cACH;IAAAD,EAAA,CAAAU,MAAA,+BAAI;IAAAV,EAAA,CAAAY,YAAA,EAAM;IAEtCZ,EADF,CAAAC,cAAA,eAAkB,gBACU;IAAAD,EAAA,CAAAU,MAAA,kDAAO;IAAAV,EAAA,CAAAY,YAAA,EAAO;IACxCZ,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAU,MAAA,IAA6C;IAC/FV,EAD+F,CAAAY,YAAA,EAAO,EAChG;IACNZ,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAU,MAAA,kPAAuC;IAGlDV,EAHkD,CAAAY,YAAA,EAAM,EAC9C,EACF,EACF;IAKFZ,EAFJ,CAAAC,cAAA,eAA2C,eACvB,iBAC4C;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACxEZ,EAAA,CAAAC,cAAA,qBAAoG;IAAzFD,EAAA,CAAA8D,gBAAA,2BAAAyD,sFAAAvD,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAmH,gBAAA,EAAAzD,MAAA,MAAA1D,MAAA,CAAAmH,gBAAA,GAAAzD,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA8B;IAAChE,EAAA,CAAAE,UAAA,4BAAAwH,uFAAA1D,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAAqH,eAAA,CAAA3D,MAAA,CAAuB;IAAA,EAAC;IAClFhE,EAAA,CAAAwC,UAAA,KAAAoF,8DAAA,wBAAyE;IAI7E5H,EADE,CAAAY,YAAA,EAAY,EACR;IAEJZ,EADF,CAAAC,cAAA,eAAoB,iBAC0C;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACxEZ,EAAA,CAAAC,cAAA,iBACsC;IADiCD,EAAA,CAAA8D,gBAAA,2BAAA+D,kFAAA7D,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAwH,eAAA,EAAA9D,MAAA,MAAA1D,MAAA,CAAAwH,eAAA,GAAA9D,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA6B;IAClGhE,EAAA,CAAAE,UAAA,mBAAA6H,0EAAA;MAAA/H,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0H,qBAAA,EAAuB;IAAA,EAAC;IACrChI,EAFE,CAAAY,YAAA,EACsC,EAClC;IAEJZ,EADF,CAAAC,cAAA,eAAuC,kBAC+D;IAAtDD,EAAA,CAAAE,UAAA,mBAAA+H,2EAAA;MAAAjI,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA4H,mBAAA,EAAqB;MAAA,OAAAlI,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAA6H,kBAAA,EAAoB;IAAA,EAAC;IACjGnI,EAAA,CAAAU,MAAA,kCAAK;IAAAV,EAAA,CAAAW,SAAA,aAA8B;IAGzCX,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAQAZ,EALN,CAAAC,cAAA,eAAsD,eAG6B,eACX,cACrC;IAAAD,EAAA,CAAAU,MAAA,sCAAK;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACvCZ,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAU,MAAA,IACF;IACFV,EADE,CAAAY,YAAA,EAAM,EACF;IAGJZ,EADF,CAAAC,cAAA,gBAAsE,gBAChC;IAClCD,EAAA,CAAAwC,UAAA,KAAA4F,wDAAA,oBAEkC;IA2BpCpI,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAwC,UAAA,KAAA6F,wDAAA,mBAAkF;IAIpFrI,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,gBAAgD,0BAE0C;IAAtFD,EADc,CAAA8D,gBAAA,kCAAAwE,kGAAAtE,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAiI,0BAAA,EAAAvE,MAAA,MAAA1D,MAAA,CAAAiI,0BAAA,GAAAvE,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAA+C,4BAAAwE,4FAAAxE,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAmI,sBAAA,EAAAzE,MAAA,MAAA1D,MAAA,CAAAmI,sBAAA,GAAAzE,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAAsC,wBAAA0E,wFAAA1E,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkE,kBAAA,CAAA5D,MAAA,CAAAqI,yBAAA,EAAA3E,MAAA,MAAA1D,MAAA,CAAAqI,yBAAA,GAAA3E,MAAA;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;IAAA,EAC/D;IAAChE,EAAA,CAAAE,UAAA,wBAAAwI,wFAAA1E,MAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAsI,yBAAA,CAAA5E,MAAA,CAAiC;IAAA,EAAC;IAG3FhE,EAFI,CAAAY,YAAA,EAAiB,EACb,EACF;IAIJZ,EADF,CAAAC,cAAA,gBAAkF,mBAErB;IADZD,EAAA,CAAAE,UAAA,mBAAA2I,2EAAA;MAAA7I,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwI,iBAAA,EAAmB;IAAA,EAAC;IAE1E9I,EAAA,CAAAW,SAAA,cAAyC;IAC3CX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,mBAC0D;IADTD,EAAA,CAAAE,UAAA,mBAAA6I,2EAAA;MAAA/I,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0I,kBAAA,EAAoB;IAAA,EAAC;IAE7EhJ,EAAA,CAAAW,SAAA,cAAwC;IAC1CX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAW,SAAA,eAAiB;IACjBX,EAAA,CAAAC,cAAA,mBAC0D;IADZD,EAAA,CAAAE,UAAA,mBAAA+I,2EAAA;MAAAjJ,EAAA,CAAAI,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4I,iBAAA,EAAmB;IAAA,EAAC;IAE7ClJ,EAA5B,CAAAW,SAAA,cAA4B,UAAI;IAChCX,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAEbV,EAFa,CAAAY,YAAA,EAAQ,EACV,EACL;IAKFZ,EAFJ,CAAAC,cAAA,eAAiF,eACX,cACrC;IAAAD,EAAA,CAAAU,MAAA,sCAAK;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACvCZ,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAU,MAAA,IACF;IACFV,EADE,CAAAY,YAAA,EAAM,EACF;IAGJZ,EADF,CAAAC,cAAA,gBAAsE,gBAChC;IAClCD,EAAA,CAAAwC,UAAA,KAAA2G,wDAAA,qBAE4C;IAsC9CnJ,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAwC,UAAA,KAAA4G,wDAAA,mBAAiF;IAKnFpJ,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAwC,UAAA,KAAA6G,wDAAA,mBAA0G;IAOhHrJ,EAFI,CAAAY,YAAA,EAAM,EACF,EACO;IAGbZ,EADF,CAAAC,cAAA,2BAA0E,gBACrC;IAIjCD,EAHA,CAAAwC,UAAA,KAAA8G,yDAAA,oBAA6D,KAAAC,yDAAA,oBAGE;IAGjEvJ,EAAA,CAAAY,YAAA,EAAM;IAEJZ,EADF,CAAAC,cAAA,gBAA0B,mBACgD;IAAlCD,EAAA,CAAAE,UAAA,mBAAAsJ,2EAAA;MAAA,MAAAC,OAAA,GAAAzJ,EAAA,CAAAI,aAAA,CAAAoH,IAAA,EAAA3C,SAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoJ,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAACzJ,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAS;IACnFZ,EAAA,CAAAC,cAAA,mBACyC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAyJ,2EAAA;MAAA,MAAAF,OAAA,GAAAzJ,EAAA,CAAAI,aAAA,CAAAoH,IAAA,EAAA3C,SAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsJ,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IACtCzJ,EAAA,CAAAU,MAAA,IACF;IAGNV,EAHM,CAAAY,YAAA,EAAS,EACL,EACS,EACT;;;;IAhNNZ,EAAA,CAAAyB,SAAA,GACF;IADEzB,EAAA,CAAAqD,kBAAA,iCAAA/C,MAAA,CAAAuD,gBAAA,CAAAtB,WAAA,eAAAjC,MAAA,CAAAuD,gBAAA,CAAAtB,WAAA,yFACF;IAY0DvC,EAAA,CAAAyB,SAAA,IAA6C;IAA7CzB,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAuD,gBAAA,CAAAf,aAAA,yBAA6C;IAWtF9C,EAAA,CAAAyB,SAAA,GAA8B;IAA9BzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAmH,gBAAA,CAA8B;IACTzH,EAAA,CAAAyB,SAAA,EAAkB;IAAlBzB,EAAA,CAAAsC,UAAA,YAAAhC,MAAA,CAAAuJ,eAAA,CAAkB;IAOqB7J,EAAA,CAAAyB,SAAA,GAA6B;IAA7BzB,EAAA,CAAAiF,gBAAA,YAAA3E,MAAA,CAAAwH,eAAA,CAA6B;IAkBhG9H,EAAA,CAAAyB,SAAA,IACF;IADEzB,EAAA,CAAAqD,kBAAA,aAAA/C,MAAA,CAAAiI,0BAAA,yBACF;IAKyBvI,EAAA,CAAAyB,SAAA,GAAkB;IAAlBzB,EAAA,CAAAsC,UAAA,YAAAhC,MAAA,CAAAwJ,eAAA,CAAkB;IAgCrC9J,EAAA,CAAAyB,SAAA,EAAkC;IAAlCzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAwJ,eAAA,CAAAjI,MAAA,OAAkC;IAQxB7B,EAAA,CAAAyB,SAAA,GAA+C;IAC7DzB,EADc,CAAAiF,gBAAA,mBAAA3E,MAAA,CAAAiI,0BAAA,CAA+C,aAAAjI,MAAA,CAAAmI,sBAAA,CAAsC,SAAAnI,MAAA,CAAAqI,yBAAA,CAC/D;IAQtC3I,EAAA,CAAAyB,SAAA,GAAyC;IAAzCzB,EAAA,CAAAsC,UAAA,aAAAhC,MAAA,CAAAwJ,eAAA,CAAAjI,MAAA,OAAyC;IAIzC7B,EAAA,CAAAyB,SAAA,GAAwC;IAAxCzB,EAAA,CAAAsC,UAAA,aAAAhC,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,OAAwC;IAKxC7B,EAAA,CAAAyB,SAAA,GAAwC;IAAxCzB,EAAA,CAAAsC,UAAA,aAAAhC,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,OAAwC;IAWtC7B,EAAA,CAAAyB,SAAA,IACF;IADEzB,EAAA,CAAAgK,kBAAA,0BAAA1J,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,SAAAvB,MAAA,CAAAyG,yBAAA,yBACF;IAKyB/G,EAAA,CAAAyB,SAAA,GAAmB;IAAnBzB,EAAA,CAAAsC,UAAA,YAAAhC,MAAA,CAAAyJ,cAAA,CAAmB;IA2CtC/J,EAAA,CAAAyB,SAAA,EAAiC;IAAjCzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,OAAiC;IAQQ7B,EAAA,CAAAyB,SAAA,EAAuD;IAAvDzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAyG,yBAAA,GAAAzG,MAAA,CAAA2G,qBAAA,CAAuD;IAWnGjH,EAAA,CAAAyB,SAAA,GAA+B;IAA/BzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAA+G,mBAAA,OAA+B;IAG/BrH,EAAA,CAAAyB,SAAA,EAA+B;IAA/BzB,EAAA,CAAAsC,UAAA,SAAAhC,MAAA,CAAAgH,mBAAA,OAA+B;IAMCtH,EAAA,CAAAyB,SAAA,GAAwC;IAAxCzB,EAAA,CAAAsC,UAAA,aAAAhC,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,OAAwC;IAE7E7B,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqD,kBAAA,gCAAA/C,MAAA,CAAAyJ,cAAA,CAAAlI,MAAA,OACF;;;;;;IAgBF7B,EAFJ,CAAAC,cAAA,mBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAU,MAAA,qBACF;IACFV,EADE,CAAAY,YAAA,EAAO,EACQ;IAEfZ,EADF,CAAAC,cAAA,wBAAwC,eACX;IACzBD,EAAA,CAAAW,SAAA,eAAkD;IAEtDX,EADE,CAAAY,YAAA,EAAM,EACO;IAGXZ,EAFJ,CAAAC,cAAA,qBAAgB,eACgC,kBACc;IAAtBD,EAAA,CAAAE,UAAA,mBAAA+J,0EAAA;MAAA,MAAAC,OAAA,GAAAlK,EAAA,CAAAI,aAAA,CAAA+J,IAAA,EAAAtF,SAAA;MAAA,OAAA7E,EAAA,CAAAQ,WAAA,CAAS0J,OAAA,CAAAE,KAAA,EAAW;IAAA,EAAC;IAACpK,EAAA,CAAAU,MAAA,oBAAE;IAGlEV,EAHkE,CAAAY,YAAA,EAAS,EACjE,EACS,EACT;;;;IARkBZ,EAAA,CAAAyB,SAAA,GAA2B;IAA3BzB,EAAA,CAAAsC,UAAA,QAAAhC,MAAA,CAAA+J,mBAAA,EAAArK,EAAA,CAAAsF,aAAA,CAA2B;;;AD3YzD;AACA,IAAKgF,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAcpB,OAAM,MAAOC,yBAA0B,SAAQ1K,aAAa;EAkD1D;EACAyD,cAAcA,CAACkH,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACvF,aAAa,CAACwF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChH,KAAK,KAAK6G,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAAC7G,KAAK,GAAG,KAAK;EACtC;EAEAgH,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA/DzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACE5H,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAA4H,iBAAiB,GAAG,IAAI;IAC9B,KAAAjJ,WAAW,GAAW,EAAE;IACxB,KAAAO,aAAa,GAAW,EAAE;IAC1B,KAAA2I,SAAS,GAAY,KAAK;IAC1B,KAAApB,mBAAmB,GAAW,EAAE;IAChC,KAAAqB,aAAa,GAAY,KAAK;IAC9B,KAAA1I,UAAU,GAAY,IAAI;IAC1B;IACA,KAAA+G,cAAc,GAAgB,EAAE,EAAC;IACjC,KAAAjC,eAAe,GAAW,EAAE;IAE5B;IACA,KAAA+B,eAAe,GAAG,CAChB;MAAElG,KAAK,EAAE2G,eAAe,CAACqB,iBAAiB;MAAE/H,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAE2G,eAAe,CAACsB,SAAS;MAAEhI,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAA6D,gBAAgB,GAAoB6C,eAAe,CAACqB,iBAAiB;IACrE,KAAAE,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAAvB,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAApF,aAAa,GAAG,CAAC;MACfvB,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAESkI,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACd,iBAAiB,CAACe,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACd9I,OAAO,EAAE;;KAEZ,CAAC,CACC+I,IAAI,CACHzM,GAAG,CAAC0M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACf,cAAc,GAAGc,GAAG,CAACE,OAAO,EAAEzK,MAAM,GAAGuK,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D;QACA;MACF;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACjB;EAAEC,eAAeA,CAACC,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAACvB,gBAAgB,CAACwB,mCAAmC,CAAC;MAC/DT,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtCC,QAAQ,EAAE,IAAI,CAACrB,iBAAiB;QAChCjJ,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BO,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCgK,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEP,SAAS;QACpBzJ,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACmJ,IAAI,CACLzM,GAAG,CAAC0M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5I,YAAY,GAAG2I,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAACzJ,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAAC4J,SAAS,GAAG,IAAI,CAAChI,YAAY,CAAC,CAAC,CAAC,CAAC0J,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA;EACAC,0BAA0BA,CAACC,iBAAiD;IAC1E,IAAIA,iBAAiB,EAAE;MACrB,IAAI,CAACT,mBAAmB,GAAGS,iBAAiB,CAACC,GAAI;IACnD,CAAC,MAAM,IAAI,IAAI,CAAChC,cAAc,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC+K,mBAAmB,GAAG,IAAI,CAACtB,cAAc,CAAC,CAAC,CAAC,CAACgC,GAAI;IACxD;IACA,IAAI,CAACvM,MAAM,EAAE;EACf;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACyL,eAAe,EAAE,CAACD,SAAS,EAAE;EACpC;EAEAgB,WAAWA,CAACd,SAAiB;IAC3B,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACF,SAAS,EAAE;EAC7C;EAEA9L,sBAAsBA,CAAA;IACpB,IAAI,CAACyK,gBAAgB,CAACsC,2CAA2C,CAAC;MAChEvB,IAAI,EAAE,IAAI,CAACW;KACZ,CAAC,CAACL,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE;UACzB,IAAI,CAACtC,eAAe,CAACuC,iBAAiB,CAACtB,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACzC,gBAAgB,CAAC0C,+CAA+C,CAAC;MACpE3B,IAAI,EAAE,IAAI,CAACW;KACZ,CAAC,CAACL,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE;UACzB,IAAI,CAACtC,eAAe,CAACuC,iBAAiB,CAACtB,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACArM,MAAMA,CAACyM,GAAQ;IACb,IAAI,CAACxC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACxH,gBAAgB,GAAG;MACtBT,OAAO,EAAE,CAAC;MAAE;MACZF,MAAM,EAAE,CAAC,CAAG;KACb;IACD,IAAI,CAAC4H,aAAa,CAACgD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACA5L,kBAAkBA,CAAC8L,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAACxC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACxH,gBAAgB,GAAG;MAAE,GAAGkK;IAAI,CAAE;IACnC,IAAI,CAACjD,aAAa,CAACgD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACAxL,oBAAoBA,CAAC0L,IAA6B,EAAEF,GAAqB;IACvE,IAAI,CAAChK,gBAAgB,GAAG;MAAE,GAAGkK;IAAI,CAAE;IACnC;IACA,IAAI,CAAChE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACjC,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACgD,aAAa,CAACgD,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAACjD,KAAK,CAACkD,KAAK,EAAE;IAElB,IAAI,CAAClD,KAAK,CAACmD,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACtK,gBAAgB,CAACtB,WAAW,CAAC;IAClE;IACA,IAAI,CAACyI,KAAK,CAACmD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtK,gBAAgB,CAACf,aAAa,CAAC;IAClE,IAAI,CAACkI,KAAK,CAACmD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtK,gBAAgB,CAACT,OAAO,CAAC;IAC1D,IAAI,CAAC4H,KAAK,CAACmD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtK,gBAAgB,CAACX,MAAM,CAAC;IACzD,IAAI,CAAC8H,KAAK,CAACoD,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACvK,gBAAgB,CAACtB,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACyI,KAAK,CAACoD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACvK,gBAAgB,CAACf,aAAa,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,IAAI,CAACe,gBAAgB,CAACX,MAAM,KAAKmL,SAAS,IAAI,IAAI,CAACxK,gBAAgB,CAACX,MAAM,KAAK,IAAI,EAAE;MACvF,IAAI,IAAI,CAACW,gBAAgB,CAACX,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAAC8H,KAAK,CAACsD,aAAa,CAACC,IAAI,CAAC,YAAY,CAAC;MAC7C;IACF;EACF;EAEAvJ,QAAQA,CAAC6I,GAAQ;IACf,IAAI,CAACI,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjD,KAAK,CAACsD,aAAa,CAACzM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACkJ,OAAO,CAACyD,aAAa,CAAC,IAAI,CAACxD,KAAK,CAACsD,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAACpD,gBAAgB,CAACuD,qCAAqC,CAAC;MAC5DxC,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC;QACA9J,aAAa,EAAE,IAAI,CAACe,gBAAgB,CAACf,aAAa;QAClDP,WAAW,EAAE,IAAI,CAACsB,gBAAgB,CAACtB,WAAW;QAC9CU,YAAY,EAAE,IAAI,CAACY,gBAAgB,CAACZ,YAAY;QAChDyL,WAAW,EAAE,IAAI,CAACrD,KAAK,GAAG,IAAI,GAAG,IAAI,CAACxH,gBAAgB,CAAChB,GAAI;QAC3DK,MAAM,EAAE,IAAI,CAACW,gBAAgB,CAACX,MAAM;QACpCE,OAAO,EAAE,IAAI,CAACS,gBAAgB,CAACT,OAAO;QAAE;QACxCuL,UAAU,EAAG,IAAI,CAAC9K,gBAAwB,CAAC+K,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCzC,IAAI,CACHzM,GAAG,CAAC0M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtB,OAAO,CAAC8D,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC9D,OAAO,CAAC+D,YAAY,CAAC1C,GAAG,CAAC2C,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFtP,QAAQ,CAAC,MAAM,IAAI,CAAC+M,eAAe,EAAE,CAAC,EACtChN,QAAQ,CAAC,MAAMqO,GAAG,CAACzD,KAAK,EAAE,CAAC,CAC5B,CAACmC,SAAS,EAAE;EACjB;EAEAzH,OAAOA,CAAC+I,GAAQ;IACdA,GAAG,CAACzD,KAAK,EAAE;EACb;EAEA4E,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBhQ,IAAI,CAACiQ,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,MAAM/B,IAAI,GAAGpO,IAAI,CAACuQ,KAAK,CAACC,aAAa,CAACH,EAAE,CAAC;MACzC,IAAIjC,IAAI,IAAIA,IAAI,CAAClM,MAAM,GAAG,CAAC,EAAE;QAE3B,IAAI,CAACqJ,gBAAgB,CAACkF,2CAA2C,CAAC;UAChEnE,IAAI,EAAE;YACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;YACtCyD,KAAK,EAAEnB,MAAM,CAACI,KAAK,CAAC,CAAC;;SAExB,CAAC,CAACnD,IAAI,CACLzM,GAAG,CAAC0M,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACtB,OAAO,CAAC8D,aAAa,CAAC,MAAM,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAAC9D,OAAO,CAAC+D,YAAY,CAAC1C,GAAG,CAAC2C,OAAQ,CAAC;UACzC;QACF,CAAC,CAAC,EACFtP,QAAQ,CAAC,MAAM,IAAI,CAAC+M,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACD,SAAS,EAAE;MAEf,CAAC,MAAM;QACL,IAAI,CAACxB,OAAO,CAAC+D,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAACvL,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA2M,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAACnG,mBAAmB,GAAGkG,QAAQ;IACnC,IAAI,CAACzF,aAAa,CAACgD,IAAI,CAAC0C,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/E,aAAa,EAAE;MACtB,IAAI,CAAC1I,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwJ,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACvJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACwJ,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC;EACF;EACA;EACA9H,eAAeA,CAACoJ,GAAqB;IACnC;IACA,IAAI,CAAC9D,cAAc,GAAG,EAAE;IACxB,IAAI,CAACpB,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACxB,wBAAwB,GAAG,CAAC;IACjC,IAAI,CAACW,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACI,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAAC2C,aAAa,CAACgD,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAGA;EACA7F,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC0D,kBAAkB,IAAI,IAAI,CAACe,mBAAmB,IAAI,IAAI,CAAC/I,gBAAgB,EAAEhB,GAAG,EAAE;MACrF,IAAI,CAACuI,eAAe,CAACsF,iCAAiC,CAAC;QACrDzE,IAAI,EAAE;UACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;UACtC8B,WAAW,EAAE,IAAI,CAAC7K,gBAAgB,CAAChB,GAAG;UACtC8N,YAAY,EAAE,IAAI,CAAClJ,gBAAgB;UACnCuF,SAAS,EAAE,IAAI,CAAC7F,wBAAwB;UACxC2F,QAAQ,EAAE,IAAI,CAAC7F;;OAElB,CAAC,CAACsF,SAAS,CAAEH,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAACtC,cAAc,GAAGqC,GAAG,CAACE,OAAO,EAAEsE,GAAG,CAAEC,OAA+B,KAAM;YAC3EC,EAAE,EAAED,OAAO,CAAChO,GAAG,IAAI,CAAC;YACpB0C,IAAI,EAAEsL,OAAO,CAACE,YAAY,IAAI,EAAE;YAChCC,IAAI,EAAE,CAAC;YACP;YACA3L,YAAY,EAAEwL,OAAO,CAACR,KAAK,IAAI,EAAE;YACjCY,OAAO,EAAEJ,OAAO,CAACR,KAAK,IAAI,EAAE;YAC5Ba,YAAY,EAAEL,OAAO,CAACM,SAAS,GAAG,IAAIC,IAAI,CAACP,OAAO,CAACM,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,IAAI,EAAER,OAAO,CAACS,KAAK,CAAC;WACrB,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACvK,yBAAyB,GAAGqF,GAAG,CAACc,UAAU,IAAI,CAAC;UAEpD;UACA,IAAI,CAACqE,aAAa,GAAG,IAAI,CAACxH,cAAc,CAAC6G,GAAG,CAACY,GAAG,IAAIA,GAAG,CAACV,EAAE,CAAC;QAC7D,CAAC,MAAM;UACL,IAAI,CAAC/F,OAAO,CAAC+D,YAAY,CAAC1C,GAAG,CAAC2C,OAAO,IAAI,WAAW,CAAC;UACrD,IAAI,CAAChF,cAAc,GAAG,EAAE;UACxB,IAAI,CAAChD,yBAAyB,GAAG,CAAC;QACpC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACwK,aAAa,CAAC1P,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAAC4P,yBAAyB,EAAE;MAClC,CAAC,MAAM;QACL,IAAI,CAAC1H,cAAc,GAAG,EAAE;QACxB,IAAI,CAAChD,yBAAyB,GAAG,CAAC;MACpC;IACF;EACF;EAIA;EACA0K,yBAAyBA,CAAA;IACvB,IAAI,CAACrG,eAAe,CAACsF,iCAAiC,CAAC;MACrDzE,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC+D,YAAY,EAAE,IAAI,CAAClJ,gBAAgB;QACnCuF,SAAS,EAAE,CAAC;QACZF,QAAQ,EAAE,IAAI,CAAC;;KAElB,CAAC,CAACP,SAAS,CAAEH,GAA2C,IAAI;MAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,MAAMqF,kBAAkB,GAAGtF,GAAG,CAACE,OAAO,EAAEsE,GAAG,CAAEC,OAA+B,KAAM;UAChFC,EAAE,EAAED,OAAO,CAAChO,GAAG,IAAI,CAAC;UACpB0C,IAAI,EAAEsL,OAAO,CAACE,YAAY,IAAI,EAAE;UAChCC,IAAI,EAAE,CAAC;UACP;UACA3L,YAAY,EAAEwL,OAAO,CAACR,KAAK,IAAI,EAAE;UACjCY,OAAO,EAAEJ,OAAO,CAACR,KAAK,IAAI,EAAE;UAC5Ba,YAAY,EAAEL,OAAO,CAACM,SAAS,GAAG,IAAIC,IAAI,CAACP,OAAO,CAACM,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;UAC1EC,IAAI,EAAER,OAAO,CAACS,KAAK,CAAC;SACrB,CAAC,CAAC,IAAI,EAAE;QAET;QACA,MAAMK,WAAW,GAAGD,kBAAkB,CAACE,MAAM,CAACC,KAAK,IAAI,IAAI,CAACN,aAAa,CAACO,QAAQ,CAACD,KAAK,CAACf,EAAE,CAAC,CAAC;QAC7F,IAAI,CAAC/G,cAAc,GAAG,CAAC,GAAG4H,WAAW,CAAC;QACtC,IAAI,CAAC5K,yBAAyB,GAAG4K,WAAW,CAAC9P,MAAM;MACrD;IACF,CAAC,CAAC;EACJ;EAEA;EACAmG,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACW,yBAAyB,GAAG,CAAC,CAAC,CAAC;IACpC,IAAI,CAACT,mBAAmB,EAAE;EAC5B;EAEAxC,cAAcA,CAACmM,KAAgB,EAAE5C,KAAa;IAC5C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAAC8C,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAAClI,eAAe,CAACmI,SAAS,CAACT,GAAG,IAAIA,GAAG,CAACV,EAAE,KAAKe,KAAK,CAACf,EAAE,CAAC;IACxE,IAAIkB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjI,cAAc,CAACwE,IAAI,CAACsD,KAAK,CAAC;MAC/B,IAAI,CAAC9K,yBAAyB,GAAG,IAAI,CAACgD,cAAc,CAAClI,MAAM;MAC3D;MACA,IAAI,CAACqG,mBAAmB,EAAE;IAC5B;EACF;EAEAzB,eAAeA,CAACoL,KAAgB,EAAE5C,KAAa;IAC7C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAAC8C,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACjI,cAAc,CAACkI,SAAS,CAACT,GAAG,IAAIA,GAAG,CAACV,EAAE,KAAKe,KAAK,CAACf,EAAE,CAAC;IACvE,IAAIkB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjI,cAAc,CAACmI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACpC,IAAI,CAACjL,yBAAyB,GAAG,IAAI,CAACgD,cAAc,CAAClI,MAAM;MAC3D;MACA,IAAI,CAACqG,mBAAmB,EAAE;IAC5B;EACF;EAEAY,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACiB,cAAc,CAACwE,IAAI,CAAC,GAAG,IAAI,CAACzE,eAAe,CAAC;IACjD,IAAI,CAAC/C,yBAAyB,GAAG,IAAI,CAACgD,cAAc,CAAClI,MAAM;IAC3D;IACA,IAAI,CAACqG,mBAAmB,EAAE;EAC5B;EAEAc,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACe,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChD,yBAAyB,GAAG,CAAC;IAClC;IACA,IAAI,CAACmB,mBAAmB,EAAE;EAC5B;EAEAvB,YAAYA,CAACkL,KAAgB;IAC3B,OAAO,IAAI,CAACN,aAAa,CAACO,QAAQ,CAACD,KAAK,CAACf,EAAE,CAAC;EAC9C;EAEAzJ,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC0C,cAAc,CAAC6H,MAAM,CAACC,KAAK,IAAI,IAAI,CAAClL,YAAY,CAACkL,KAAK,CAAC,CAAC,CAAChQ,MAAM;EAC7E;EAEAyF,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACyC,cAAc,CAAC6H,MAAM,CAACC,KAAK,IAAI,CAAC,IAAI,CAAClL,YAAY,CAACkL,KAAK,CAAC,CAAC,CAAChQ,MAAM;EAC9E;EAEA;EACAqH,iBAAiBA,CAAA;IACf,IAAI,CAACa,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChD,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACmB,mBAAmB,EAAE;EAC5B;EAEA;EACAnC,YAAYA,CAAC8L,KAAgB,EAAEM,qBAA4C,EAAElD,KAAY;IACvFA,KAAK,CAAC8C,eAAe,EAAE;IAEvB;IACA,MAAMK,SAAS,GAAG,CAAC,GAAG,IAAI,CAACtI,eAAe,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC;IACnE,MAAMsI,UAAU,GAAGD,SAAS,CAACH,SAAS,CAAET,GAAc,IAAKA,GAAG,CAACV,EAAE,KAAKe,KAAK,CAACf,EAAE,CAAC;IAE/E;IACAqB,qBAAqB,CAACG,WAAW,GAAG,IAAI,CAAC1F,mBAAmB;IAC5DuF,qBAAqB,CAACI,UAAU,GAAG,IAAI,CAAC1O,gBAAgB,EAAEhB,GAAG;IAC7DsP,qBAAqB,CAACK,WAAW,GAAG,IAAI,CAAC/K,gBAAgB;IACzD0K,qBAAqB,CAACM,UAAU,GAAG,IAAI,CAAC3K,eAAe;IACvDqK,qBAAqB,CAACpI,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1DoI,qBAAqB,CAACO,iBAAiB,GAAGL,UAAU;IACpDF,qBAAqB,CAACQ,mBAAmB,GAAG,IAAI;IAEhD;IACAR,qBAAqB,CAACS,WAAW,EAAE;EACrC;EAEA;EACAC,sBAAsBA,CAAChB,KAAgB;IACrC,MAAMiB,UAAU,GAAG,IAAI,CAAC/I,cAAc,CAACgJ,IAAI,CAACvB,GAAG,IAAIA,GAAG,CAACV,EAAE,KAAKe,KAAK,CAACf,EAAE,CAAC;IACvE,IAAIgC,UAAU,EAAE;MACd,IAAI,CAACrM,eAAe,CAACoL,KAAK,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACnM,cAAc,CAACmM,KAAK,CAAC;IAC5B;EACF;EACAjI,uBAAuBA,CAACiE,GAAQ;IAC9B,IAAI,IAAI,CAAC9D,cAAc,CAAClI,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,MAAM+M,gBAAgB,GAAG,IAAI,CAAC7E,cAAc,CAAC6G,GAAG,CAACY,GAAG,IAAIA,GAAG,CAACV,EAAE,CAAC,CAAC,CAAM;MAGtE;MACC,IAAI,CAACjN,gBAAwB,CAAC+K,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAAC/K,gBAAgB,CAAChB,GAAG,EAAE;QAC7B,IAAI,CAACmQ,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAAC9J,iBAAiB,EAAE;IACxB2E,GAAG,CAACzD,KAAK,EAAE;EACb,CAAC,CAAE;EACH4I,gBAAgBA,CAAA;IACd,IAAI,CAAC9H,gBAAgB,CAACuD,qCAAqC,CAAC;MAC1DxC,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC9J,aAAa,EAAE,IAAI,CAACe,gBAAgB,CAACf,aAAa;QAClDP,WAAW,EAAE,IAAI,CAACsB,gBAAgB,CAACtB,WAAW;QAC9CU,YAAY,EAAE,IAAI,CAACY,gBAAgB,CAACZ,YAAY;QAChDyL,WAAW,EAAE,IAAI,CAAC7K,gBAAgB,CAAChB,GAAI;QACvCK,MAAM,EAAE,IAAI,CAACW,gBAAgB,CAACX,MAAM;QACpCE,OAAO,EAAE,IAAI,CAACS,gBAAgB,CAACT,OAAO;QAAE;QACxCuL,UAAU,EAAG,IAAI,CAAC9K,gBAAwB,CAAC+K,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACzC,IAAI,CACLzM,GAAG,CAAC0M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtB,OAAO,CAAC8D,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAAC9D,OAAO,CAAC+D,YAAY,CAAC1C,GAAG,CAAC2C,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFtP,QAAQ,CAAC,MAAM,IAAI,CAAC+M,eAAe,EAAE,CAAC,EACtChN,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACqE,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC0I,SAAS,EAAE;EACf;EACA7C,kBAAkBA,CAACmE,GAAQ;IACzB;IACA,IAAI,CAAC9D,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChD,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACe,eAAe,GAAG,EAAE;IACzB,IAAI,CAACa,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACxB,wBAAwB,GAAG,CAAC;IACjC0G,GAAG,CAACzD,KAAK,EAAE;EACb;EAEA;EACAzC,eAAeA,CAACsL,QAAyB;IACvC,IAAI,CAACxL,gBAAgB,GAAGwL,QAAQ;IAChC,IAAI,CAACpH,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAAClD,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACxB,wBAAwB,GAAG,CAAC;IACjC,IAAI,IAAI,CAACyF,mBAAmB,EAAE;MAC5B,IAAI,CAAC1E,mBAAmB,EAAE;MAC1B,IAAI,CAACC,kBAAkB,EAAE;IAC3B;EACF;EAEA;EACA+K,gBAAgBA,CAACD,QAAgB;IAC/B,MAAMxI,MAAM,GAAG,IAAI,CAACZ,eAAe,CAACa,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChH,KAAK,KAAKsP,QAAQ,CAAC;IACvE,OAAOxI,MAAM,GAAGA,MAAM,CAAC7G,KAAK,GAAG,MAAM;EACvC;EAEA;EACAgF,yBAAyBA,CAACuK,IAAY;IACpC,IAAI,CAACxK,yBAAyB,GAAGwK,IAAI;IACrC,IAAI,CAACjL,mBAAmB,EAAE;EAC5B;EAEA;EACAd,wBAAwBA,CAAC+L,IAAY;IACnC,IAAI,CAAChM,wBAAwB,GAAGgM,IAAI;IACpC,IAAI,CAAChL,kBAAkB,EAAE;EAC3B;;;uCA9iBWoC,yBAAyB,EAAAvK,EAAA,CAAAoT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtT,EAAA,CAAAoT,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxT,EAAA,CAAAoT,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1T,EAAA,CAAAoT,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA5T,EAAA,CAAAoT,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA9T,EAAA,CAAAoT,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAA/T,EAAA,CAAAoT,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAjU,EAAA,CAAAoT,iBAAA,CAAAS,EAAA,CAAAK,cAAA;IAAA;EAAA;;;YAAzB3J,yBAAyB;MAAA4J,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArU,EAAA,CAAAsU,0BAAA,EAAAtU,EAAA,CAAAuU,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC9BpC7U,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAW,SAAA,qBAAiC;UACnCX,EAAA,CAAAY,YAAA,EAAiB;UAEfZ,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAU,MAAA,yJACtC;UAAAV,EAAA,CAAAY,YAAA,EAAK;UAICZ,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACF;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UAE7DZ,EADF,CAAAC,cAAA,eAAqB,iCAKF;UAHfD,EAAA,CAAA8D,gBAAA,iCAAAiR,yFAAA/Q,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAAlI,mBAAA,EAAA5I,MAAA,MAAA8Q,GAAA,CAAAlI,mBAAA,GAAA5I,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAAuC;UACvChE,EAAA,CAAAE,UAAA,6BAAA+U,qFAAAjR,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAAmBsU,GAAA,CAAA1H,0BAAA,CAAApJ,MAAA,CAAkC;UAAA,EAAC;UAM9DhE,EAHM,CAAAY,YAAA,EAAwB,EACpB,EACF,EACF;UAaFZ,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAU,MAAA,6CAAO;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UACpEZ,EAAA,CAAAC,cAAA,iBAAwG;UAAxDD,EAAA,CAAA8D,gBAAA,2BAAAoR,mEAAAlR,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAAvS,WAAA,EAAAyB,MAAA,MAAA8Q,GAAA,CAAAvS,WAAA,GAAAyB,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAAyB;UAE7EhE,EAFI,CAAAY,YAAA,EAAwG,EACpG,EACF;UAIFZ,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UACjEZ,EAAA,CAAAC,cAAA,iBAAwG;UAA1DD,EAAA,CAAA8D,gBAAA,2BAAAqR,mEAAAnR,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAAhS,aAAA,EAAAkB,MAAA,MAAA8Q,GAAA,CAAAhS,aAAA,GAAAkB,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAA2B;UAE7EhE,EAFI,CAAAY,YAAA,EAAwG,EACpG,EACF;UAGFZ,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAA8D,gBAAA,2BAAAsR,yEAAApR,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAApJ,aAAA,EAAA1H,MAAA,MAAA8Q,GAAA,CAAApJ,aAAA,GAAA1H,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAA2B;UACpFhE,EAAA,CAAAE,UAAA,oBAAAmV,kEAAA;YAAArV,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAAUsU,GAAA,CAAArE,YAAA,EAAc;UAAA,EAAC;UACzBzQ,EAAA,CAAAU,MAAA,gHACF;UAAAV,EAAA,CAAAY,YAAA,EAAc;UAOdZ,EANA,CAAAwC,UAAA,KAAA8S,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpFzV,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAE,UAAA,oBAAAwV,4DAAA1R,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAAUsU,GAAA,CAAA9F,eAAA,CAAAhL,MAAA,CAAuB;UAAA,EAAC;UAApGhE,EAAA,CAAAY,YAAA,EAAqG;UACrGZ,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAE,UAAA,mBAAAyV,4DAAA;YAAA3V,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAASsU,GAAA,CAAAnH,0BAAA,EAA4B;UAAA,EAAC;UAAC3N,EAAA,CAAAU,MAAA,6CAAO;UAAAV,EAAA,CAAAW,SAAA,aAC9C;UAG3CX,EAH2C,CAAAY,YAAA,EAAS,EAC1C,EACF,EACF;UAKEZ,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,sCAAK;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACxCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAEpCV,EAFoC,CAAAY,YAAA,EAAK,EAClC,EACC;UACRZ,EAAA,CAAAwC,UAAA,KAAAoT,2CAAA,oBAA+D;UAiCrE5V,EAFI,CAAAY,YAAA,EAAQ,EACJ,EACO;UAEbZ,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA8D,gBAAA,kCAAA+R,mFAAA7R,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAA7H,YAAA,EAAAjJ,MAAA,MAAA8Q,GAAA,CAAA7H,YAAA,GAAAjJ,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAAiC,4BAAA8R,6EAAA9R,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAA/H,QAAA,EAAA/I,MAAA,MAAA8Q,GAAA,CAAA/H,QAAA,GAAA/I,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAAwB,wBAAA+R,yEAAA/R,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAAhV,EAAA,CAAAkE,kBAAA,CAAA4Q,GAAA,CAAArI,SAAA,EAAAzI,MAAA,MAAA8Q,GAAA,CAAArI,SAAA,GAAAzI,MAAA;YAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAwD,MAAA;UAAA,EAAqB;UAC5FhE,EAAA,CAAAE,UAAA,wBAAA6V,yEAAA/R,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAAcsU,GAAA,CAAAvH,WAAA,CAAAvJ,MAAA,CAAmB;UAAA,EAAC;UAGxChE,EAFI,CAAAY,YAAA,EAAiB,EACF,EACT;UAiEVZ,EA/DA,CAAAwC,UAAA,KAAAwT,iDAAA,iCAAAhW,EAAA,CAAAiW,sBAAA,CAAoD,KAAAC,iDAAA,kCAAAlW,EAAA,CAAAiW,sBAAA,CA+DK;UAuNzDjW,EAAA,CAAAC,cAAA,gCAE0D;UAAxDD,EAAA,CAAAE,UAAA,kCAAAiW,sFAAAnS,MAAA;YAAAhE,EAAA,CAAAI,aAAA,CAAA4U,GAAA;YAAA,OAAAhV,EAAA,CAAAQ,WAAA,CAAwBsU,GAAA,CAAAjC,sBAAA,CAAA7O,MAAA,CAA8B;UAAA,EAAC;UACzDhE,EAAA,CAAAY,YAAA,EAAoB;UAGpBZ,EAAA,CAAAwC,UAAA,KAAA4T,iDAAA,iCAAApW,EAAA,CAAAiW,sBAAA,CAAuD;;;UArYzCjW,EAAA,CAAAyB,SAAA,IAAuC;UAAvCzB,EAAA,CAAAiF,gBAAA,kBAAA6P,GAAA,CAAAlI,mBAAA,CAAuC;UAqBK5M,EAAA,CAAAyB,SAAA,GAAyB;UAAzBzB,EAAA,CAAAiF,gBAAA,YAAA6P,GAAA,CAAAvS,WAAA,CAAyB;UAO3BvC,EAAA,CAAAyB,SAAA,GAA2B;UAA3BzB,EAAA,CAAAiF,gBAAA,YAAA6P,GAAA,CAAAhS,aAAA,CAA2B;UAKd9C,EAAA,CAAAyB,SAAA,GAA2B;UAA3BzB,EAAA,CAAAiF,gBAAA,YAAA6P,GAAA,CAAApJ,aAAA,CAA2B;UAI7E1L,EAAA,CAAAyB,SAAA,GAAmB;UAAnBzB,EAAA,CAAAsC,UAAA,SAAAwS,GAAA,CAAAuB,aAAA,CAAmB;UAEnBrW,EAAA,CAAAyB,SAAA,EAAY;UAAZzB,EAAA,CAAAsC,UAAA,SAAAwS,GAAA,CAAAvR,MAAA,CAAY;UAEZvD,EAAA,CAAAyB,SAAA,EAAc;UAAdzB,EAAA,CAAAsC,UAAA,SAAAwS,GAAA,CAAAwB,QAAA,CAAc;UAEYtW,EAAA,CAAAyB,SAAA,EAAmB;UAAnBzB,EAAA,CAAAsC,UAAA,SAAAwS,GAAA,CAAAyB,aAAA,CAAmB;UAqBhDvW,EAAA,CAAAyB,SAAA,IAAqD;UAArDzB,EAAA,CAAAsC,UAAA,SAAAwS,GAAA,CAAArR,YAAA,YAAAqR,GAAA,CAAArR,YAAA,CAAA5B,MAAA,KAAqD;UAmCjD7B,EAAA,CAAAyB,SAAA,GAAiC;UAAyBzB,EAA1D,CAAAiF,gBAAA,mBAAA6P,GAAA,CAAA7H,YAAA,CAAiC,aAAA6H,GAAA,CAAA/H,QAAA,CAAwB,SAAA+H,GAAA,CAAArI,SAAA,CAAqB;;;qBDpFtFlN,YAAY,EAAAiX,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9W,YAAY,EAAA+W,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,YAAA,EAAAN,EAAA,CAAAO,OAAA,EAAA3D,EAAA,CAAA4D,eAAA,EAAA5D,EAAA,CAAA6D,mBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,qBAAA,EAAA/D,EAAA,CAAAgE,mBAAA,EAAAhE,EAAA,CAAAiE,gBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEhY,wBAAwB,EAAEC,qBAAqB;MAAAgY,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}