import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';
import { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';
import { finalize, mergeMap, tap } from 'rxjs';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { UtilityService } from 'src/app/shared/services/utility.service';
import * as XLSX from 'xlsx';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';
import { ImagePreviewComponent, ImageItem } from '../../../shared/components/image-preview';

// 圖片類別枚舉
enum PictureCategory {
  NONE = 0,           // 未選擇
  BUILDING_MATERIAL = 1,  // 建材圖片
  SCHEMATIC = 2       // 示意圖片
}

@Component({
  selector: 'ngx-building-material',
  templateUrl: './building-material.component.html',
  styleUrls: ['./building-material.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent],
})

export class BuildingMaterialComponent extends BaseComponent implements OnInit {
  @ViewChild('imagePreviewComponent', { static: false }) imagePreviewComponent!: ImagePreviewComponent;
  
  isNew = true

  materialList: GetMaterialListResponse[]
  selectedMaterial: GetMaterialListResponse

  listBuildCases: BuildCaseGetListReponse[] = []
  selectedBuildCaseId: number

  materialOptions = [
    {
      value: null,
      label: '全部',
    },
    {
      value: false,
      label: '方案',
    },
    {
      value: true,
      label: '選樣',
    }]; materialOptionsId = null;
  CSelectName: string = ""
  CMaterialCode: string = ""
  ShowPrice: boolean = false
  filterMapping: boolean = false
  CIsMapping: boolean = true
  // 圖片綁定相關屬性 - 簡化後只保留必要的
  selectedImages: ImageItem[] = [] // 右側已選擇的圖片
  imageSearchTerm: string = ""

  // 類別選項
  categoryOptions = [
    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },
    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }
  ]
  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL
  isCategorySelected: boolean = true // 預設選擇建材圖片
  // 讓模板可以使用 enum
  PictureCategory = PictureCategory;

  // 狀態選項
  statusOptions = [{
    value: 1, //0停用 1啟用 9刪除
    label: '啟用' //enable
  }, {
    value: 2,
    label: '停用' //Disable
  }];
  // 根據狀態值獲取狀態標籤
  getStatusLabel(status: number): string {
    const option = this.statusOptions.find(opt => opt.value === status);
    return option ? option.label : '未設定';
  }

  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _buildCaseService: BuildCaseService,
    private _materialService: MaterialService,
    private _utilityService: UtilityService,
    private _pictureService: PictureService
  ) {
    super(_allow)
  }

  override ngOnInit(): void {
    this.getListBuildCase()
  }

  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({
      body: {
        CIsPagi: false,
        CStatus: 1,
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.listBuildCases = res.Entries?.length ? res.Entries : []
            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇
            // 不立即載入材料列表，等待建案選擇事件觸發
          }
        })
      ).subscribe()
  } getMaterialList(pageIndex: number = 1) {
    return this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CPlanUse: this.materialOptionsId,
        CSelectName: this.CSelectName,
        CMaterialCode: this.CMaterialCode,
        PageSize: this.pageSize,
        PageIndex: pageIndex,
        CIsMapping: this.CIsMapping
      }
    }).pipe(
      tap(res => {
        if (res.StatusCode == 0) {
          this.materialList = res.Entries! ?? []
          this.totalRecords = res.TotalItems!

          if (this.materialList.length > 0) {
            this.ShowPrice = this.materialList[0].CShowPrice!;
          }
        }
      })
    )
  }

  // 建案選擇事件處理（新）
  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {
    if (selectedBuildCase) {
      this.selectedBuildCaseId = selectedBuildCase.cID!;
    } else if (this.listBuildCases.length > 0) {
      this.selectedBuildCaseId = this.listBuildCases[0].cID!;
    }
    this.search();
  }

  search() {
    this.getMaterialList().subscribe()
  }

  pageChanged(pageIndex: number) {
    this.getMaterialList(pageIndex).subscribe()
  }

  exportExelMaterialList() {
    this._materialService.apiMaterialExportExcelMaterialListPost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }

  exportExelMaterialTemplate() {
    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }
  addNew(ref: any) {
    this.isNew = true
    this.selectedMaterial = {
      CStatus: 1, // 預設為啟用狀態
      CPrice: 0   // 預設價格為0
    }
    this.dialogService.open(ref)
  }
  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {
    this.isNew = false
    this.selectedMaterial = { ...data }
    this.dialogService.open(ref)
  }
  bindImageForMaterial(data: GetMaterialListResponse) {
    this.selectedMaterial = { ...data }
    // 重置選擇狀態
    this.selectedImages = []
    this.imageSearchTerm = ""

    // 使用 imagePreviewComponent 的綁定界面
    if (this.imagePreviewComponent) {
      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;
      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;
      this.imagePreviewComponent.pictureType = this.selectedCategory;
      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;
      
      // 監聽圖片綁定確認事件
      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {
        this.onConfirmImageBinding(selectedImages);
      });
      
      this.imagePreviewComponent.openBindingInterface();
    }
  } validation() {
    this.valid.clear();

    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)
    // 啟用建材代號驗證
    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)
    this.valid.required('[狀態]', this.selectedMaterial.CStatus)
    this.valid.required('[價格]', this.selectedMaterial.CPrice)
    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)
    // 啟用建材代號長度驗證
    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)
    // 價格驗證：必須為數字且大於等於0
    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {
      if (this.selectedMaterial.CPrice < 0) {
        this.valid.errorMessages.push('[價格] 不能小於0')
      }
    }
  }

  onSubmit(ref: any) {
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        // 暫時保留 CImageCode 給圖片綁定功能使用
        CMaterialCode: this.selectedMaterial.CMaterialCode,
        CSelectName: this.selectedMaterial.CSelectName,
        CDescription: this.selectedMaterial.CDescription,
        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,
        CPrice: this.selectedMaterial.CPrice,
        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位
        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode === 0) {
            this.message.showSucessMSG("執行成功");
          } else {
            this.message.showErrorMSG(res.Message!);
          }
        }),
        mergeMap(() => this.getMaterialList()),
        finalize(() => ref.close())
      ).subscribe()
  }

  onClose(ref: any) {
    ref.close();
  }

  detectFileExcel(event: any) {
    const target: DataTransfer = <DataTransfer>(event.target);
    const reader: FileReader = new FileReader();
    reader.readAsBinaryString(target.files[0]);
    reader.onload = (e: any) => {
      const binarystr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });

      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      const data = XLSX.utils.sheet_to_json(ws);
      if (data && data.length > 0) {

        this._materialService.apiMaterialImportExcelMaterialListPost$Json({
          body: {
            CBuildCaseId: this.selectedBuildCaseId,
            CFile: target.files[0]
          }
        }).pipe(
          tap(res => {
            if (res.StatusCode == 0) {
              this.message.showSucessMSG("執行成功")
            } else {
              this.message.showErrorMSG(res.Message!)
            }
          }),
          mergeMap(() => this.getMaterialList(1))
        ).subscribe();

      } else {
        this.message.showErrorMSG("匯入的檔案內容為空，請檢查檔案並重新上傳。")
      }
      event.target.value = null;
    };
  }

  changeFilter() {
    if (this.filterMapping) {
      this.CIsMapping = false;
      this.getMaterialList().subscribe();
    }
    else {
      this.CIsMapping = true;
      this.getMaterialList().subscribe();
    }
  }
  // 圖片綁定功能方法
  openImageBinder() {
    // 重置選擇狀態
    this.selectedImages = []
    this.imageSearchTerm = ""

    // 使用 imagePreviewComponent 的綁定界面
    if (this.imagePreviewComponent) {
      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;
      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;
      this.imagePreviewComponent.pictureType = this.selectedCategory;
      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;
      
      // 監聽圖片綁定確認事件
      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {
        this.onConfirmImageBinding(selectedImages);
      });
      
      this.imagePreviewComponent.openBindingInterface();
    }
  }

  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent
  previewImage(image: ImageItem, imagePreviewComponent: ImagePreviewComponent, event: Event) {
    event.stopPropagation();

    // 設定預覽元件參數，讓它自行載入圖片
    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;
    imagePreviewComponent.materialId = this.selectedMaterial?.CId;
    imagePreviewComponent.pictureType = this.selectedCategory;
    imagePreviewComponent.searchTerm = this.imageSearchTerm;
    imagePreviewComponent.showSelectionToggle = true;

    // 開啟預覽對話框
    imagePreviewComponent.openPreview();
  }

  onConfirmImageBinding(selectedImages: ImageItem[]) {
    if (selectedImages.length > 0) {
      // 收集選中圖片的 ID
      const selectedImageIds = selectedImages.map(img => img.id);

      // 暫存所有選中的圖片 ID，供 API 呼叫使用
      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;

      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫
      if (this.selectedMaterial.CId) {
        this.saveImageBinding();
      }
    }

    // 清理選擇狀態
    this.selectedImages = [];
  }  // 新增方法：保存圖片綁定
  saveImageBinding() {
    this._materialService.apiMaterialSaveMaterialAdminPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CMaterialCode: this.selectedMaterial.CMaterialCode,
        CSelectName: this.selectedMaterial.CSelectName,
        CDescription: this.selectedMaterial.CDescription,
        CMaterialId: this.selectedMaterial.CId!,
        CPrice: this.selectedMaterial.CPrice,
        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位
        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列
      }
    }).pipe(
      tap(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG(`圖片綁定成功`);
        } else {
          this.message.showErrorMSG(res.Message!);
        }
      }),
      mergeMap(() => this.getMaterialList()),
      finalize(() => {
        // 清空選取的建材
        this.selectedMaterial = {};
      })
    ).subscribe()
  }

  // 類別變更處理方法
  categoryChanged(category: PictureCategory) {
    this.selectedCategory = category;
    this.isCategorySelected = true;
  }

  // 獲取類別標籤的方法
  getCategoryLabel(category: number): string {
    const option = this.categoryOptions.find(opt => opt.value === category);
    return option ? option.label : '未知類別';
  }

}
