<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">可設定上傳建材示意圖片，上傳前請將圖片檔案改為建材圖片檔名。</h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-4">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2">建案</label>
          <app-build-case-select 
            [(selectedValue)]="selectedBuildCaseId"
            (selectionChange)="onBuildCaseSelectionChange($event)"
            placeholder="請選擇建案"
            class="w-full">
          </app-build-case-select>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group d-flex align-items-center w-full">
          <label for="category" class="label mr-2">類別</label>
          <nb-select placeholder="圖片類別" [(ngModel)]="selectedCategory" (selectedChange)="categoryChanged($event)"
            class="w-full">
            <nb-option *ngFor="let option of categoryOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="col-md-4">
        <div class="d-flex justify-content-end w-full">
          <button class="btn btn-danger mr-2" (click)="batchDelete()" [disabled]="selectedCount === 0"
            *ngIf="isRead && images.length > 0">
            批次刪除 ({{selectedCount}})
          </button>
          <button class="btn btn-info" (click)="addNew(dialog)" *ngIf="isCreate">
            圖片上傳</button>
        </div>
      </div>
    </div>

    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="min-width: 1000px; background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <th scope="col" class="col-1" *ngIf="isRead && images.length > 0">
              <nb-checkbox [ngModel]="selectAll" (ngModelChange)="toggleSelectAll($event)">
                全選
              </nb-checkbox>
            </th>
            <th scope="col" class="col-1">項次</th>
            <th scope="col" class="col-1">名稱</th>
            <th scope="col" class="col-1">類別</th>
            <th scope="col" class="col-1">最新圖片上傳時間</th>
            <th scope="col" class="col-1"></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of images ; let i = index">
            <td *ngIf="isRead && images.length > 0">
              <nb-checkbox [ngModel]="isItemSelected(item.CId!)" (ngModelChange)="toggleSelectItem(item.CId!)">
              </nb-checkbox>
            </td>
            <td>{{ item.CId}}</td>
            <td>
              <!-- <a class="cursor-pointer text-blue-500" (click)="openPdfInNewTab(item?.CFile)">{{ item?.CName}}</a> -->
              {{ item?.CPictureCode}}
            </td>
            <td>{{ getCategoryLabel(selectedCategory) }}</td>
            <td>{{ item?.CUpdateDT | date: "yyyy/MM/dd HH:mm:ss"}}</td>
            <td class="w-32">
              <button class="btn btn-outline-primary btn-sm m-1" *ngIf="isRead"
                (click)="addNew(dialog, item)">預覽</button>
              <button class="btn btn-outline-primary btn-sm m-1" *ngIf="isRead"
                (click)="changePicture(dialog, item)">改變圖片</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(CollectionSize)]="totalRecords" [(PageSize)]="pageSize" [(Page)]="pageIndex"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<ng-template #dialog let-data let-ref="dialogRef"> <nb-card style="height: 100%; overflow: auto; width: 700px;"
    class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>
        {{isPreviewMode ? '圖片預覽' : (selectedCategory === PictureCategory.BUILDING_MATERIAL ? '建材圖片' : '示意圖片') + '上傳'}}
      </span>
      <span *ngIf="isPreviewMode && currentPreviewImages.length > 1" class="ml-2 text-muted">
        ({{imageCounter}})
      </span>
    </nb-card-header> <nb-card-body style="padding:1rem 2rem">
      <div class="w-full h-auto" *ngIf="isPreviewMode; else upload">
        <!-- 輪播預覽區域 -->
        <div class="preview-container position-relative" style="min-height: 400px;"> <!-- 圖片顯示區 -->
          <div class="text-center mb-3">
            <img class="fit-size" [src]="currentImageShowing | base64Image "
              style="max-height: 400px; max-width: 100%; object-fit: contain;">
          </div>

          <!-- 圖片信息 -->
          <div class="text-center mb-3" *ngIf="currentImageInfo">
            <p class="mb-1"><strong>圖片名稱:</strong> {{currentImageInfo.CPictureCode}}</p>
            <p class="mb-1"><strong>更新時間:</strong> {{currentImageInfo.CUpdateDT | date: "yyyy/MM/dd HH:mm:ss"}}</p>
          </div>

          <!-- 輪播控制按鈕 -->
          <div class="carousel-controls d-flex justify-content-between align-items-center"
            *ngIf="currentPreviewImages.length > 1">
            <button class="btn btn-outline-primary btn-sm" (click)="previousImage()">
              <i class="nb-chevron-left"></i> 上一張
            </button>

            <span class="text-muted">{{imageCounter}}</span>

            <button class="btn btn-outline-primary btn-sm" (click)="nextImage()">
              下一張 <i class="nb-chevron-right"></i>
            </button>
          </div>
          <!-- 縮圖導航 -->
          <div class="thumbnail-nav mt-3" *ngIf="currentPreviewImages.length > 1">
            <div class="d-flex flex-wrap justify-content-center">
              <div *ngFor="let img of currentPreviewImages; let i = index" class="thumbnail-item m-1"
                [class.active]="i === currentImageIndex" (click)="goToImage(i)"
                style="cursor: pointer; border: 2px solid transparent; padding: 2px;">
                <!-- TODO: 可使用 CGuid 透過 apiPictureGetPictureGuidGet 取得完整圖片資料 -->
                <img [src]="img.CFile | base64Image "
                  style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
              </div>
            </div>
          </div>
        </div>
      </div>

      <ng-template #upload>
        <div class="mb-3">
          <h6>上傳方式：</h6>
          <button
            [class]="!isEdit ? 'btn btn-info mr-2' : listPictures.length < 1 ? 'btn btn-info mr-2' : 'btn btn-info disable mr-2'"
            (click)="inputFile.click()">單張圖片上傳</button>
          <button [class]="!isEdit ? 'btn btn-success' : 'btn btn-success disable'" (click)="zipInputFile.click()"
            [disabled]="isEdit">ZIP 批量匯入</button>
        </div>
        <input #inputFile type="file" class="hidden" (change)="detectFiles($event)"
          accept="image/png, image/gif, image/jpeg" multiple>
        <input #zipInputFile type="file" class="hidden" (change)="detectFiles($event)" accept=".zip,application/zip">
        <div class="mt-3 w-full flex flex-col">
          <table class="table table-striped border" style="background-color:#f3f3f3;">
            <thead>
              <tr style="background-color: #27ae60; color: white;">
                <th scope="col" class="col-4">文件名</th>
                <th scope="col" class="col-1">檢視</th>
                <th scope="col" class="col-1"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let picture of listPictures; let i = index">
                <td>
                  <input nbInput class="w-100 p-2 text-[13px]" type="text" [value]="picture.name"
                    (blur)="renameFile($event, i)">
                </td>
                <td class="w-[100px] h-auto">
                  <img class="fit-size" [src]="picture.data">
                </td>
                <td class="w-32">
                  <button class="btn btn-outline-danger btn-sm m-1" (click)="removeImage(picture.id)">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </ng-template>
    </nb-card-body> <nb-card-footer>
      <div class="flex justify-center items-center">
        <button class="btn btn-danger mr-2" (click)="ref.close(); resetPreviewState()">取消</button>
        <button *ngIf="!isPreviewMode" class="btn btn-success" (click)="uploadImage(ref)">儲存</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>