import { Component, Input, Output, EventEmitter, OnInit, TemplateRef, ViewChild, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogService, NbSpinnerModule } from '@nebular/theme';
import { SharedModule } from '../../../pages/components/shared.module';
import { PictureService } from 'src/services/api/services';
import { GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';
import { MessageService } from 'src/app/shared/services/message.service';

// 圖片項目介面
export interface ImageItem {
  id: number;
  name: string;
  size: number;
  thumbnailUrl?: string;
  fullUrl?: string;
  lastModified?: Date;
  guid?: string | null; // 新增 CGuid 欄位支援
}

@Component({
  selector: 'app-image-preview',
  templateUrl: './image-preview.component.html',
  styleUrls: ['./image-preview.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, NbSpinnerModule]
})
export class ImagePreviewComponent implements OnInit, OnChanges {
  @Input() isVisible: boolean = false;
  @Input() selectedImages: ImageItem[] = [];
  @Input() initialImageIndex: number = 0;
  @Input() showSelectionToggle: boolean = true;

  // 新增輸入參數來接收圖片載入所需的資訊
  @Input() buildCaseId?: number;
  @Input() materialId?: number;
  @Input() pictureType?: number;
  @Input() searchTerm?: string;

  // 新增綁定功能相關輸入參數
  @Input() showBindingInterface: boolean = false;
  @Input() materialName?: string;

  @Output() imageSelectionToggle = new EventEmitter<ImageItem>();
  @Output() close = new EventEmitter<void>();
  @Output() previousImage = new EventEmitter<number>();
  @Output() nextImage = new EventEmitter<number>();

  // 新增綁定功能相關輸出事件
  @Output() confirmImageBinding = new EventEmitter<ImageItem[]>();
  @Output() categoryChange = new EventEmitter<number>();

  @ViewChild('imagePreview', { static: true }) imagePreview!: TemplateRef<any>;

  // 內部屬性，不再依賴外部傳入
  images: ImageItem[] = [];
  availableImages: ImageItem[] = [];
  previewingImage: ImageItem | null = null;
  currentPreviewIndex: number = 0;
  isLoading: boolean = false;

  // 分頁相關
  currentPage: number = 1;
  pageSize: number = 50;
  totalRecords: number = 0;

  // Picklist 分頁相關 (每頁5筆)
  picklistPageSize: number = 30;
  availableCurrentPage: number = 1;
  selectedCurrentPage: number = 1;

  // 綁定模式相關屬性
  categoryOptions = [
    { value: 1, label: '建材圖片' },
    { value: 2, label: '示意圖片' }
  ];
  selectedCategory: number = 1;
  tempSelectedImages: ImageItem[] = []; // 臨時選擇的圖片

  // GUID 圖片緩存，避免重複請求
  private guidImageCache: Map<string, string> = new Map();

  constructor(
    private dialogService: NbDialogService,
    private pictureService: PictureService,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.loadImages();
  }

  ngOnChanges(): void {
    // 當輸入參數變化時重新載入圖片
    if (this.buildCaseId || this.materialId || this.pictureType !== undefined) {
      this.loadImages();
    }
  }

  // Computed properties for picklist pagination
  get totalAvailableRecords(): number {
    return this.availableImages.length;
  }

  get availablePageCount(): number {
    return Math.ceil(this.totalAvailableRecords / this.picklistPageSize);
  }

  get selectedPageCount(): number {
    return Math.ceil(this.tempSelectedImages.length / this.picklistPageSize);
  }

  // 主要分頁相關計算屬性
  get totalPages(): number {
    return Math.ceil(this.totalRecords / this.pageSize);
  }

  get paginatedAvailableImages(): ImageItem[] {
    const startIndex = (this.availableCurrentPage - 1) * this.picklistPageSize;
    const endIndex = startIndex + this.picklistPageSize;
    return this.availableImages.slice(startIndex, endIndex);
  }

  get paginatedSelectedImages(): ImageItem[] {
    const startIndex = (this.selectedCurrentPage - 1) * this.picklistPageSize;
    const endIndex = startIndex + this.picklistPageSize;
    return this.tempSelectedImages.slice(startIndex, endIndex);
  }

  // 載入圖片的主要方法
  loadImages(): void {
    if (!this.buildCaseId && !this.materialId) {
      return;
    }

    this.isLoading = true;

    // 如果處於綁定模式且沒有 materialId，初始化 tempSelectedImages
    if (this.showBindingInterface && !this.materialId) {
      this.tempSelectedImages = [...this.selectedImages];
    }

    this.loadAvailableImages();
    this.loadSelectedImages();
  }

  // 載入可選擇的圖片
  loadAvailableImages(): void {
    if (!this.buildCaseId) {
      this.availableImages = [];
      return;
    }

    // 載入較大量的資料以支援前端分頁，或者根據需求調整
    const apiPageSize = this.pageSize; // 使用 pageSize 屬性 (50)

    this.pictureService.apiPictureGetPictureListPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        cPictureType: this.pictureType,
        PageIndex: this.currentPage,
        PageSize: apiPageSize,
        CName: this.searchTerm || undefined
      }
    }).subscribe({
      next: (res: GetPictureListResponseListResponseBase) => {
        if (res.StatusCode === 0) {
          // 轉換 API 回應為 ImageItem 格式
          const newImages = res.Entries?.map((picture: GetPictureListResponse) => ({
            id: picture.CId || 0,
            name: picture.CPictureCode || '',
            size: 0,
            thumbnailUrl: picture.CFile || '',
            fullUrl: picture.CFile || '',
            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),
            guid: picture.CGuid
          })) || [];

          this.totalRecords = res.TotalItems || 0;

          // 保存完整圖片列表
          this.images = newImages;

          // 更新可選圖片（排除已選擇的）
          this.updateAvailableImages();

          // 設定初始預覽圖片
          this.setInitialPreviewImage();
        } else {
          this.messageService.showErrorMSG(res.Message || '載入圖片失敗');
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.messageService.showErrorMSG('載入圖片失敗: ' + (error.message || '未知錯誤'));
        this.isLoading = false;
      }
    });
  }

  // 載入已選擇的圖片（如果有 materialId）
  loadSelectedImages(): void {
    if (!this.buildCaseId || !this.materialId) {
      // 如果沒有 materialId，在綁定模式下初始化為空陣列
      if (this.showBindingInterface) {
        this.tempSelectedImages = [...this.selectedImages];
        this.updateAvailableImages();
      }
      return;
    }

    this.pictureService.apiPictureGetPictureListPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CMaterialId: this.materialId,
        cPictureType: this.pictureType,
        PageIndex: 1,
        PageSize: 999 // 載入所有已選圖片
      }
    }).subscribe({
      next: (res: GetPictureListResponseListResponseBase) => {
        if (res.StatusCode === 0) {
          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({
            id: picture.CId || 0,
            name: picture.CPictureCode || '',
            size: 0,
            thumbnailUrl: picture.CFile || '',
            fullUrl: picture.CFile || '',
            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),
            guid: picture.CGuid
          })) || [];

          // 如果處於綁定模式，同步更新 tempSelectedImages
          if (this.showBindingInterface) {
            this.tempSelectedImages = [...this.selectedImages];
            this.updateAvailableImages();
          }
        }
      },
      error: (error) => {
        console.error('載入已選擇圖片失敗:', error);
      }
    });
  }

  // 設定初始預覽圖片
  setInitialPreviewImage(): void {
    if (this.images.length > 0) {
      // 如果是頁面變更後的重新載入，使用 currentPreviewIndex (可能已被重置為0)
      // 如果是初始載入，使用 initialImageIndex
      const indexToUse = this.currentPreviewIndex >= 0 ? this.currentPreviewIndex : this.initialImageIndex;
      this.currentPreviewIndex = Math.max(0, Math.min(indexToUse, this.images.length - 1));
      this.previewingImage = this.images[this.currentPreviewIndex];
    } else {
      this.previewingImage = null;
      this.currentPreviewIndex = 0;
    }
  }

  onPreviousImage() {
    if (this.currentPreviewIndex > 0) {
      this.currentPreviewIndex--;
      this.previewingImage = this.images[this.currentPreviewIndex];
      this.previousImage.emit(this.currentPreviewIndex);
    }
  }

  onNextImage() {
    if (this.currentPreviewIndex < this.images.length - 1) {
      this.currentPreviewIndex++;
      this.previewingImage = this.images[this.currentPreviewIndex];
      this.nextImage.emit(this.currentPreviewIndex);
    }
  }

  onToggleImageSelection() {
    if (this.previewingImage) {
      this.imageSelectionToggle.emit(this.previewingImage);
    }
  }

  isImageSelected(image: ImageItem): boolean {
    return this.selectedImages.some(selected => selected.id === image.id);
  }

  onClose() {
    this.close.emit();
  }

  // 開啟預覽對話框的方法
  openPreview(imagePreviewRef?: TemplateRef<any>) {
    // 每次開啟時重新載入圖片，確保資料是最新的
    this.loadImages();

    // 開啟對話框
    const template = imagePreviewRef || this.imagePreview;
    this.dialogService.open(template);
  }

  // 使用 CGuid 取得圖片資料的方法（現在主要用於手動呼叫的情況）
  getPictureByGuid(guid: string): void {
    if (!guid) {
      this.messageService.showErrorMSG('圖片 GUID 不能為空');
      return;
    }

    // 檢查是否已經在緩存中
    if (this.guidImageCache.has(guid)) {
      return;
    }

    // 建構圖片 URL 並加入緩存
    const imageUrl = `${this.pictureService.rootUrl}/api/Picture/GetPicture/${guid}`;
    this.guidImageCache.set(guid, imageUrl);
    this.updateImageItemWithUrl(guid, imageUrl);
  }

  // 更新 ImageItem 的 URL
  private updateImageItemWithUrl(guid: string, url: string): void {
    // 更新 images 陣列中的項目
    this.images.forEach(image => {
      if (image.guid === guid) {
        image.fullUrl = url;
        image.thumbnailUrl = url;
      }
    });

    // 更新 availableImages 陣列中的項目
    this.availableImages.forEach(image => {
      if (image.guid === guid) {
        image.fullUrl = url;
        image.thumbnailUrl = url;
      }
    });

    // 更新 selectedImages 陣列中的項目
    this.selectedImages.forEach(image => {
      if (image.guid === guid) {
        image.fullUrl = url;
        image.thumbnailUrl = url;
      }
    });

    // 更新 tempSelectedImages 陣列中的項目
    this.tempSelectedImages.forEach(image => {
      if (image.guid === guid) {
        image.fullUrl = url;
        image.thumbnailUrl = url;
      }
    });

    // 如果當前預覽的圖片是這個 GUID，也要更新
    if (this.previewingImage && this.previewingImage.guid === guid) {
      this.previewingImage.fullUrl = url;
      this.previewingImage.thumbnailUrl = url;
    }
  }

  // 獲取圖片 URL，優先使用 CGuid，避免重複呼叫 API
  getImageUrl(imageItem: ImageItem): string {
    // 如果已經有 fullUrl 或 thumbnailUrl，直接回傳
    if (imageItem.fullUrl || imageItem.thumbnailUrl) {
      return imageItem.fullUrl || imageItem.thumbnailUrl || '';
    }

    // 如果有 GUID，檢查緩存
    if (imageItem.guid) {
      const cachedUrl = this.guidImageCache.get(imageItem.guid);
      if (cachedUrl) {
        // 更新 imageItem 的 URL，避免下次再查詢緩存
        imageItem.fullUrl = cachedUrl;
        imageItem.thumbnailUrl = cachedUrl;
        return cachedUrl;
      }

      // 如果還沒在緩存中，建構 URL 並加入緩存
      const imageUrl = `${this.pictureService.rootUrl}/api/Picture/GetPicture/${imageItem.guid}`;
      this.guidImageCache.set(imageItem.guid, imageUrl);

      // 更新 imageItem 的 URL
      imageItem.fullUrl = imageUrl;
      imageItem.thumbnailUrl = imageUrl;

      return imageUrl;
    }

    return '';
  }

  // 綁定模式相關方法
  onCategoryChanged(category: number) {
    this.selectedCategory = category;
    this.categoryChange.emit(category);
    if (this.buildCaseId) {
      this.loadImages();
    }
  }

  // 開啟綁定界面
  openBindingInterface(imageBindingRef?: TemplateRef<any>) {
    this.showBindingInterface = true;

    // 重置分頁狀態
    this.availableCurrentPage = 1;
    this.selectedCurrentPage = 1;

    // 每次開啟時重新載入圖片，確保資料是最新的
    // tempSelectedImages 會在 loadSelectedImages 完成後自動設定
    this.loadImages();

    const template = imageBindingRef || this.imagePreview;
    this.dialogService.open(template);
  }

  // 確認圖片綁定
  onConfirmBinding() {
    this.confirmImageBinding.emit(this.tempSelectedImages);
    this.showBindingInterface = false;
  }

  // 取消綁定
  onCancelBinding() {
    this.tempSelectedImages = [];
    this.showBindingInterface = false;
    this.onClose();
  }

  // 切換圖片選擇狀態
  toggleImageSelection(image: ImageItem) {
    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);
    if (index > -1) {
      this.tempSelectedImages.splice(index, 1);
    } else {
      this.tempSelectedImages.push(image);
    }
  }

  // 檢查圖片是否被選中
  isImageTempSelected(image: ImageItem): boolean {
    return this.tempSelectedImages.some(img => img.id === image.id);
  }

  // Picklist 相關方法
  moveToSelected(image: ImageItem): void {
    if (!this.isImageTempSelected(image)) {
      this.tempSelectedImages.push(image);
      this.updateAvailableImages();
      this.resetSelectedPageIfNeeded();
    }
  }

  moveToAvailable(image: ImageItem): void {
    const index = this.tempSelectedImages.findIndex(img => img.id === image.id);
    if (index > -1) {
      this.tempSelectedImages.splice(index, 1);
      this.updateAvailableImages();
      this.resetSelectedPageIfNeeded();
    }
  }

  moveAllToSelected(): void {
    // 將目前頁面的可選圖片全部移到已選擇
    this.paginatedAvailableImages.forEach(image => {
      if (!this.isImageTempSelected(image)) {
        this.tempSelectedImages.push(image);
      }
    });
    this.updateAvailableImages();
    this.resetSelectedPageIfNeeded();
  }

  moveAllToAvailable(): void {
    this.tempSelectedImages = [];
    this.updateAvailableImages();
    this.selectedCurrentPage = 1;
  }

  updateAvailableImages(): void {
    // 重新計算可選圖片（排除已選擇的）
    const selectedIds = this.tempSelectedImages.map(img => img.id);
    this.availableImages = this.images.filter(image => !selectedIds.includes(image.id));
    this.resetAvailablePageIfNeeded();
  }

  resetAvailablePageIfNeeded(): void {
    if (this.availableCurrentPage > this.availablePageCount && this.availablePageCount > 0) {
      this.availableCurrentPage = this.availablePageCount;
    }
  }

  resetSelectedPageIfNeeded(): void {
    if (this.selectedCurrentPage > this.selectedPageCount && this.selectedPageCount > 0) {
      this.selectedCurrentPage = this.selectedPageCount;
    }
  }

  // 分頁導航方法
  goToAvailablePage(page: number): void {
    this.availableCurrentPage = page;
  }

  goToSelectedPage(page: number): void {
    this.selectedCurrentPage = page;
  }

  // 主要分頁導航方法
  goToPage(page: number): void {
    this.currentPage = page;
    this.loadImages();
    // 重置預覽索引
    this.currentPreviewIndex = 0;
  }

  // 搜尋變更處理
  onSearchTermChange(): void {
    // 重新載入圖片並重置分頁
    this.availableCurrentPage = 1;
    this.loadImages();
  }
}
