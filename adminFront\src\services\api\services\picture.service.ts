/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiPictureDeletePicturePost$Json } from '../fn/picture/api-picture-delete-picture-post-json';
import { ApiPictureDeletePicturePost$Json$Params } from '../fn/picture/api-picture-delete-picture-post-json';
import { apiPictureDeletePicturePost$Plain } from '../fn/picture/api-picture-delete-picture-post-plain';
import { ApiPictureDeletePicturePost$Plain$Params } from '../fn/picture/api-picture-delete-picture-post-plain';
import { apiPictureGetPictureGuidGet } from '../fn/picture/api-picture-get-picture-guid-get';
import { ApiPictureGetPictureGuidGet$Params } from '../fn/picture/api-picture-get-picture-guid-get';
import { apiPictureGetPictureListPost$Json } from '../fn/picture/api-picture-get-picture-list-post-json';
import { ApiPictureGetPictureListPost$Json$Params } from '../fn/picture/api-picture-get-picture-list-post-json';
import { apiPictureGetPictureListPost$Plain } from '../fn/picture/api-picture-get-picture-list-post-plain';
import { ApiPictureGetPictureListPost$Plain$Params } from '../fn/picture/api-picture-get-picture-list-post-plain';
import { apiPictureUpdatePicturePost$Json } from '../fn/picture/api-picture-update-picture-post-json';
import { ApiPictureUpdatePicturePost$Json$Params } from '../fn/picture/api-picture-update-picture-post-json';
import { apiPictureUpdatePicturePost$Plain } from '../fn/picture/api-picture-update-picture-post-plain';
import { ApiPictureUpdatePicturePost$Plain$Params } from '../fn/picture/api-picture-update-picture-post-plain';
import { apiPictureUploadListPicturePost$Json } from '../fn/picture/api-picture-upload-list-picture-post-json';
import { ApiPictureUploadListPicturePost$Json$Params } from '../fn/picture/api-picture-upload-list-picture-post-json';
import { apiPictureUploadListPicturePost$Plain } from '../fn/picture/api-picture-upload-list-picture-post-plain';
import { ApiPictureUploadListPicturePost$Plain$Params } from '../fn/picture/api-picture-upload-list-picture-post-plain';
import { BooleanResponseBase } from '../models/boolean-response-base';
import { GetPictureListResponseListResponseBase } from '../models/get-picture-list-response-list-response-base';
import { UploadFileResponseResponseBase } from '../models/upload-file-response-response-base';

@Injectable({ providedIn: 'root' })
export class PictureService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiPictureGetPictureListPost()` */
  static readonly ApiPictureGetPictureListPostPath = '/api/Picture/GetPictureList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureGetPictureListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPictureListPost$Plain$Response(params?: ApiPictureGetPictureListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {
    return apiPictureGetPictureListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPictureListPost$Plain(params?: ApiPictureGetPictureListPost$Plain$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {
    return this.apiPictureGetPictureListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureGetPictureListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPictureListPost$Json$Response(params?: ApiPictureGetPictureListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {
    return apiPictureGetPictureListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureGetPictureListPost$Json(params?: ApiPictureGetPictureListPost$Json$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {
    return this.apiPictureGetPictureListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPictureGetPictureGuidGet()` */
  static readonly ApiPictureGetPictureGuidGetPath = '/api/Picture/GetPicture/{guid}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureGetPictureGuidGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiPictureGetPictureGuidGet$Response(params: ApiPictureGetPictureGuidGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return apiPictureGetPictureGuidGet(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureGetPictureGuidGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiPictureGetPictureGuidGet(params: ApiPictureGetPictureGuidGet$Params, context?: HttpContext): Observable<void> {
    return this.apiPictureGetPictureGuidGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `apiPictureUploadListPicturePost()` */
  static readonly ApiPictureUploadListPicturePostPath = '/api/Picture/UploadListPicture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUploadListPicturePost$Plain()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Plain$Response(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUploadListPicturePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Plain$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Plain(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUploadListPicturePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUploadListPicturePost$Json()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Json$Response(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUploadListPicturePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Json$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUploadListPicturePost$Json(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUploadListPicturePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPictureUpdatePicturePost()` */
  static readonly ApiPictureUpdatePicturePostPath = '/api/Picture/UpdatePicture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUpdatePicturePost$Plain()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Plain$Response(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUpdatePicturePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Plain$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Plain(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUpdatePicturePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureUpdatePicturePost$Json()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Json$Response(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {
    return apiPictureUpdatePicturePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Json$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  apiPictureUpdatePicturePost$Json(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {
    return this.apiPictureUpdatePicturePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPictureDeletePicturePost()` */
  static readonly ApiPictureDeletePicturePostPath = '/api/Picture/DeletePicture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureDeletePicturePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureDeletePicturePost$Plain$Response(params?: ApiPictureDeletePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {
    return apiPictureDeletePicturePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureDeletePicturePost$Plain(params?: ApiPictureDeletePicturePost$Plain$Params, context?: HttpContext): Observable<BooleanResponseBase> {
    return this.apiPictureDeletePicturePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPictureDeletePicturePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureDeletePicturePost$Json$Response(params?: ApiPictureDeletePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {
    return apiPictureDeletePicturePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPictureDeletePicturePost$Json(params?: ApiPictureDeletePicturePost$Json$Params, context?: HttpContext): Observable<BooleanResponseBase> {
    return this.apiPictureDeletePicturePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)
    );
  }

}
