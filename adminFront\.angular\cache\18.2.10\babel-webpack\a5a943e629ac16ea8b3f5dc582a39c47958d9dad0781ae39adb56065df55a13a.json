{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiPictureGetPictureGuidGet(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiPictureGetPictureGuidGet.PATH, 'get');\n  if (params) {\n    rb.path('guid', params.guid, {});\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: '*/*',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r.clone({\n      body: undefined\n    });\n  }));\n}\napiPictureGetPictureGuidGet.PATH = '/api/Picture/GetPicture/{guid}';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiPictureGetPictureGuidGet", "http", "rootUrl", "params", "context", "rb", "PATH", "path", "guid", "request", "build", "responseType", "accept", "pipe", "r", "clone", "body", "undefined"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\picture\\api-picture-get-picture-guid-get.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\n\r\nexport interface ApiPictureGetPictureGuidGet$Params {\r\n  guid: string;\r\n}\r\n\r\nexport function apiPictureGetPictureGuidGet(http: HttpClient, rootUrl: string, params: ApiPictureGetPictureGuidGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {\r\n  const rb = new RequestBuilder(rootUrl, apiPictureGetPictureGuidGet.PATH, 'get');\r\n  if (params) {\r\n    rb.path('guid', params.guid, {});\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: '*/*', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;\r\n    })\r\n  );\r\n}\r\n\r\napiPictureGetPictureGuidGet.PATH = '/api/Picture/GetPicture/{guid}';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,2BAA2BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA0C,EAAEC,OAAqB;EAC9I,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,2BAA2B,CAACM,IAAI,EAAE,KAAK,CAAC;EAC/E,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAAC,MAAM,EAAEJ,MAAM,CAACK,IAAI,EAAE,EAAE,CAAC;EAClC;EAEA,OAAOP,IAAI,CAACQ,OAAO,CACjBJ,EAAE,CAACK,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,KAAK;IAAER;EAAO,CAAE,CAAC,CAC3D,CAACS,IAAI,CACJhB,MAAM,CAAEiB,CAAM,IAA6BA,CAAC,YAAYlB,YAAY,CAAC,EACrEE,GAAG,CAAEgB,CAAoB,IAAI;IAC3B,OAAQA,CAAuB,CAACC,KAAK,CAAC;MAAEC,IAAI,EAAEC;IAAS,CAAE,CAA6B;EACxF,CAAC,CAAC,CACH;AACH;AAEAjB,2BAA2B,CAACM,IAAI,GAAG,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}