{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPictureDeletePicturePost$Json } from '../fn/picture/api-picture-delete-picture-post-json';\nimport { apiPictureDeletePicturePost$Plain } from '../fn/picture/api-picture-delete-picture-post-plain';\nimport { apiPictureGetPictureGuidGet } from '../fn/picture/api-picture-get-picture-guid-get';\nimport { apiPictureGetPictureListPost$Json } from '../fn/picture/api-picture-get-picture-list-post-json';\nimport { apiPictureGetPictureListPost$Plain } from '../fn/picture/api-picture-get-picture-list-post-plain';\nimport { apiPictureUpdatePicturePost$Json } from '../fn/picture/api-picture-update-picture-post-json';\nimport { apiPictureUpdatePicturePost$Plain } from '../fn/picture/api-picture-update-picture-post-plain';\nimport { apiPictureUploadListPicturePost$Json } from '../fn/picture/api-picture-upload-list-picture-post-json';\nimport { apiPictureUploadListPicturePost$Plain } from '../fn/picture/api-picture-upload-list-picture-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class PictureService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiPictureGetPictureListPost()` */\n  static {\n    this.ApiPictureGetPictureListPostPath = '/api/Picture/GetPictureList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureGetPictureListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureGetPictureListPost$Plain$Response(params, context) {\n    return apiPictureGetPictureListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureGetPictureListPost$Plain(params, context) {\n    return this.apiPictureGetPictureListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureGetPictureListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureGetPictureListPost$Json$Response(params, context) {\n    return apiPictureGetPictureListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureGetPictureListPost$Json(params, context) {\n    return this.apiPictureGetPictureListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiPictureGetPictureGuidGet()` */\n  static {\n    this.ApiPictureGetPictureGuidGetPath = '/api/Picture/GetPicture/{guid}';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureGetPictureGuidGet()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPictureGetPictureGuidGet$Response(params, context) {\n    return apiPictureGetPictureGuidGet(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureGetPictureGuidGet$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPictureGetPictureGuidGet(params, context) {\n    return this.apiPictureGetPictureGuidGet$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiPictureUploadListPicturePost()` */\n  static {\n    this.ApiPictureUploadListPicturePostPath = '/api/Picture/UploadListPicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureUploadListPicturePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUploadListPicturePost$Plain$Response(params, context) {\n    return apiPictureUploadListPicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUploadListPicturePost$Plain(params, context) {\n    return this.apiPictureUploadListPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureUploadListPicturePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUploadListPicturePost$Json$Response(params, context) {\n    return apiPictureUploadListPicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUploadListPicturePost$Json(params, context) {\n    return this.apiPictureUploadListPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiPictureUpdatePicturePost()` */\n  static {\n    this.ApiPictureUpdatePicturePostPath = '/api/Picture/UpdatePicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureUpdatePicturePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUpdatePicturePost$Plain$Response(params, context) {\n    return apiPictureUpdatePicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUpdatePicturePost$Plain(params, context) {\n    return this.apiPictureUpdatePicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureUpdatePicturePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUpdatePicturePost$Json$Response(params, context) {\n    return apiPictureUpdatePicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiPictureUpdatePicturePost$Json(params, context) {\n    return this.apiPictureUpdatePicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiPictureDeletePicturePost()` */\n  static {\n    this.ApiPictureDeletePicturePostPath = '/api/Picture/DeletePicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureDeletePicturePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureDeletePicturePost$Plain$Response(params, context) {\n    return apiPictureDeletePicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureDeletePicturePost$Plain(params, context) {\n    return this.apiPictureDeletePicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPictureDeletePicturePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureDeletePicturePost$Json$Response(params, context) {\n    return apiPictureDeletePicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPictureDeletePicturePost$Json(params, context) {\n    return this.apiPictureDeletePicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function PictureService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PictureService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PictureService,\n      factory: PictureService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiPictureDeletePicturePost$Json", "apiPictureDeletePicturePost$Plain", "apiPictureGetPictureGuidGet", "apiPictureGetPictureListPost$Json", "apiPictureGetPictureListPost$Plain", "apiPictureUpdatePicturePost$Json", "apiPictureUpdatePicturePost$Plain", "apiPictureUploadListPicturePost$Json", "apiPictureUploadListPicturePost$Plain", "PictureService", "constructor", "config", "http", "ApiPictureGetPictureListPostPath", "apiPictureGetPictureListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiPictureGetPictureListPost$Json$Response", "ApiPictureGetPictureGuidGetPath", "apiPictureGetPictureGuidGet$Response", "ApiPictureUploadListPicturePostPath", "apiPictureUploadListPicturePost$Plain$Response", "apiPictureUploadListPicturePost$Json$Response", "ApiPictureUpdatePicturePostPath", "apiPictureUpdatePicturePost$Plain$Response", "apiPictureUpdatePicturePost$Json$Response", "ApiPictureDeletePicturePostPath", "apiPictureDeletePicturePost$Plain$Response", "apiPictureDeletePicturePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\picture.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiPictureDeletePicturePost$Json } from '../fn/picture/api-picture-delete-picture-post-json';\r\nimport { ApiPictureDeletePicturePost$Json$Params } from '../fn/picture/api-picture-delete-picture-post-json';\r\nimport { apiPictureDeletePicturePost$Plain } from '../fn/picture/api-picture-delete-picture-post-plain';\r\nimport { ApiPictureDeletePicturePost$Plain$Params } from '../fn/picture/api-picture-delete-picture-post-plain';\r\nimport { apiPictureGetPictureGuidGet } from '../fn/picture/api-picture-get-picture-guid-get';\r\nimport { ApiPictureGetPictureGuidGet$Params } from '../fn/picture/api-picture-get-picture-guid-get';\r\nimport { apiPictureGetPictureListPost$Json } from '../fn/picture/api-picture-get-picture-list-post-json';\r\nimport { ApiPictureGetPictureListPost$Json$Params } from '../fn/picture/api-picture-get-picture-list-post-json';\r\nimport { apiPictureGetPictureListPost$Plain } from '../fn/picture/api-picture-get-picture-list-post-plain';\r\nimport { ApiPictureGetPictureListPost$Plain$Params } from '../fn/picture/api-picture-get-picture-list-post-plain';\r\nimport { apiPictureUpdatePicturePost$Json } from '../fn/picture/api-picture-update-picture-post-json';\r\nimport { ApiPictureUpdatePicturePost$Json$Params } from '../fn/picture/api-picture-update-picture-post-json';\r\nimport { apiPictureUpdatePicturePost$Plain } from '../fn/picture/api-picture-update-picture-post-plain';\r\nimport { ApiPictureUpdatePicturePost$Plain$Params } from '../fn/picture/api-picture-update-picture-post-plain';\r\nimport { apiPictureUploadListPicturePost$Json } from '../fn/picture/api-picture-upload-list-picture-post-json';\r\nimport { ApiPictureUploadListPicturePost$Json$Params } from '../fn/picture/api-picture-upload-list-picture-post-json';\r\nimport { apiPictureUploadListPicturePost$Plain } from '../fn/picture/api-picture-upload-list-picture-post-plain';\r\nimport { ApiPictureUploadListPicturePost$Plain$Params } from '../fn/picture/api-picture-upload-list-picture-post-plain';\r\nimport { BooleanResponseBase } from '../models/boolean-response-base';\r\nimport { GetPictureListResponseListResponseBase } from '../models/get-picture-list-response-list-response-base';\r\nimport { UploadFileResponseResponseBase } from '../models/upload-file-response-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class PictureService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiPictureGetPictureListPost()` */\r\n  static readonly ApiPictureGetPictureListPostPath = '/api/Picture/GetPictureList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureGetPictureListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureGetPictureListPost$Plain$Response(params?: ApiPictureGetPictureListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {\r\n    return apiPictureGetPictureListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureGetPictureListPost$Plain(params?: ApiPictureGetPictureListPost$Plain$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {\r\n    return this.apiPictureGetPictureListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureGetPictureListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureGetPictureListPost$Json$Response(params?: ApiPictureGetPictureListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {\r\n    return apiPictureGetPictureListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureGetPictureListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureGetPictureListPost$Json(params?: ApiPictureGetPictureListPost$Json$Params, context?: HttpContext): Observable<GetPictureListResponseListResponseBase> {\r\n    return this.apiPictureGetPictureListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetPictureListResponseListResponseBase>): GetPictureListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiPictureGetPictureGuidGet()` */\r\n  static readonly ApiPictureGetPictureGuidGetPath = '/api/Picture/GetPicture/{guid}';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureGetPictureGuidGet()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPictureGetPictureGuidGet$Response(params: ApiPictureGetPictureGuidGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {\r\n    return apiPictureGetPictureGuidGet(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureGetPictureGuidGet$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPictureGetPictureGuidGet(params: ApiPictureGetPictureGuidGet$Params, context?: HttpContext): Observable<void> {\r\n    return this.apiPictureGetPictureGuidGet$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<void>): void => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiPictureUploadListPicturePost()` */\r\n  static readonly ApiPictureUploadListPicturePostPath = '/api/Picture/UploadListPicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureUploadListPicturePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUploadListPicturePost$Plain$Response(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiPictureUploadListPicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUploadListPicturePost$Plain(params?: ApiPictureUploadListPicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiPictureUploadListPicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureUploadListPicturePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUploadListPicturePost$Json$Response(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiPictureUploadListPicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUploadListPicturePost$Json(params?: ApiPictureUploadListPicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiPictureUploadListPicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiPictureUpdatePicturePost()` */\r\n  static readonly ApiPictureUpdatePicturePostPath = '/api/Picture/UpdatePicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureUpdatePicturePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUpdatePicturePost$Plain$Response(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiPictureUpdatePicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUpdatePicturePost$Plain(params?: ApiPictureUpdatePicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiPictureUpdatePicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureUpdatePicturePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUpdatePicturePost$Json$Response(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiPictureUpdatePicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiPictureUpdatePicturePost$Json(params?: ApiPictureUpdatePicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiPictureUpdatePicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiPictureDeletePicturePost()` */\r\n  static readonly ApiPictureDeletePicturePostPath = '/api/Picture/DeletePicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureDeletePicturePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureDeletePicturePost$Plain$Response(params?: ApiPictureDeletePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {\r\n    return apiPictureDeletePicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureDeletePicturePost$Plain(params?: ApiPictureDeletePicturePost$Plain$Params, context?: HttpContext): Observable<BooleanResponseBase> {\r\n    return this.apiPictureDeletePicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPictureDeletePicturePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureDeletePicturePost$Json$Response(params?: ApiPictureDeletePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {\r\n    return apiPictureDeletePicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPictureDeletePicturePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPictureDeletePicturePost$Json(params?: ApiPictureDeletePicturePost$Json$Params, context?: HttpContext): Observable<BooleanResponseBase> {\r\n    return this.apiPictureDeletePicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,gCAAgC,QAAQ,oDAAoD;AAErG,SAASC,iCAAiC,QAAQ,qDAAqD;AAEvG,SAASC,2BAA2B,QAAQ,gDAAgD;AAE5F,SAASC,iCAAiC,QAAQ,sDAAsD;AAExG,SAASC,kCAAkC,QAAQ,uDAAuD;AAE1G,SAASC,gCAAgC,QAAQ,oDAAoD;AAErG,SAASC,iCAAiC,QAAQ,qDAAqD;AAEvG,SAASC,oCAAoC,QAAQ,yDAAyD;AAE9G,SAASC,qCAAqC,QAAQ,0DAA0D;;;;AAOhH,OAAM,MAAOC,cAAe,SAAQV,WAAW;EAC7CW,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,gCAAgC,GAAG,6BAA6B;EAAC;EAEjF;;;;;;EAMAC,2CAA2CA,CAACC,MAAkD,EAAEC,OAAqB;IACnH,OAAOZ,kCAAkC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAZ,kCAAkCA,CAACW,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACF,2CAA2C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3EpB,GAAG,CAAEqB,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;;;;;EAMAC,0CAA0CA,CAACN,MAAiD,EAAEC,OAAqB;IACjH,OAAOb,iCAAiC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAb,iCAAiCA,CAACY,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACK,0CAA0C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1EpB,GAAG,CAAEqB,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;IACgB,KAAAE,+BAA+B,GAAG,gCAAgC;EAAC;EAEnF;;;;;;EAMAC,oCAAoCA,CAACR,MAA0C,EAAEC,OAAqB;IACpG,OAAOd,2BAA2B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAd,2BAA2BA,CAACa,MAA0C,EAAEC,OAAqB;IAC3F,OAAO,IAAI,CAACO,oCAAoC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpEpB,GAAG,CAAEqB,CAA2B,IAAWA,CAAC,CAACC,IAAI,CAAC,CACnD;EACH;EAEA;;IACgB,KAAAI,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAACV,MAAqD,EAAEC,OAAqB;IACzH,OAAOR,qCAAqC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAR,qCAAqCA,CAACO,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACS,8CAA8C,CAACV,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EpB,GAAG,CAAEqB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAM,6CAA6CA,CAACX,MAAoD,EAAEC,OAAqB;IACvH,OAAOT,oCAAoC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAT,oCAAoCA,CAACQ,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACU,6CAA6C,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EpB,GAAG,CAAEqB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAO,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACb,MAAiD,EAAEC,OAAqB;IACjH,OAAOV,iCAAiC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAV,iCAAiCA,CAACS,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACY,0CAA0C,CAACb,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1EpB,GAAG,CAAEqB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAS,yCAAyCA,CAACd,MAAgD,EAAEC,OAAqB;IAC/G,OAAOX,gCAAgC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAX,gCAAgCA,CAACU,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACa,yCAAyC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzEpB,GAAG,CAAEqB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAU,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAAChB,MAAiD,EAAEC,OAAqB;IACjH,OAAOf,iCAAiC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAf,iCAAiCA,CAACc,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACe,0CAA0C,CAAChB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1EpB,GAAG,CAAEqB,CAA0C,IAA0BA,CAAC,CAACC,IAAI,CAAC,CACjF;EACH;EAEA;;;;;;EAMAY,yCAAyCA,CAACjB,MAAgD,EAAEC,OAAqB;IAC/G,OAAOhB,gCAAgC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAhB,gCAAgCA,CAACe,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACgB,yCAAyC,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzEpB,GAAG,CAAEqB,CAA0C,IAA0BA,CAAC,CAACC,IAAI,CAAC,CACjF;EACH;;;uCAxNWX,cAAc,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd7B,cAAc;MAAA8B,OAAA,EAAd9B,cAAc,CAAA+B,IAAA;MAAAC,UAAA,EADD;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}