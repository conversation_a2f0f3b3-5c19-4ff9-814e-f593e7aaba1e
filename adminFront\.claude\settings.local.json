{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "mcp__sequential-thinking-mcp__think", "mcp__ide__getDiagnostics", "mcp__serena__find_symbol", "mcp__serena__activate_project", "mcp__serena__search_for_pattern", "mcp__serena__read_file", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__mcp-feedback-enhanced__interactive_feedback", "<PERSON><PERSON>(sed:*)", "Bash(find:*)", "Bash(xcopy:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(npx tsc:*)", "Bash(del temp-imagebinder.html)", "Bash(del \"C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\temp-imagebinder.html\")"], "deny": []}}