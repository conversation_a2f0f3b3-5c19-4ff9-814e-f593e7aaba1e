{"ast": null, "code": "import { EventEmitter, DestroyRef, inject } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { FormsModule } from '@angular/forms';\nimport { NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@nebular/theme\";\nfunction BuildCaseSelectComponent_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r1.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r1.CBuildCaseName, \" \");\n  }\n}\nfunction BuildCaseSelectComponent_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 4);\n    i0.ɵɵtext(1, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nfunction BuildCaseSelectComponent_nb_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 4);\n    i0.ɵɵtext(1, \" \\u7121\\u53EF\\u7528\\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nexport class BuildCaseSelectComponent {\n  constructor(buildCaseService) {\n    this.buildCaseService = buildCaseService;\n    this.selectedValue = null;\n    this.placeholder = '請選擇建案';\n    this.disabled = false;\n    this.required = false;\n    this.cssClass = '';\n    this.storageKey = 'selectedBuildCase'; // localStorage key\n    this.selectedValueChange = new EventEmitter();\n    this.valueChange = new EventEmitter();\n    this.selectionChange = new EventEmitter();\n    this.buildCaseListLoaded = new EventEmitter();\n    this.buildCaseList = [];\n    this.loading = false;\n    this.destroyRef = inject(DestroyRef);\n  }\n  ngOnInit() {\n    console.log('BuildCaseSelectComponent ngOnInit - 初始selectedValue:', this.selectedValue);\n    // 優先載入記憶的值，只有在沒有記憶值時才使用傳入的值\n    const storedValue = this.getStoredValue();\n    if (storedValue !== null) {\n      this.selectedValue = storedValue;\n      console.log('載入localStorage後的selectedValue:', this.selectedValue);\n    }\n    this.loadBuildCaseList();\n  }\n  loadBuildCaseList() {\n    this.loading = true;\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: res => {\n        if (res.Entries) {\n          this.buildCaseList = res.Entries;\n          this.buildCaseListLoaded.emit(this.buildCaseList);\n          // 驗證localStorage載入的值是否有效\n          if (this.selectedValue !== null && this.selectedValue !== undefined) {\n            const foundBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);\n            if (!foundBuildCase) {\n              // 如果localStorage中的值在建案列表中不存在，重設為null但保留localStorage記錄\n              console.log('localStorage中的建案ID在列表中不存在，重設為null:', this.selectedValue);\n              this.selectedValue = null;\n            } else {\n              console.log('驗證通過，localStorage中的建案ID有效:', this.selectedValue);\n              // 重新設定選中值以觸發UI更新\n              const currentValue = this.selectedValue;\n              this.selectedValue = null;\n              setTimeout(() => {\n                this.selectedValue = currentValue;\n              }, 0);\n            }\n          }\n          // 如果沒有選中值且有建案資料，自動選擇第一個\n          if ((this.selectedValue === null || this.selectedValue === undefined) && this.buildCaseList.length > 0) {\n            this.selectedValue = this.buildCaseList[0].cID;\n            this.selectedValueChange.emit(this.selectedValue);\n            this.valueChange.emit(this.selectedValue);\n            this.selectionChange.emit(this.buildCaseList[0]);\n            this.saveToLocalStorage(this.selectedValue);\n          } else if (this.selectedValue !== null && this.selectedValue !== undefined) {\n            // 如果已有選中值，觸發事件通知\n            const selectedBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);\n            if (selectedBuildCase) {\n              this.selectionChange.emit(selectedBuildCase);\n            }\n          }\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('載入建案列表失敗:', error);\n        this.loading = false;\n      }\n    });\n  }\n  onSelectionChange(value) {\n    this.selectedValue = value;\n    this.selectedValueChange.emit(value);\n    this.valueChange.emit(value);\n    // 儲存到localStorage\n    this.saveToLocalStorage(value);\n    if (value !== null && value !== undefined) {\n      const selectedBuildCase = this.buildCaseList.find(item => item.cID === value);\n      this.selectionChange.emit(selectedBuildCase || null);\n    } else {\n      this.selectionChange.emit(null);\n    }\n  }\n  // 取得選中的建案資料\n  getSelectedBuildCase() {\n    if (this.selectedValue === null || this.selectedValue === undefined) {\n      return null;\n    }\n    return this.buildCaseList.find(item => item.cID === this.selectedValue) || null;\n  }\n  // 重新載入建案列表\n  reload() {\n    this.loadBuildCaseList();\n  }\n  // 獲取localStorage中儲存的值\n  getStoredValue() {\n    try {\n      const storedValue = localStorage.getItem(this.storageKey);\n      console.log('從localStorage讀取值:', storedValue);\n      if (storedValue !== null) {\n        const parsedValue = parseInt(storedValue, 10);\n        if (!isNaN(parsedValue)) {\n          console.log('成功從localStorage載入建案選擇:', parsedValue);\n          return parsedValue;\n        }\n      } else {\n        console.log('localStorage中沒有儲存的建案選擇');\n      }\n    } catch (error) {\n      console.warn('無法從localStorage載入建案選擇:', error);\n    }\n    return null;\n  }\n  // 儲存選擇的建案到localStorage\n  saveToLocalStorage(value) {\n    try {\n      if (value !== null && value !== undefined) {\n        localStorage.setItem(this.storageKey, value.toString());\n        console.log('儲存建案選擇到localStorage:', value);\n      } else {\n        localStorage.removeItem(this.storageKey);\n        console.log('清除localStorage中的建案選擇');\n      }\n    } catch (error) {\n      console.warn('無法儲存建案選擇到localStorage:', error);\n    }\n  }\n  // 清除localStorage中的選擇\n  clearStoredSelection() {\n    try {\n      localStorage.removeItem(this.storageKey);\n    } catch (error) {\n      console.warn('無法清除localStorage中的建案選擇:', error);\n    }\n  }\n  static {\n    this.ɵfac = function BuildCaseSelectComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildCaseSelectComponent)(i0.ɵɵdirectiveInject(i1.BuildCaseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildCaseSelectComponent,\n      selectors: [[\"app-build-case-select\"]],\n      inputs: {\n        selectedValue: \"selectedValue\",\n        placeholder: \"placeholder\",\n        disabled: \"disabled\",\n        required: \"required\",\n        cssClass: \"cssClass\"\n      },\n      outputs: {\n        selectedValueChange: \"selectedValueChange\",\n        valueChange: \"valueChange\",\n        selectionChange: \"selectionChange\",\n        buildCaseListLoaded: \"buildCaseListLoaded\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 6,\n      consts: [[1, \"col-9\", 3, \"selectedChange\", \"selected\", \"placeholder\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"disabled\", 4, \"ngIf\"], [3, \"value\"], [3, \"disabled\"]],\n      template: function BuildCaseSelectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-select\", 0);\n          i0.ɵɵlistener(\"selectedChange\", function BuildCaseSelectComponent_Template_nb_select_selectedChange_0_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵtemplate(1, BuildCaseSelectComponent_nb_option_1_Template, 2, 2, \"nb-option\", 1)(2, BuildCaseSelectComponent_nb_option_2_Template, 2, 1, \"nb-option\", 2)(3, BuildCaseSelectComponent_nb_option_3_Template, 2, 1, \"nb-option\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"selected\", ctx.selectedValue)(\"placeholder\", ctx.placeholder)(\"disabled\", ctx.disabled || ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.buildCaseList.length === 0);\n        }\n      },\n      dependencies: [NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule, FormsModule, NgFor, NgIf],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\nnb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\nnb-select[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImJ1aWxkLWNhc2Utc2VsZWN0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtFQUNBLFdBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7QUFDRjs7QUFHQTtFQUNFLFlBQUE7QUFBRiIsImZpbGUiOiJidWlsZC1jYXNlLXNlbGVjdC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxubmItc2VsZWN0IHtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLy8g6LyJ5YWl54uA5oWL5qij5byPXHJcbm5iLXNlbGVjdFtkaXNhYmxlZF0ge1xyXG4gIG9wYWNpdHk6IDAuNjtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvYnVpbGQtY2FzZS1zZWxlY3QvYnVpbGQtY2FzZS1zZWxlY3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFBO0VBQ0EsV0FBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtBQUNGOztBQUdBO0VBQ0UsWUFBQTtBQUFGO0FBQ0EsZ2tCQUFna0IiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbm5iLXNlbGVjdCB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi8vIMOowrzCicOlwoXCpcOnwovCgMOmwoXCi8OmwqjCo8OlwrzCj1xyXG5uYi1zZWxlY3RbZGlzYWJsZWRdIHtcclxuICBvcGFjaXR5OiAwLjY7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "DestroyRef", "inject", "takeUntilDestroyed", "FormsModule", "NbSelectModule", "NbOptionModule", "<PERSON><PERSON><PERSON>", "NgIf", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r1", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "BuildCaseSelectComponent", "constructor", "buildCaseService", "selected<PERSON><PERSON><PERSON>", "placeholder", "disabled", "required", "cssClass", "storageKey", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueChange", "selectionChange", "buildCaseListLoaded", "buildCaseList", "loading", "destroyRef", "ngOnInit", "console", "log", "storedValue", "getStoredValue", "loadBuildCaseList", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "subscribe", "next", "res", "Entries", "emit", "undefined", "foundBuildCase", "find", "item", "currentValue", "setTimeout", "length", "saveToLocalStorage", "selectedBuildCase", "error", "onSelectionChange", "value", "getSelectedBuildCase", "reload", "localStorage", "getItem", "parsedValue", "parseInt", "isNaN", "warn", "setItem", "toString", "removeItem", "clearStoredSelection", "ɵɵdirectiveInject", "i1", "BuildCaseService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildCaseSelectComponent_Template", "rf", "ctx", "ɵɵlistener", "BuildCaseSelectComponent_Template_nb_select_selectedChange_0_listener", "$event", "ɵɵtemplate", "BuildCaseSelectComponent_nb_option_1_Template", "BuildCaseSelectComponent_nb_option_2_Template", "BuildCaseSelectComponent_nb_option_3_Template", "i2", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\build-case-select\\build-case-select.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\build-case-select\\build-case-select.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, OnDestroy, DestroyRef, inject } from '@angular/core';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbSelectModule, NbOptionModule } from '@nebular/theme';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-build-case-select',\r\n  standalone: true,\r\n  imports: [\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    FormsModule,\r\n    NgFor,\r\n    NgIf\r\n  ],\r\n  templateUrl: './build-case-select.component.html',\r\n  styleUrls: ['./build-case-select.component.scss']\r\n})\r\nexport class BuildCaseSelectComponent implements OnInit {\r\n\r\n  @Input() selectedValue: number | null | undefined = null;\r\n  @Input() placeholder: string = '請選擇建案';\r\n  @Input() disabled: boolean = false;\r\n  @Input() required: boolean = false;\r\n  @Input() cssClass: string = '';\r\n  private readonly storageKey: string = 'selectedBuildCase'; // localStorage key\r\n\r\n  @Output() selectedValueChange = new EventEmitter<number | null | undefined>();\r\n  @Output() valueChange = new EventEmitter<number | null | undefined>();\r\n  @Output() selectionChange = new EventEmitter<BuildCaseGetListReponse | null>();\r\n  @Output() buildCaseListLoaded = new EventEmitter<BuildCaseGetListReponse[]>();\r\n\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  loading: boolean = false;\r\n\r\n  private destroyRef = inject(DestroyRef);\r\n\r\n  constructor(\r\n    private buildCaseService: BuildCaseService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log('BuildCaseSelectComponent ngOnInit - 初始selectedValue:', this.selectedValue);\r\n    // 優先載入記憶的值，只有在沒有記憶值時才使用傳入的值\r\n    const storedValue = this.getStoredValue();\r\n    if (storedValue !== null) {\r\n      this.selectedValue = storedValue;\r\n      console.log('載入localStorage後的selectedValue:', this.selectedValue);\r\n    }\r\n    this.loadBuildCaseList();\r\n  }\r\n\r\n  private loadBuildCaseList(): void {\r\n    this.loading = true;\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyRef))\r\n      .subscribe({\r\n        next: (res) => {\r\n          if (res.Entries) {\r\n            this.buildCaseList = res.Entries;\r\n            this.buildCaseListLoaded.emit(this.buildCaseList);\r\n\r\n            // 驗證localStorage載入的值是否有效\r\n            if (this.selectedValue !== null && this.selectedValue !== undefined) {\r\n              const foundBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);\r\n              if (!foundBuildCase) {\r\n                // 如果localStorage中的值在建案列表中不存在，重設為null但保留localStorage記錄\r\n                console.log('localStorage中的建案ID在列表中不存在，重設為null:', this.selectedValue);\r\n                this.selectedValue = null;\r\n              } else {\r\n                console.log('驗證通過，localStorage中的建案ID有效:', this.selectedValue);\r\n                // 重新設定選中值以觸發UI更新\r\n                const currentValue = this.selectedValue;\r\n                this.selectedValue = null;\r\n                setTimeout(() => {\r\n                  this.selectedValue = currentValue;\r\n                }, 0);\r\n              }\r\n            }\r\n\r\n            // 如果沒有選中值且有建案資料，自動選擇第一個\r\n            if ((this.selectedValue === null || this.selectedValue === undefined) && this.buildCaseList.length > 0) {\r\n              this.selectedValue = this.buildCaseList[0].cID!;\r\n              this.selectedValueChange.emit(this.selectedValue);\r\n              this.valueChange.emit(this.selectedValue);\r\n              this.selectionChange.emit(this.buildCaseList[0]);\r\n              this.saveToLocalStorage(this.selectedValue);\r\n            } else if (this.selectedValue !== null && this.selectedValue !== undefined) {\r\n              // 如果已有選中值，觸發事件通知\r\n              const selectedBuildCase = this.buildCaseList.find(item => item.cID === this.selectedValue);\r\n              if (selectedBuildCase) {\r\n                this.selectionChange.emit(selectedBuildCase);\r\n              }\r\n            }\r\n          }\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('載入建案列表失敗:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  onSelectionChange(value: number | null | undefined): void {\r\n    this.selectedValue = value;\r\n    this.selectedValueChange.emit(value);\r\n    this.valueChange.emit(value);\r\n\r\n    // 儲存到localStorage\r\n    this.saveToLocalStorage(value);\r\n\r\n    if (value !== null && value !== undefined) {\r\n      const selectedBuildCase = this.buildCaseList.find(item => item.cID === value);\r\n      this.selectionChange.emit(selectedBuildCase || null);\r\n    } else {\r\n      this.selectionChange.emit(null);\r\n    }\r\n  }\r\n\r\n  // 取得選中的建案資料\r\n  getSelectedBuildCase(): BuildCaseGetListReponse | null {\r\n    if (this.selectedValue === null || this.selectedValue === undefined) {\r\n      return null;\r\n    }\r\n    return this.buildCaseList.find(item => item.cID === this.selectedValue) || null;\r\n  }\r\n\r\n  // 重新載入建案列表\r\n  reload(): void {\r\n    this.loadBuildCaseList();\r\n  }\r\n\r\n  // 獲取localStorage中儲存的值\r\n  private getStoredValue(): number | null {\r\n    try {\r\n      const storedValue = localStorage.getItem(this.storageKey);\r\n      console.log('從localStorage讀取值:', storedValue);\r\n      if (storedValue !== null) {\r\n        const parsedValue = parseInt(storedValue, 10);\r\n        if (!isNaN(parsedValue)) {\r\n          console.log('成功從localStorage載入建案選擇:', parsedValue);\r\n          return parsedValue;\r\n        }\r\n      } else {\r\n        console.log('localStorage中沒有儲存的建案選擇');\r\n      }\r\n    } catch (error) {\r\n      console.warn('無法從localStorage載入建案選擇:', error);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 儲存選擇的建案到localStorage\r\n  private saveToLocalStorage(value: number | null | undefined): void {\r\n    try {\r\n      if (value !== null && value !== undefined) {\r\n        localStorage.setItem(this.storageKey, value.toString());\r\n        console.log('儲存建案選擇到localStorage:', value);\r\n      } else {\r\n        localStorage.removeItem(this.storageKey);\r\n        console.log('清除localStorage中的建案選擇');\r\n      }\r\n    } catch (error) {\r\n      console.warn('無法儲存建案選擇到localStorage:', error);\r\n    }\r\n  }\r\n\r\n  // 清除localStorage中的選擇\r\n  clearStoredSelection(): void {\r\n    try {\r\n      localStorage.removeItem(this.storageKey);\r\n    } catch (error) {\r\n      console.warn('無法清除localStorage中的建案選擇:', error);\r\n    }\r\n  }\r\n}\r\n", "<nb-select [selected]=\"selectedValue\" [placeholder]=\"placeholder\" [disabled]=\"disabled || loading\" class=\"col-9\"\r\n  (selectedChange)=\"onSelectionChange($event)\">\r\n\r\n\r\n  <nb-option *ngFor=\"let buildCase of buildCaseList\" [value]=\"buildCase.cID\">\r\n    {{ buildCase.CBuildCaseName }}\r\n  </nb-option>\r\n\r\n  <nb-option *ngIf=\"loading\" [disabled]=\"true\">\r\n    載入中...\r\n  </nb-option>\r\n\r\n  <nb-option *ngIf=\"!loading && buildCaseList.length === 0\" [disabled]=\"true\">\r\n    無可用建案\r\n  </nb-option>\r\n</nb-select>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,EAAoCC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAC7G,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;;;;;;ICA3CC,EAAA,CAAAC,cAAA,mBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACxEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;IAEAT,EAAA,CAAAC,cAAA,mBAA6C;IAC3CD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;IAFeH,EAAA,CAAAI,UAAA,kBAAiB;;;;;IAI5CJ,EAAA,CAAAC,cAAA,mBAA4E;IAC1ED,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;IAF8CH,EAAA,CAAAI,UAAA,kBAAiB;;;ADS7E,OAAM,MAAOM,wBAAwB;EAmBnCC,YACUC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAlBjB,KAAAC,aAAa,GAA8B,IAAI;IAC/C,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,QAAQ,GAAW,EAAE;IACb,KAAAC,UAAU,GAAW,mBAAmB,CAAC,CAAC;IAEjD,KAAAC,mBAAmB,GAAG,IAAI5B,YAAY,EAA6B;IACnE,KAAA6B,WAAW,GAAG,IAAI7B,YAAY,EAA6B;IAC3D,KAAA8B,eAAe,GAAG,IAAI9B,YAAY,EAAkC;IACpE,KAAA+B,mBAAmB,GAAG,IAAI/B,YAAY,EAA6B;IAE7E,KAAAgC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,OAAO,GAAY,KAAK;IAEhB,KAAAC,UAAU,GAAGhC,MAAM,CAACD,UAAU,CAAC;EAInC;EAEJkC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAACf,aAAa,CAAC;IACvF;IACA,MAAMgB,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACzC,IAAID,WAAW,KAAK,IAAI,EAAE;MACxB,IAAI,CAAChB,aAAa,GAAGgB,WAAW;MAChCF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACf,aAAa,CAAC;IACnE;IACA,IAAI,CAACkB,iBAAiB,EAAE;EAC1B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACP,OAAO,GAAG,IAAI;IACnB,IAAI,CAACZ,gBAAgB,CAACoB,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEC,IAAI,CAACxC,kBAAkB,CAAC,IAAI,CAAC+B,UAAU,CAAC,CAAC,CACzCU,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAACf,aAAa,GAAGc,GAAG,CAACC,OAAO;UAChC,IAAI,CAAChB,mBAAmB,CAACiB,IAAI,CAAC,IAAI,CAAChB,aAAa,CAAC;UAEjD;UACA,IAAI,IAAI,CAACV,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAK2B,SAAS,EAAE;YACnE,MAAMC,cAAc,GAAG,IAAI,CAAClB,aAAa,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACrC,GAAG,KAAK,IAAI,CAACO,aAAa,CAAC;YACvF,IAAI,CAAC4B,cAAc,EAAE;cACnB;cACAd,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACf,aAAa,CAAC;cACrE,IAAI,CAACA,aAAa,GAAG,IAAI;YAC3B,CAAC,MAAM;cACLc,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACf,aAAa,CAAC;cAC7D;cACA,MAAM+B,YAAY,GAAG,IAAI,CAAC/B,aAAa;cACvC,IAAI,CAACA,aAAa,GAAG,IAAI;cACzBgC,UAAU,CAAC,MAAK;gBACd,IAAI,CAAChC,aAAa,GAAG+B,YAAY;cACnC,CAAC,EAAE,CAAC,CAAC;YACP;UACF;UAEA;UACA,IAAI,CAAC,IAAI,CAAC/B,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAK2B,SAAS,KAAK,IAAI,CAACjB,aAAa,CAACuB,MAAM,GAAG,CAAC,EAAE;YACtG,IAAI,CAACjC,aAAa,GAAG,IAAI,CAACU,aAAa,CAAC,CAAC,CAAC,CAACjB,GAAI;YAC/C,IAAI,CAACa,mBAAmB,CAACoB,IAAI,CAAC,IAAI,CAAC1B,aAAa,CAAC;YACjD,IAAI,CAACO,WAAW,CAACmB,IAAI,CAAC,IAAI,CAAC1B,aAAa,CAAC;YACzC,IAAI,CAACQ,eAAe,CAACkB,IAAI,CAAC,IAAI,CAAChB,aAAa,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAACwB,kBAAkB,CAAC,IAAI,CAAClC,aAAa,CAAC;UAC7C,CAAC,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAK2B,SAAS,EAAE;YAC1E;YACA,MAAMQ,iBAAiB,GAAG,IAAI,CAACzB,aAAa,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACrC,GAAG,KAAK,IAAI,CAACO,aAAa,CAAC;YAC1F,IAAImC,iBAAiB,EAAE;cACrB,IAAI,CAAC3B,eAAe,CAACkB,IAAI,CAACS,iBAAiB,CAAC;YAC9C;UACF;QACF;QACA,IAAI,CAACxB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACftB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACzB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA0B,iBAAiBA,CAACC,KAAgC;IAChD,IAAI,CAACtC,aAAa,GAAGsC,KAAK;IAC1B,IAAI,CAAChC,mBAAmB,CAACoB,IAAI,CAACY,KAAK,CAAC;IACpC,IAAI,CAAC/B,WAAW,CAACmB,IAAI,CAACY,KAAK,CAAC;IAE5B;IACA,IAAI,CAACJ,kBAAkB,CAACI,KAAK,CAAC;IAE9B,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKX,SAAS,EAAE;MACzC,MAAMQ,iBAAiB,GAAG,IAAI,CAACzB,aAAa,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACrC,GAAG,KAAK6C,KAAK,CAAC;MAC7E,IAAI,CAAC9B,eAAe,CAACkB,IAAI,CAACS,iBAAiB,IAAI,IAAI,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAAC3B,eAAe,CAACkB,IAAI,CAAC,IAAI,CAAC;IACjC;EACF;EAEA;EACAa,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACvC,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAK2B,SAAS,EAAE;MACnE,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACjB,aAAa,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACrC,GAAG,KAAK,IAAI,CAACO,aAAa,CAAC,IAAI,IAAI;EACjF;EAEA;EACAwC,MAAMA,CAAA;IACJ,IAAI,CAACtB,iBAAiB,EAAE;EAC1B;EAEA;EACQD,cAAcA,CAAA;IACpB,IAAI;MACF,MAAMD,WAAW,GAAGyB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACrC,UAAU,CAAC;MACzDS,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,WAAW,CAAC;MAC7C,IAAIA,WAAW,KAAK,IAAI,EAAE;QACxB,MAAM2B,WAAW,GAAGC,QAAQ,CAAC5B,WAAW,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC6B,KAAK,CAACF,WAAW,CAAC,EAAE;UACvB7B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4B,WAAW,CAAC;UAClD,OAAOA,WAAW;QACpB;MACF,CAAC,MAAM;QACL7B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACvC;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACgC,IAAI,CAAC,wBAAwB,EAAEV,KAAK,CAAC;IAC/C;IACA,OAAO,IAAI;EACb;EAEA;EACQF,kBAAkBA,CAACI,KAAgC;IACzD,IAAI;MACF,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKX,SAAS,EAAE;QACzCc,YAAY,CAACM,OAAO,CAAC,IAAI,CAAC1C,UAAU,EAAEiC,KAAK,CAACU,QAAQ,EAAE,CAAC;QACvDlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuB,KAAK,CAAC;MAC5C,CAAC,MAAM;QACLG,YAAY,CAACQ,UAAU,CAAC,IAAI,CAAC5C,UAAU,CAAC;QACxCS,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACgC,IAAI,CAAC,wBAAwB,EAAEV,KAAK,CAAC;IAC/C;EACF;EAEA;EACAc,oBAAoBA,CAAA;IAClB,IAAI;MACFT,YAAY,CAACQ,UAAU,CAAC,IAAI,CAAC5C,UAAU,CAAC;IAC1C,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdtB,OAAO,CAACgC,IAAI,CAAC,yBAAyB,EAAEV,KAAK,CAAC;IAChD;EACF;;;uCA7JWvC,wBAAwB,EAAAV,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAxBxD,wBAAwB;MAAAyD,SAAA;MAAAC,MAAA;QAAAvD,aAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA;MAAAoD,OAAA;QAAAlD,mBAAA;QAAAC,WAAA;QAAAC,eAAA;QAAAC,mBAAA;MAAA;MAAAgD,UAAA;MAAAC,QAAA,GAAAvE,EAAA,CAAAwE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBrC9E,EAAA,CAAAC,cAAA,mBAC+C;UAA7CD,EAAA,CAAAgF,UAAA,4BAAAC,sEAAAC,MAAA;YAAA,OAAkBH,GAAA,CAAA7B,iBAAA,CAAAgC,MAAA,CAAyB;UAAA,EAAC;UAW5ClF,EARA,CAAAmF,UAAA,IAAAC,6CAAA,uBAA2E,IAAAC,6CAAA,uBAI9B,IAAAC,6CAAA,uBAI+B;UAG9EtF,EAAA,CAAAG,YAAA,EAAY;;;UAfsDH,EAAvD,CAAAI,UAAA,aAAA2E,GAAA,CAAAlE,aAAA,CAA0B,gBAAAkE,GAAA,CAAAjE,WAAA,CAA4B,aAAAiE,GAAA,CAAAhE,QAAA,IAAAgE,GAAA,CAAAvD,OAAA,CAAiC;UAI/DxB,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA2E,GAAA,CAAAxD,aAAA,CAAgB;UAIrCvB,EAAA,CAAAO,SAAA,EAAa;UAAbP,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAAvD,OAAA,CAAa;UAIbxB,EAAA,CAAAO,SAAA,EAA4C;UAA5CP,EAAA,CAAAI,UAAA,UAAA2E,GAAA,CAAAvD,OAAA,IAAAuD,GAAA,CAAAxD,aAAA,CAAAuB,MAAA,OAA4C;;;qBDAtDlD,cAAc,EAAA2F,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,iBAAA,EACd5F,cAAc,EACdF,WAAW,EACXG,KAAK,EACLC,IAAI;MAAA2F,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}