{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\nimport { ImagePreviewComponent } from '../../../shared/components/image-preview';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"imagePreviewComponent\"];\nfunction BuildingMaterialComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(60);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext();\n      const inputFile_r8 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(inputFile_r8.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.CSelectPictureId.length);\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_56_tr_1_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const dialog_r6 = i0.ɵɵreference(60);\n      return i0.ɵɵresetView(ctx_r2.onSelectedMaterial(item_r9, dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_56_tr_1_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.bindImageForMaterial(item_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \" \\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r9.CSelectName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"div\", 38);\n    i0.ɵɵtemplate(11, BuildingMaterialComponent_tbody_56_tr_1_span_11_Template, 2, 1, \"span\", 39)(12, BuildingMaterialComponent_tbody_56_tr_1_span_12_Template, 2, 0, \"span\", 40);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\")(18, \"span\", 41);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"td\", 42);\n    i0.ɵɵtemplate(21, BuildingMaterialComponent_tbody_56_tr_1_button_21_Template, 2, 0, \"button\", 43)(22, BuildingMaterialComponent_tbody_56_tr_1_button_22_Template, 3, 1, \"button\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CMaterialCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r9.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r9.CSelectPictureId && item_r9.CSelectPictureId.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.CSelectPictureId || item_r9.CSelectPictureId.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CSelectPictureId && item_r9.CSelectPictureId.length > 0 ? \"\\u5DF2\\u7D81\\u5B9A\" : \"\\u672A\\u7D81\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r9.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getStatusLabel(item_r9.CStatus || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_56_tr_1_Template, 23, 15, \"tr\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r13.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"i\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u8A2D\\u5B9A\\u5EFA\\u6750\\u4EE3\\u865F: \", ctx_r2.selectedMaterial.CMaterialCode, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 50)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 51)(4, \"h5\", 52);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 53)(7, \"div\", 54)(8, \"label\", 55);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CMaterialCode, $event) || (ctx_r2.selectedMaterial.CMaterialCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 57)(12, \"label\", 55);\n    i0.ɵɵtext(13, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CSelectName, $event) || (ctx_r2.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 57)(16, \"label\", 59);\n    i0.ɵɵtext(17, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"textarea\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_textarea_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CDescription, $event) || (ctx_r2.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 57)(20, \"label\", 55);\n    i0.ɵɵtext(21, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CPrice, $event) || (ctx_r2.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 57)(24, \"label\", 55);\n    i0.ɵɵtext(25, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_59_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedMaterial.CStatus, $event) || (ctx_r2.selectedMaterial.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, BuildingMaterialComponent_ng_template_59_nb_option_27_Template, 2, 2, \"nb-option\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 64)(29, \"label\", 59);\n    i0.ɵɵtext(30, \"\\u5716\\u7247\\u7D81\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 65)(32, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openImageBinder());\n    });\n    i0.ɵɵelement(33, \"i\", 67);\n    i0.ɵɵtext(34, \"\\u9078\\u64C7\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, BuildingMaterialComponent_ng_template_59_div_35_Template, 3, 1, \"div\", 68);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"nb-card-footer\", 29)(37, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_37_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r14));\n    });\n    i0.ɵɵtext(38, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_39_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r14));\n    });\n    i0.ɵɵtext(40, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CMaterialCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedMaterial.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"title\", \"\\u70BA\\u5EFA\\u6750\\u7D81\\u5B9A\\u5716\\u7247\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedMaterial.CMaterialCode);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    this.CMaterialCode = \"\";\n    this.ShowPrice = false;\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 簡化後只保留必要的\n    this.selectedImages = []; // 右側已選擇的圖片\n    this.imageSearchTerm = \"\";\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\n        // 不立即載入材料列表，等待建案選擇事件觸發\n      }\n    })).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        CMaterialCode: this.CMaterialCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  // 建案選擇事件處理（新）\n  onBuildCaseSelectionChange(selectedBuildCase) {\n    if (selectedBuildCase) {\n      this.selectedBuildCaseId = selectedBuildCase.cID;\n    } else if (this.listBuildCases.length > 0) {\n      this.selectedBuildCaseId = this.listBuildCases[0].cID;\n    }\n    this.search();\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1,\n      // 預設為啟用狀態\n      CPrice: 0 // 預設價格為0\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    // 使用 imagePreviewComponent 的綁定界面\n    if (this.imagePreviewComponent) {\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName;\n      // 監聽圖片綁定確認事件\n      this.imagePreviewComponent.confirmImageBinding.subscribe(selectedImages => {\n        this.onConfirmImageBinding(selectedImages);\n      });\n      this.imagePreviewComponent.openBindingInterface();\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.required('[價格]', this.selectedMaterial.CPrice);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30);\n    // 價格驗證：必須為數字且大於等於0\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\n      if (this.selectedMaterial.CPrice < 0) {\n        this.valid.errorMessages.push('[價格] 不能小於0');\n      }\n    }\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CFile: target.files[0]\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        }), mergeMap(() => this.getMaterialList(1))).subscribe();\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder() {\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    // 使用 imagePreviewComponent 的綁定界面\n    if (this.imagePreviewComponent) {\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName;\n      // 監聽圖片綁定確認事件\n      this.imagePreviewComponent.confirmImageBinding.subscribe(selectedImages => {\n        this.onConfirmImageBinding(selectedImages);\n      });\n      this.imagePreviewComponent.openBindingInterface();\n    }\n  }\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\n  previewImage(image, imagePreviewComponent, event) {\n    event.stopPropagation();\n    // 設定預覽元件參數，讓它自行載入圖片\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\n    imagePreviewComponent.pictureType = this.selectedCategory;\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\n    imagePreviewComponent.showSelectionToggle = true;\n    // 開啟預覽對話框\n    imagePreviewComponent.openPreview();\n  }\n  onConfirmImageBinding(selectedImages) {\n    if (selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = selectedImages.map(img => img.id);\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    // 清理選擇狀態\n    this.selectedImages = [];\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      viewQuery: function BuildingMaterialComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imagePreviewComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 63,\n      vars: 12,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imagePreviewComponent\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [1, \"w-[78%]\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 1, \"w-full\", 3, \"selectedValueChange\", \"selectionChange\", \"selectedValue\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u4EE3\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-danger mr-2\", 4, \"ngIf\"], [1, \"badge\"], [1, \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-danger\", \"mr-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"20\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"50\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", \"placeholder\", \"0\", \"required\", \"\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\", \"mr-2\"], [\"class\", \"text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [3, \"value\"], [1, \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"text-green-500\", \"mr-2\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"app-build-case-select\", 10);\n          i0.ɵɵtwoWayListener(\"selectedValueChange\", function BuildingMaterialComponent_Template_app_build_case_select_selectedValueChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectionChange\", function BuildingMaterialComponent_Template_app_build_case_select_selectionChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseSelectionChange($event));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 7)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 6)(19, \"div\", 7)(20, \"label\", 8);\n          i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CMaterialCode, $event) || (ctx.CMaterialCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 13)(24, \"div\", 14)(25, \"nb-checkbox\", 15);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(26, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, BuildingMaterialComponent_button_27_Template, 3, 0, \"button\", 16)(28, BuildingMaterialComponent_button_28_Template, 3, 0, \"button\", 17)(29, BuildingMaterialComponent_button_29_Template, 3, 0, \"button\", 18)(30, BuildingMaterialComponent_button_30_Template, 2, 0, \"button\", 19);\n          i0.ɵɵelementStart(31, \"input\", 20, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_33_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(34, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(35, \"i\", 22);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"table\", 24)(38, \"thead\")(39, \"tr\", 25)(40, \"th\", 26);\n          i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 26);\n          i0.ɵɵtext(43, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 26);\n          i0.ɵɵtext(45, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"th\", 27);\n          i0.ɵɵtext(47, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 26);\n          i0.ɵɵtext(49, \"\\u5DF2\\u7D81\\u5B9A\\u5716\\u7247\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 26);\n          i0.ɵɵtext(51, \"\\u50F9\\u683C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"th\", 26);\n          i0.ɵɵtext(53, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"th\", 26);\n          i0.ɵɵtext(55, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(56, BuildingMaterialComponent_tbody_56_Template, 2, 1, \"tbody\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"nb-card-footer\", 29)(58, \"ngx-pagination\", 30);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(59, BuildingMaterialComponent_ng_template_59_Template, 41, 9, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelement(61, \"app-image-preview\", null, 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtwoWayProperty(\"selectedValue\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CMaterialCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.RequiredValidator, i8.MaxLengthValidator, i8.MinValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, BuildCaseSelectComponent, ImagePreviewComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n\\n\\n\\n.picklist-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  height: 100%;\\n  min-height: 400px;\\n}\\n\\n.picklist-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  background-color: #fafafa;\\n}\\n.picklist-panel[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.picklist-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 80px;\\n  gap: 0.5rem;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0.5rem 0;\\n  border-color: #ddd;\\n}\\n\\n.image-grid-item.hover\\\\:bg-gray-50[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n.image-grid-item.border-success[_ngcontent-%COMP%] {\\n  border-color: #28a745 !important;\\n  background-color: #f8fff9;\\n}\\n.image-grid-item.bg-green-50[_ngcontent-%COMP%] {\\n  background-color: #f0fff4;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .picklist-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: auto;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: auto;\\n    min-width: 60px;\\n  }\\n  .grid.grid-cols-3[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImJ1aWxkaW5nLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUFoQjtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtBQUVGOztBQUNBO0VBQ0UsVUFBQTtBQUVGOztBQUNBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtBQUVGOztBQUVBO0VBQ0UseUJBQUE7QUFDRjtBQUNFO0VBQ0UsMkJBQUE7RUFDQSx3Q0FBQTtBQUNKO0FBRUU7RUFDRSxxQkFBQTtFQUNBLHlCQUFBO0VBQ0EsNkNBQUE7QUFBSjs7QUFJQTtFQUNFLGtCQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtBQURGO0FBR0U7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0FBREo7O0FBS0E7RUFDRSwrQkFBQTtBQUZGO0FBSUU7RUFDRSxzQkFBQTtBQUZKOztBQU1BO0VBQ0UsaUJBQUE7RUFDQSwyQkFBQTtFQUNBLGtCQUFBO0VBRUEsWUFBQTtFQW1CQSxrQkFBQTtFQUNBLGlDQUFBO0VBQ0EscUJBQUE7RUFDQSxnQ0FBQTtBQXRCRjtBQUNFO0VBQ0UsVUFBQTtBQUNKO0FBRUU7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0FBQUo7QUFHRTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFESjtBQUdJO0VBQ0UsbUJBQUE7QUFETjs7QUFXQTtFQUNFLGlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5Q0FBQTtBQVJGO0FBVUU7RUFDRSxhQUFBO0VBQ0EscUJBQUE7RUFDQSw2Q0FBQTtBQVJKOztBQVlBO0VBQ0UsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQ0FBQTtBQVRGO0FBV0U7RUFDRSwyQkFBQTtBQVRKO0FBWUU7RUFDRSxnQkFBQTtFQUNBLGVBQUE7QUFWSjs7QUFjQSxvQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0FBWEY7O0FBY0E7RUFDRSxPQUFBO0VBQ0EsYUFBQTtBQVhGOztBQWNBLGdCQUFBO0FBRUU7RUFDRSx3QkFBQTtFQUNBLDJCQUFBO0VBQ0Esd0JBQUE7RUFDQSxpQ0FBQTtBQVpKOztBQWdCQSxpQkFBQTtBQUNBO0VBQ0UsY0FBQTtBQWJGOztBQWdCQSxhQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7RUFDQSw2QkFBQTtFQUNBLDZCQUFBO0FBYkY7O0FBZ0JBLHFCQUFBO0FBQ0E7RUFDRSx1QkFBQTtBQWJGOztBQWdCQSwwQkFBQTtBQUVFO0VBQ0UsMkJBQUE7RUFDQSx1QkFBQTtBQWRKOztBQWtCQSxrQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7QUFmRjs7QUFrQkE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtBQWZGO0FBaUJFO0VBQ0Usc0JBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFmSjs7QUFtQkE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7QUFoQkY7QUFrQkU7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQWhCSjtBQWtCSTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQWhCTjtBQW9CRTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBbEJKOztBQXVCRTtFQUNFLHlCQUFBO0FBcEJKO0FBdUJFO0VBQ0UsZ0NBQUE7RUFDQSx5QkFBQTtBQXJCSjtBQXdCRTtFQUNFLHlCQUFBO0FBdEJKOztBQTJCRTtFQUNFLHlCQUFBO0VBQ0EsWUFBQTtBQXhCSjtBQTJCRTtFQUNFLHlCQUFBO0VBQ0EsWUFBQTtBQXpCSjs7QUE2QkEsVUFBQTtBQUNBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLFdBQUE7RUExQkY7RUE2QkE7SUFDRSxtQkFBQTtJQUNBLFdBQUE7SUFDQSxZQUFBO0VBM0JGO0VBNkJFO0lBQ0UsV0FBQTtJQUNBLGVBQUE7RUEzQko7RUErQkE7SUFDRSxxQ0FBQTtFQTdCRjtBQUNGIiwiZmlsZSI6ImJ1aWxkaW5nLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmltYWdlLXRhYmxlIHtcclxuICB3aWR0aDogNTBweDtcclxuICBoZWlnaHQ6IDUwcHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG4uZW1wdHktaW1hZ2Uge1xyXG4gIGNvbG9yOiByZWQ7XHJcbn1cclxuXHJcbi5maXQtc2l6ZSB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG4gIG1heC13aWR0aDogMTAwJTtcclxuICBtYXgtaGVpZ2h0OiA1MDBweDtcclxuICBvYmplY3QtZml0OiBjb250YWluO1xyXG59XHJcblxyXG4vLyDlnJbniYfntoHlrprlip/og73mqKPlvI9cclxuLmltYWdlLWdyaWQtaXRlbSB7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIH1cclxuXHJcbiAgJi5zZWxlY3RlZCB7XHJcbiAgICBib3JkZXItY29sb3I6ICMzMzY2ZmY7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmN2ZmO1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoNTEsIDEwMiwgMjU1LCAwLjIpO1xyXG4gIH1cclxufVxyXG5cclxuLmltYWdlLWNoZWNrYm94IHtcclxuICBib3JkZXItY29sb3I6ICNjY2M7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgJi5jaGVja2VkIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMzMzY2ZmY7XHJcbiAgICBib3JkZXItY29sb3I6ICMzMzY2ZmY7XHJcbiAgfVxyXG59XHJcblxyXG4uaW1hZ2UtdGh1bWJuYWlsIHtcclxuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XHJcbiAgfVxyXG59XHJcblxyXG4uaW1hZ2UtcHJldmlldy1jb250YWluZXIge1xyXG4gIG1heC1oZWlnaHQ6IDQ4MHB4O1xyXG4gIG92ZXJmbG93LXk6IGF1dG8gIWltcG9ydGFudDtcclxuICBvdmVyZmxvdy14OiBoaWRkZW47XHJcblxyXG4gIC8qIOiHquWumue+qea7kei7jOaoo+W8jyAqL1xyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgIHdpZHRoOiA4cHg7XHJcbiAgfVxyXG5cclxuICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZjFmMWYxO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIH1cclxuXHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xyXG4gICAgYmFja2dyb3VuZDogI2MxYzFjMTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogI2E4YThhODtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qIOeiuuS/neWcqOWQhOeorueAj+imveWZqOS4remDveiDvea7keWLlSAqL1xyXG4gIC13ZWJraXQtb3ZlcmZsb3ctc2Nyb2xsaW5nOiB0b3VjaDtcclxuICBzY3JvbGxiYXItd2lkdGg6IHRoaW47XHJcbiAgc2Nyb2xsYmFyLWNvbG9yOiAjYzFjMWMxICNmMWYxZjE7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQge1xyXG4gIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgMC4ycyBlYXNlLWluLW91dDtcclxuXHJcbiAgJjpmb2N1cyB7XHJcbiAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMzM2NmZmO1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoNTEsIDEwMiwgMjU1LCAwLjEpO1xyXG4gIH1cclxufVxyXG5cclxuLmJ0bi1pbWFnZS1hY3Rpb24ge1xyXG4gIHBhZGRpbmc6IDRweCA4cHg7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgfVxyXG5cclxuICAmLmJ0bi14cyB7XHJcbiAgICBwYWRkaW5nOiAycHggNnB4O1xyXG4gICAgZm9udC1zaXplOiAxMHB4O1xyXG4gIH1cclxufVxyXG5cclxuLyog56K65L+dIEZsZXhib3gg5q2j56K66YGL5L2cICovXHJcbi5kLWZsZXguZmxleC1jb2x1bW4ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLmZsZXgtMSB7XHJcbiAgZmxleDogMTtcclxuICBtaW4taGVpZ2h0OiAwO1xyXG59XHJcblxyXG4vKiDlnJbniYfntoHlrprlsI3oqbHmoYbnibnlrprmqKPlvI8gKi9cclxubmItY2FyZC53LVxcWzkwMHB4XFxdIHtcclxuICAubmItY2FyZC1ib2R5IHtcclxuICAgIGhlaWdodDogY2FsYyg3MDBweCAtIDEyMHB4KSAhaW1wb3J0YW50O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbiAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG5cclxuLyog56K65L+d5ruR5YuV5a655Zmo5Y+v5Lul5q2j5bi46YGL5L2cICovXHJcbi5mbGV4LXNocmluay0wIHtcclxuICBmbGV4LXNocmluazogMDtcclxufVxyXG5cclxuLyog5by35Yi25ruR5YuV5a655Zmo5qij5byPICovXHJcbi5pbWFnZS1wcmV2aWV3LWNvbnRhaW5lci5mbGV4LTEge1xyXG4gIGZsZXg6IDEgMSBhdXRvO1xyXG4gIG1pbi1oZWlnaHQ6IDA7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG4gIG92ZXJmbG93LXk6IHNjcm9sbCAhaW1wb3J0YW50O1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4vKiBHcmlkIOWuueWZqOeiuuS/neWFp+WuueiDveWkoOiiq+a7keWLlSAqL1xyXG4uZ3JpZC5ncmlkLWNvbHMtNCB7XHJcbiAgbWluLWhlaWdodDogbWluLWNvbnRlbnQ7XHJcbn1cclxuXHJcbi8qIOeiuuS/neWcqCBBbmd1bGFyIOadkOizquioreioiOS4reato+W4uOmBi+S9nCAqL1xyXG46Om5nLWRlZXAgbmItY2FyZC1ib2R5IHtcclxuICAuaW1hZ2UtcHJldmlldy1jb250YWluZXIge1xyXG4gICAgbWF4LWhlaWdodDogbm9uZSAhaW1wb3J0YW50O1xyXG4gICAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBQaWNrbGlzdCDnibnlrprmqKPlvI8gKi9cclxuLnBpY2tsaXN0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBnYXA6IDFyZW07XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIG1pbi1oZWlnaHQ6IDQwMHB4O1xyXG59XHJcblxyXG4ucGlja2xpc3QtcGFuZWwge1xyXG4gIGZsZXg6IDE7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHBhZGRpbmc6IDFyZW07XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTtcclxuXHJcbiAgaDYge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuICAgIGNvbG9yOiAjMzMzO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICB9XHJcbn1cclxuXHJcbi5waWNrbGlzdC1jb250cm9scyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgd2lkdGg6IDgwcHg7XHJcbiAgZ2FwOiAwLjVyZW07XHJcblxyXG4gIC5idG4ge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBtaW4taGVpZ2h0OiAzNnB4O1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuXHJcbiAgICAmOmRpc2FibGVkIHtcclxuICAgICAgb3BhY2l0eTogMC41O1xyXG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgaHIge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBtYXJnaW46IDAuNXJlbSAwO1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjZGRkO1xyXG4gIH1cclxufVxyXG5cclxuLmltYWdlLWdyaWQtaXRlbSB7XHJcbiAgJi5ob3ZlclxcOmJnLWdyYXktNTA6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcclxuICB9XHJcblxyXG4gICYuYm9yZGVyLXN1Y2Nlc3Mge1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMjhhNzQ1ICFpbXBvcnRhbnQ7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmZmY5O1xyXG4gIH1cclxuXHJcbiAgJi5iZy1ncmVlbi01MCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmZmY0O1xyXG4gIH1cclxufVxyXG5cclxuLmJhZGdlIHtcclxuICAmLmJhZGdlLXN1Y2Nlc3Mge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzI4YTc0NTtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICB9XHJcblxyXG4gICYuYmFkZ2UtaW5mbyB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTdhMmI4O1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG4gIH1cclxufVxyXG5cclxuLyog6Z+/5oeJ5byP6Kit6KiIICovXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gIC5waWNrbGlzdC1jb250YWluZXIge1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGdhcDogMC41cmVtO1xyXG4gIH1cclxuXHJcbiAgLnBpY2tsaXN0LWNvbnRyb2xzIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGhlaWdodDogYXV0bztcclxuXHJcbiAgICAuYnRuIHtcclxuICAgICAgd2lkdGg6IGF1dG87XHJcbiAgICAgIG1pbi13aWR0aDogNjBweDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5ncmlkLmdyaWQtY29scy0zIHtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XHJcbiAgfVxyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "BuildCaseSelectComponent", "ImagePreviewComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "BuildingMaterialComponent_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "BuildingMaterialComponent_button_28_Template_button_click_0_listener", "_r4", "search", "BuildingMaterialComponent_button_29_Template_button_click_0_listener", "_r5", "dialog_r6", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_30_Template_button_click_0_listener", "_r7", "inputFile_r8", "click", "ɵɵadvance", "ɵɵtextInterpolate", "item_r9", "CSelectPictureId", "length", "BuildingMaterialComponent_tbody_56_tr_1_button_21_Template_button_click_0_listener", "_r10", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_56_tr_1_button_22_Template_button_click_0_listener", "_r11", "bindImageForMaterial", "ɵɵproperty", "CSelectName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_56_tr_1_span_11_Template", "BuildingMaterialComponent_tbody_56_tr_1_span_12_Template", "BuildingMaterialComponent_tbody_56_tr_1_button_21_Template", "BuildingMaterialComponent_tbody_56_tr_1_button_22_Template", "CId", "CMaterialCode", "ɵɵstyleMap", "CIsMapping", "CDescription", "CPrice", "ɵɵclassMap", "CStatus", "ɵɵtextInterpolate1", "getStatusLabel", "isRead", "BuildingMaterialComponent_tbody_56_tr_1_Template", "materialList", "option_r13", "value", "label", "selectedMaterial", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_10_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_59_Template_textarea_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_59_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_59_Template_nb_select_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_59_nb_option_27_Template", "BuildingMaterialComponent_ng_template_59_Template_button_click_32_listener", "openImageBinder", "BuildingMaterialComponent_ng_template_59_div_35_Template", "BuildingMaterialComponent_ng_template_59_Template_button_click_37_listener", "ref_r14", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_59_Template_button_click_39_listener", "onSubmit", "ɵɵtwoWayProperty", "statusOptions", "PictureCategory", "BuildingMaterialComponent", "status", "option", "find", "opt", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "ShowPrice", "filterMapping", "selectedImages", "imageSearchTerm", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "selectedCate<PERSON><PERSON>", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "pipe", "res", "StatusCode", "Entries", "subscribe", "getMaterialList", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "selectedBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "CShowPrice", "onBuildCaseSelectionChange", "selectedBuildCase", "cID", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "imagePreviewComponent", "buildCaseId", "materialId", "pictureType", "materialName", "confirmImageBinding", "onConfirmImageBinding", "openBindingInterface", "validation", "clear", "required", "isStringMaxLength", "undefined", "errorMessages", "push", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "close", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "utils", "sheet_to_json", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "currentImageShowing", "changeFilter", "previewImage", "image", "stopPropagation", "searchTerm", "showSelectionToggle", "openPreview", "map", "img", "id", "saveImageBinding", "categoryChanged", "category", "getCategoryLabel", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "PictureService", "selectors", "viewQuery", "BuildingMaterialComponent_Query", "rf", "ctx", "BuildingMaterialComponent_Template_app_build_case_select_selectedValueChange_12_listener", "_r1", "BuildingMaterialComponent_Template_app_build_case_select_selectionChange_12_listener", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_25_listener", "BuildingMaterialComponent_button_27_Template", "BuildingMaterialComponent_button_28_Template", "BuildingMaterialComponent_button_29_Template", "BuildingMaterialComponent_button_30_Template", "BuildingMaterialComponent_Template_input_change_31_listener", "BuildingMaterialComponent_Template_button_click_33_listener", "BuildingMaterialComponent_tbody_56_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_58_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_58_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_58_listener", "BuildingMaterialComponent_ng_template_59_Template", "ɵɵtemplateRefExtractor", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "RequiredValidator", "MaxLengthValidator", "MinValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\r\nimport { ImagePreviewComponent, ImageItem } from '../../../shared/components/image-preview';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('imagePreviewComponent', { static: false }) imagePreviewComponent!: ImagePreviewComponent;\r\n  \r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 簡化後只保留必要的\r\n  selectedImages: ImageItem[] = [] // 右側已選擇的圖片\r\n  imageSearchTerm: string = \"\"\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\r\n            // 不立即載入材料列表，等待建案選擇事件觸發\r\n          }\r\n        })\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        CMaterialCode: this.CMaterialCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  // 建案選擇事件處理（新）\r\n  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {\r\n    if (selectedBuildCase) {\r\n      this.selectedBuildCaseId = selectedBuildCase.cID!;\r\n    } else if (this.listBuildCases.length > 0) {\r\n      this.selectedBuildCaseId = this.listBuildCases[0].cID!;\r\n    }\r\n    this.search();\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1, // 預設為啟用狀態\r\n      CPrice: 0   // 預設價格為0\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse) {\r\n    this.selectedMaterial = { ...data }\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName;\r\n      \r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n      \r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.required('[價格]', this.selectedMaterial.CPrice)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\r\n    // 價格驗證：必須為數字且大於等於0\r\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\r\n      if (this.selectedMaterial.CPrice < 0) {\r\n        this.valid.errorMessages.push('[價格] 不能小於0')\r\n      }\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n\r\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CFile: target.files[0]\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG(\"執行成功\")\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n          }),\r\n          mergeMap(() => this.getMaterialList(1))\r\n        ).subscribe();\r\n\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder() {\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName;\r\n      \r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n      \r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n  }\r\n\r\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\r\n  previewImage(image: ImageItem, imagePreviewComponent: ImagePreviewComponent, event: Event) {\r\n    event.stopPropagation();\r\n\r\n    // 設定預覽元件參數，讓它自行載入圖片\r\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\r\n    imagePreviewComponent.pictureType = this.selectedCategory;\r\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\r\n    imagePreviewComponent.showSelectionToggle = true;\r\n\r\n    // 開啟預覽對話框\r\n    imagePreviewComponent.openPreview();\r\n  }\r\n\r\n  onConfirmImageBinding(selectedImages: ImageItem[]) {\r\n    if (selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = selectedImages.map(img => img.id);\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    // 清理選擇狀態\r\n    this.selectedImages = [];\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  // 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label> \r\n          <div class=\"w-[78%]\">\r\n            <app-build-case-select \r\n              [(selectedValue)]=\"selectedBuildCaseId\"\r\n              (selectionChange)=\"onBuildCaseSelectionChange($event)\"\r\n              placeholder=\"請選擇建案\"\r\n              class=\"w-full\">\r\n            </app-build-case-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\" maxlength=\"50\">\r\n        </div>\r\n      </div>\r\n      <!-- 啟用建材代號欄位 -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號</label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CMaterialCode\" class=\"w-full\" maxlength=\"20\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">建材代號</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\">已綁定圖片</th>\r\n            <th scope=\"col\" class=\"col-1\">價格</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CMaterialCode || '待設定' }}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td>\r\n              <div class=\"d-flex align-items-center\">\r\n                <span *ngIf=\"item.CSelectPictureId && item.CSelectPictureId.length > 0\"\r\n                  class=\"badge badge-success mr-2\">{{ item.CSelectPictureId.length }}</span>\r\n                <span *ngIf=\"!item.CSelectPictureId || item.CSelectPictureId.length === 0\"\r\n                  class=\"badge badge-danger mr-2\">0</span>\r\n                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>\r\n              </div>\r\n            </td>\r\n            <td>{{ item.CPrice}}</td>\r\n            <td>\r\n              <span class=\"badge\" [class]=\"item.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ getStatusLabel(item.CStatus || 0) }}\r\n              </span>\r\n            </td>\r\n            <td class=\"w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button> <button class=\"btn btn-outline-info btn-sm m-1\"\r\n                (click)=\"bindImageForMaterial(item)\" *ngIf=\"isRead\"\r\n                [title]=\"'為 ' + item.CSelectName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i> 綁定\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 啟用建材代號欄位 -->\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"20\"\r\n            [(ngModel)]=\"selectedMaterial.CMaterialCode\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"50\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" min=\"0\" step=\"0.01\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" placeholder=\"0\" required />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">狀態</label>\r\n          <nb-select [(ngModel)]=\"selectedMaterial.CStatus\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <!-- 圖片綁定按鈕 -->\r\n        <div class=\"flex items-center mt-4 pt-3 border-t border-gray-200\">\r\n          <label class=\"w-[150px]\">圖片綁定</label>\r\n          <div class=\"flex gap-2 w-full\">\r\n            <button type=\"button\" class=\"btn btn-outline-info btn-sm\" (click)=\"openImageBinder()\"\r\n              [title]=\"'為建材綁定圖片'\">\r\n              <i class=\"fas fa-images mr-2\"></i>選擇圖片\r\n            </button>\r\n            <div class=\"text-sm text-gray-600 flex items-center\" *ngIf=\"selectedMaterial.CMaterialCode\">\r\n              <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\r\n              已設定建材代號: {{ selectedMaterial.CMaterialCode }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n\r\n<!-- 圖片預覽元件 -->\r\n<app-image-preview \r\n  #imagePreviewComponent>\r\n</app-image-preview>\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,qBAAqB,QAAmB,0CAA0C;;;;;;;;;;;;;;;;ICoCjFC,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAE,UAAA,mBAAAC,qEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,oBAAG;IAAAV,EAAA,CAAAW,SAAA,YAC5D;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC9CZ,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAE,UAAA,mBAAAW,qEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,MAAA,EAAQ;IAAA,EAAC;IACjFf,EAAA,CAAAU,MAAA,qBAAG;IAAAV,EAAA,CAAAW,SAAA,YAA6B;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC3CZ,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAE,UAAA,mBAAAc,qEAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAAClB,EAAA,CAAAU,MAAA,gCAAK;IAAAV,EAAA,CAAAW,SAAA,YAC/D;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IACrCZ,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmB,qEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAAtB,EAAA,CAAAO,aAAA;MAAA,MAAAgB,YAAA,GAAAvB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASe,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAExB,EAAA,CAAAU,MAAA,iCAAK;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;;IA6B7FZ,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAU,MAAA,GAAkC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;IAAzCZ,EAAA,CAAAyB,SAAA,EAAkC;IAAlCzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,CAAAC,MAAA,CAAkC;;;;;IACrE7B,EAAA,CAAAC,cAAA,eACkC;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;;;IAW5CZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAE,UAAA,mBAAA4B,mFAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAJ,OAAA,GAAA3B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,kBAAA,CAAAN,OAAA,EAAAT,SAAA,CAAgC;IAAA,EAAC;IAC5ElB,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;;;IAACZ,EAAA,CAAAC,cAAA,iBAEiB;IAD5CD,EAAA,CAAAE,UAAA,mBAAAgC,mFAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,IAAA;MAAA,MAAAR,OAAA,GAAA3B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,oBAAA,CAAAT,OAAA,CAA0B;IAAA,EAAC;IAEpC3B,EAAA,CAAAW,SAAA,YAA6B;IAACX,EAAA,CAAAU,MAAA,qBAChC;IAAAV,EAAA,CAAAY,YAAA,EAAS;;;;IAFPZ,EAAA,CAAAqC,UAAA,sBAAAV,OAAA,CAAAW,WAAA,+BAA2C;;;;;IAvB/CtC,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAU,MAAA,GAAa;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAC1CZ,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAC7EZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAE7BZ,EADF,CAAAC,cAAA,SAAI,eACqC;IAGrCD,EAFA,CAAAuC,UAAA,KAAAC,wDAAA,mBACmC,KAAAC,wDAAA,mBAED;IAClCzC,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiF;IAE3FV,EAF2F,CAAAY,YAAA,EAAO,EAC1F,EACH;IACLZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAgB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAEvBZ,EADF,CAAAC,cAAA,UAAI,gBACqF;IACrFD,EAAA,CAAAU,MAAA,IACF;IACFV,EADE,CAAAY,YAAA,EAAO,EACJ;IACLZ,EAAA,CAAAC,cAAA,cAAiB;IAEcD,EAD7B,CAAAuC,UAAA,KAAAG,0DAAA,qBACiB,KAAAC,0DAAA,qBAE6B;IAIlD3C,EADE,CAAAY,YAAA,EAAK,EACF;;;;;IA3BCZ,EAAA,CAAAyB,SAAA,GAAa;IAAbzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAiB,GAAA,CAAa;IACb5C,EAAA,CAAAyB,SAAA,GAAiC;IAAjCzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAkB,aAAA,yBAAiC;IACjC7C,EAAA,CAAAyB,SAAA,EAA8C;IAA9CzB,EAAA,CAAA8C,UAAA,EAAAnB,OAAA,CAAAoB,UAAA,qBAA8C;IAAC/C,EAAA,CAAAyB,SAAA,EAAqB;IAArBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAW,WAAA,CAAqB;IACpEtC,EAAA,CAAAyB,SAAA,GAAsB;IAAtBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAqB,YAAA,CAAsB;IAGfhD,EAAA,CAAAyB,SAAA,GAA+D;IAA/DzB,EAAA,CAAAqC,UAAA,SAAAV,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,KAA+D;IAE/D7B,EAAA,CAAAyB,SAAA,EAAkE;IAAlEzB,EAAA,CAAAqC,UAAA,UAAAV,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,OAAkE;IAEnE7B,EAAA,CAAAyB,SAAA,GAAiF;IAAjFzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,IAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA,mDAAiF;IAGvF7B,EAAA,CAAAyB,SAAA,GAAgB;IAAhBzB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAsB,MAAA,CAAgB;IAEEjD,EAAA,CAAAyB,SAAA,GAAkE;IAAlEzB,EAAA,CAAAkD,UAAA,CAAAvB,OAAA,CAAAwB,OAAA,6CAAkE;IACpFnD,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAoD,kBAAA,MAAA9C,MAAA,CAAA+C,cAAA,CAAA1B,OAAA,CAAAwB,OAAA,YACF;IAIGnD,EAAA,CAAAyB,SAAA,GAAY;IAAZzB,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAgD,MAAA,CAAY;IACyBtD,EAAA,CAAAyB,SAAA,EAAY;IAAZzB,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAgD,MAAA,CAAY;;;;;IAxB1DtD,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAAuC,UAAA,IAAAgB,gDAAA,mBAAsD;IA6BxDvD,EAAA,CAAAY,YAAA,EAAQ;;;;IA7BeZ,EAAA,CAAAyB,SAAA,EAAkB;IAAlBzB,EAAA,CAAAqC,UAAA,YAAA/B,MAAA,CAAAkD,YAAA,CAAkB;;;;;IA0ErCxD,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAY;;;;IAFoCZ,EAAA,CAAAqC,UAAA,UAAAoB,UAAA,CAAAC,KAAA,CAAsB;IACpE1D,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAoD,kBAAA,MAAAK,UAAA,CAAAE,KAAA,MACF;;;;;IAYA3D,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAW,SAAA,YAAuD;IACvDX,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;IADJZ,EAAA,CAAAyB,SAAA,GACF;IADEzB,EAAA,CAAAoD,kBAAA,kDAAA9C,MAAA,CAAAsD,gBAAA,CAAAf,aAAA,MACF;;;;;;IAjDR7C,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAU,MAAA,4DACF;IAAAV,EAAA,CAAAY,YAAA,EAAiB;IAEfZ,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAU,MAAA,+EAAY;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAInCZ,EAHJ,CAAAC,cAAA,cAAyB,cAEQ,gBACW;IAAAD,EAAA,CAAAU,MAAA,+BAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACpDZ,EAAA,CAAAC,cAAA,iBACiD;IAA/CD,EAAA,CAAA6D,gBAAA,2BAAAC,kFAAAC,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiE,kBAAA,CAAA3D,MAAA,CAAAsD,gBAAA,CAAAf,aAAA,EAAAkB,MAAA,MAAAzD,MAAA,CAAAsD,gBAAA,CAAAf,aAAA,GAAAkB,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAA4C;IAChD/D,EAFE,CAAAY,YAAA,EACiD,EAC7C;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,4CAAM;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACtDZ,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAA6D,gBAAA,2BAAAK,kFAAAH,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiE,kBAAA,CAAA3D,MAAA,CAAAsD,gBAAA,CAAAtB,WAAA,EAAAyB,MAAA,MAAAzD,MAAA,CAAAsD,gBAAA,CAAAtB,WAAA,GAAAyB,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAA0C;IAC9C/D,EAFE,CAAAY,YAAA,EAC+C,EAC3C;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACrCZ,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAA6D,gBAAA,2BAAAM,qFAAAJ,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiE,kBAAA,CAAA3D,MAAA,CAAAsD,gBAAA,CAAAZ,YAAA,EAAAe,MAAA,MAAAzD,MAAA,CAAAsD,gBAAA,CAAAZ,YAAA,GAAAe,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAA2C;IAE/D/D,EADmE,CAAAY,YAAA,EAAW,EACxE;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClDZ,EAAA,CAAAC,cAAA,iBACmE;IAAjED,EAAA,CAAA6D,gBAAA,2BAAAO,kFAAAL,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiE,kBAAA,CAAA3D,MAAA,CAAAsD,gBAAA,CAAAX,MAAA,EAAAc,MAAA,MAAAzD,MAAA,CAAAsD,gBAAA,CAAAX,MAAA,GAAAc,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAAqC;IACzC/D,EAFE,CAAAY,YAAA,EACmE,EAC/D;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClDZ,EAAA,CAAAC,cAAA,qBAAiE;IAAtDD,EAAA,CAAA6D,gBAAA,2BAAAQ,sFAAAN,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiE,kBAAA,CAAA3D,MAAA,CAAAsD,gBAAA,CAAAT,OAAA,EAAAY,MAAA,MAAAzD,MAAA,CAAAsD,gBAAA,CAAAT,OAAA,GAAAY,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAAsC;IAC/C/D,EAAA,CAAAuC,UAAA,KAAA+B,8DAAA,wBAAuE;IAI3EtE,EADE,CAAAY,YAAA,EAAY,EACR;IAIJZ,EADF,CAAAC,cAAA,eAAkE,iBACvC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAEnCZ,EADF,CAAAC,cAAA,eAA+B,kBAEP;IADoCD,EAAA,CAAAE,UAAA,mBAAAqE,2EAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,eAAA,EAAiB;IAAA,EAAC;IAEnFxE,EAAA,CAAAW,SAAA,aAAkC;IAAAX,EAAA,CAAAU,MAAA,iCACpC;IAAAV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAuC,UAAA,KAAAkC,wDAAA,kBAA4F;IAOpGzE,EAHM,CAAAY,YAAA,EAAM,EACF,EACF,EACO;IAEbZ,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAE,UAAA,mBAAAwE,2EAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAI,aAAA,CAAA4D,IAAA,EAAAY,SAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuE,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC3E,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAY,YAAA,EAAS;IAC7EZ,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAE,UAAA,mBAAA4E,2EAAA;MAAA,MAAAH,OAAA,GAAA3E,EAAA,CAAAI,aAAA,CAAA4D,IAAA,EAAAY,SAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyE,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAC3E,EAAA,CAAAU,MAAA,oBAAE;IAErEV,EAFqE,CAAAY,YAAA,EAAS,EAC3D,EACT;;;;IAhDAZ,EAAA,CAAAyB,SAAA,IAA4C;IAA5CzB,EAAA,CAAAgF,gBAAA,YAAA1E,MAAA,CAAAsD,gBAAA,CAAAf,aAAA,CAA4C;IAK5C7C,EAAA,CAAAyB,SAAA,GAA0C;IAA1CzB,EAAA,CAAAgF,gBAAA,YAAA1E,MAAA,CAAAsD,gBAAA,CAAAtB,WAAA,CAA0C;IAK1BtC,EAAA,CAAAyB,SAAA,GAA2C;IAA3CzB,EAAA,CAAAgF,gBAAA,YAAA1E,MAAA,CAAAsD,gBAAA,CAAAZ,YAAA,CAA2C;IAAChD,EAAA,CAAAqC,UAAA,WAAU;IAMtErC,EAAA,CAAAyB,SAAA,GAAqC;IAArCzB,EAAA,CAAAgF,gBAAA,YAAA1E,MAAA,CAAAsD,gBAAA,CAAAX,MAAA,CAAqC;IAK5BjD,EAAA,CAAAyB,SAAA,GAAsC;IAAtCzB,EAAA,CAAAgF,gBAAA,YAAA1E,MAAA,CAAAsD,gBAAA,CAAAT,OAAA,CAAsC;IACjBnD,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAAqC,UAAA,YAAA/B,MAAA,CAAA2E,aAAA,CAAgB;IAW5CjF,EAAA,CAAAyB,SAAA,GAAmB;IAAnBzB,EAAA,CAAAqC,UAAA,uDAAmB;IAGiCrC,EAAA,CAAAyB,SAAA,GAAoC;IAApCzB,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAsD,gBAAA,CAAAf,aAAA,CAAoC;;;ADtJtG;AACA,IAAKqC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAcpB,OAAM,MAAOC,yBAA0B,SAAQtF,aAAa;EAmD1D;EACAwD,cAAcA,CAAC+B,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACJ,aAAa,CAACK,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAK0B,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAAC1B,KAAK,GAAG,KAAK;EACtC;EAEA6B,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA9DzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEzC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAAyC,iBAAiB,GAAG,IAAI;IAC9B,KAAA9D,WAAW,GAAW,EAAE;IACxB,KAAAO,aAAa,GAAW,EAAE;IAC1B,KAAAwD,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAvD,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAwD,cAAc,GAAgB,EAAE,EAAC;IACjC,KAAAC,eAAe,GAAW,EAAE;IAE5B;IACA,KAAAC,eAAe,GAAG,CAChB;MAAE/C,KAAK,EAAEwB,eAAe,CAACwB,iBAAiB;MAAE/C,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAEwB,eAAe,CAACyB,SAAS;MAAEhD,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAiD,gBAAgB,GAAoB1B,eAAe,CAACwB,iBAAiB;IACrE,KAAAG,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAA3B,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAAD,aAAa,GAAG,CAAC;MACfvB,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAESmD,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClB,iBAAiB,CAACmB,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACd/D,OAAO,EAAE;;KAEZ,CAAC,CACCgE,IAAI,CACHzH,GAAG,CAAC0H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnB,cAAc,GAAGkB,GAAG,CAACE,OAAO,EAAEzF,MAAM,GAAGuF,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D;QACA;MACF;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACjB;EAAEC,eAAeA,CAACC,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAAC3B,gBAAgB,CAAC4B,mCAAmC,CAAC;MAC/DT,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtCC,QAAQ,EAAE,IAAI,CAACzB,iBAAiB;QAChC9D,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BO,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCiF,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEP,SAAS;QACpB1E,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACoE,IAAI,CACLzH,GAAG,CAAC0H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC7D,YAAY,GAAG4D,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAAC1E,YAAY,CAAC3B,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACwE,SAAS,GAAG,IAAI,CAAC7C,YAAY,CAAC,CAAC,CAAC,CAAC2E,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA;EACAC,0BAA0BA,CAACC,iBAAiD;IAC1E,IAAIA,iBAAiB,EAAE;MACrB,IAAI,CAACT,mBAAmB,GAAGS,iBAAiB,CAACC,GAAI;IACnD,CAAC,MAAM,IAAI,IAAI,CAACpC,cAAc,CAACrE,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC+F,mBAAmB,GAAG,IAAI,CAAC1B,cAAc,CAAC,CAAC,CAAC,CAACoC,GAAI;IACxD;IACA,IAAI,CAACvH,MAAM,EAAE;EACf;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACyG,eAAe,EAAE,CAACD,SAAS,EAAE;EACpC;EAEAgB,WAAWA,CAACd,SAAiB;IAC3B,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACF,SAAS,EAAE;EAC7C;EAEA9G,sBAAsBA,CAAA;IACpB,IAAI,CAACqF,gBAAgB,CAAC0C,2CAA2C,CAAC;MAChEvB,IAAI,EAAE,IAAI,CAACW;KACZ,CAAC,CAACL,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE;UACzB,IAAI,CAAC1C,eAAe,CAAC2C,iBAAiB,CAACtB,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAAC7C,gBAAgB,CAAC8C,+CAA+C,CAAC;MACpE3B,IAAI,EAAE,IAAI,CAACW;KACZ,CAAC,CAACL,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE;UACzB,IAAI,CAAC1C,eAAe,CAAC2C,iBAAiB,CAACtB,GAAG,CAACE,OAAQ,CAACmB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACArH,MAAMA,CAACyH,GAAQ;IACb,IAAI,CAAC5C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACrC,gBAAgB,GAAG;MACtBT,OAAO,EAAE,CAAC;MAAE;MACZF,MAAM,EAAE,CAAC,CAAG;KACb;IACD,IAAI,CAACyC,aAAa,CAACoD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACA5G,kBAAkBA,CAAC8G,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAAC5C,KAAK,GAAG,KAAK;IAClB,IAAI,CAACrC,gBAAgB,GAAG;MAAE,GAAGmF;IAAI,CAAE;IACnC,IAAI,CAACrD,aAAa,CAACoD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACAzG,oBAAoBA,CAAC2G,IAA6B;IAChD,IAAI,CAACnF,gBAAgB,GAAG;MAAE,GAAGmF;IAAI,CAAE;IACnC;IACA,IAAI,CAACxC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,IAAI,CAACwC,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAACrB,mBAAmB;MACjE,IAAI,CAACoB,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACtF,gBAAgB,CAAChB,GAAG;MACjE,IAAI,CAACoG,qBAAqB,CAACG,WAAW,GAAG,IAAI,CAACvC,gBAAgB;MAC9D,IAAI,CAACoC,qBAAqB,CAACI,YAAY,GAAG,IAAI,CAACxF,gBAAgB,CAACtB,WAAW;MAE3E;MACA,IAAI,CAAC0G,qBAAqB,CAACK,mBAAmB,CAAC9B,SAAS,CAAEhB,cAA2B,IAAI;QACvF,IAAI,CAAC+C,qBAAqB,CAAC/C,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAACyC,qBAAqB,CAACO,oBAAoB,EAAE;IACnD;EACF;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC5D,KAAK,CAAC6D,KAAK,EAAE;IAElB,IAAI,CAAC7D,KAAK,CAAC8D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9F,gBAAgB,CAACtB,WAAW,CAAC;IAClE;IACA,IAAI,CAACsD,KAAK,CAAC8D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9F,gBAAgB,CAACf,aAAa,CAAC;IAClE,IAAI,CAAC+C,KAAK,CAAC8D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9F,gBAAgB,CAACT,OAAO,CAAC;IAC1D,IAAI,CAACyC,KAAK,CAAC8D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9F,gBAAgB,CAACX,MAAM,CAAC;IACzD,IAAI,CAAC2C,KAAK,CAAC+D,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC/F,gBAAgB,CAACtB,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACsD,KAAK,CAAC+D,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/F,gBAAgB,CAACf,aAAa,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,IAAI,CAACe,gBAAgB,CAACX,MAAM,KAAK2G,SAAS,IAAI,IAAI,CAAChG,gBAAgB,CAACX,MAAM,KAAK,IAAI,EAAE;MACvF,IAAI,IAAI,CAACW,gBAAgB,CAACX,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAAC2C,KAAK,CAACiE,aAAa,CAACC,IAAI,CAAC,YAAY,CAAC;MAC7C;IACF;EACF;EAEA/E,QAAQA,CAAC8D,GAAQ;IACf,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5D,KAAK,CAACiE,aAAa,CAAChI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC8D,OAAO,CAACoE,aAAa,CAAC,IAAI,CAACnE,KAAK,CAACiE,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC/D,gBAAgB,CAACkE,qCAAqC,CAAC;MAC5D/C,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC;QACA/E,aAAa,EAAE,IAAI,CAACe,gBAAgB,CAACf,aAAa;QAClDP,WAAW,EAAE,IAAI,CAACsB,gBAAgB,CAACtB,WAAW;QAC9CU,YAAY,EAAE,IAAI,CAACY,gBAAgB,CAACZ,YAAY;QAChDiH,WAAW,EAAE,IAAI,CAAChE,KAAK,GAAG,IAAI,GAAG,IAAI,CAACrC,gBAAgB,CAAChB,GAAI;QAC3DK,MAAM,EAAE,IAAI,CAACW,gBAAgB,CAACX,MAAM;QACpCE,OAAO,EAAE,IAAI,CAACS,gBAAgB,CAACT,OAAO;QAAE;QACxC+G,UAAU,EAAG,IAAI,CAACtG,gBAAwB,CAACuG,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACChD,IAAI,CACHzH,GAAG,CAAC0H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,OAAO,CAACyE,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACzE,OAAO,CAAC0E,YAAY,CAACjD,GAAG,CAACkD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACF7K,QAAQ,CAAC,MAAM,IAAI,CAAC+H,eAAe,EAAE,CAAC,EACtChI,QAAQ,CAAC,MAAMqJ,GAAG,CAAC0B,KAAK,EAAE,CAAC,CAC5B,CAAChD,SAAS,EAAE;EACjB;EAEA1C,OAAOA,CAACgE,GAAQ;IACdA,GAAG,CAAC0B,KAAK,EAAE;EACb;EAEAC,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBxL,IAAI,CAACyL,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,MAAMvC,IAAI,GAAGpJ,IAAI,CAAC+L,KAAK,CAACC,aAAa,CAACH,EAAE,CAAC;MACzC,IAAIzC,IAAI,IAAIA,IAAI,CAAClH,MAAM,GAAG,CAAC,EAAE;QAE3B,IAAI,CAACiE,gBAAgB,CAAC8F,2CAA2C,CAAC;UAChE3E,IAAI,EAAE;YACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;YACtCiE,KAAK,EAAEnB,MAAM,CAACI,KAAK,CAAC,CAAC;;SAExB,CAAC,CAAC3D,IAAI,CACLzH,GAAG,CAAC0H,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC1B,OAAO,CAACyE,aAAa,CAAC,MAAM,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACzE,OAAO,CAAC0E,YAAY,CAACjD,GAAG,CAACkD,OAAQ,CAAC;UACzC;QACF,CAAC,CAAC,EACF7K,QAAQ,CAAC,MAAM,IAAI,CAAC+H,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACD,SAAS,EAAE;MAEf,CAAC,MAAM;QACL,IAAI,CAAC5B,OAAO,CAAC0E,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAI,KAAK,CAACC,MAAM,CAAChH,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAoI,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAACC,mBAAmB,GAAGF,QAAQ;IACnC,IAAI,CAACrG,aAAa,CAACoD,IAAI,CAACkD,MAAM,CAAC;EACjC;EACAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5F,aAAa,EAAE;MACtB,IAAI,CAACvD,UAAU,GAAG,KAAK;MACvB,IAAI,CAACyE,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACxE,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyE,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC;EACF;EACA;EACA/C,eAAeA,CAAA;IACb;IACA,IAAI,CAAC+B,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,IAAI,CAACwC,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAACrB,mBAAmB;MACjE,IAAI,CAACoB,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACtF,gBAAgB,CAAChB,GAAG;MACjE,IAAI,CAACoG,qBAAqB,CAACG,WAAW,GAAG,IAAI,CAACvC,gBAAgB;MAC9D,IAAI,CAACoC,qBAAqB,CAACI,YAAY,GAAG,IAAI,CAACxF,gBAAgB,CAACtB,WAAW;MAE3E;MACA,IAAI,CAAC0G,qBAAqB,CAACK,mBAAmB,CAAC9B,SAAS,CAAEhB,cAA2B,IAAI;QACvF,IAAI,CAAC+C,qBAAqB,CAAC/C,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAACyC,qBAAqB,CAACO,oBAAoB,EAAE;IACnD;EACF;EAEA;EACA4C,YAAYA,CAACC,KAAgB,EAAEpD,qBAA4C,EAAEyB,KAAY;IACvFA,KAAK,CAAC4B,eAAe,EAAE;IAEvB;IACArD,qBAAqB,CAACC,WAAW,GAAG,IAAI,CAACrB,mBAAmB;IAC5DoB,qBAAqB,CAACE,UAAU,GAAG,IAAI,CAACtF,gBAAgB,EAAEhB,GAAG;IAC7DoG,qBAAqB,CAACG,WAAW,GAAG,IAAI,CAACvC,gBAAgB;IACzDoC,qBAAqB,CAACsD,UAAU,GAAG,IAAI,CAAC9F,eAAe;IACvDwC,qBAAqB,CAACuD,mBAAmB,GAAG,IAAI;IAEhD;IACAvD,qBAAqB,CAACwD,WAAW,EAAE;EACrC;EAEAlD,qBAAqBA,CAAC/C,cAA2B;IAC/C,IAAIA,cAAc,CAAC1E,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,MAAMsI,gBAAgB,GAAG5D,cAAc,CAACkG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC;MAE1D;MACC,IAAI,CAAC/I,gBAAwB,CAACuG,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAACvG,gBAAgB,CAAChB,GAAG,EAAE;QAC7B,IAAI,CAACgK,gBAAgB,EAAE;MACzB;IACF;IAEA;IACA,IAAI,CAACrG,cAAc,GAAG,EAAE;EAC1B,CAAC,CAAE;EACHqG,gBAAgBA,CAAA;IACd,IAAI,CAAC9G,gBAAgB,CAACkE,qCAAqC,CAAC;MAC1D/C,IAAI,EAAE;QACJU,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC/E,aAAa,EAAE,IAAI,CAACe,gBAAgB,CAACf,aAAa;QAClDP,WAAW,EAAE,IAAI,CAACsB,gBAAgB,CAACtB,WAAW;QAC9CU,YAAY,EAAE,IAAI,CAACY,gBAAgB,CAACZ,YAAY;QAChDiH,WAAW,EAAE,IAAI,CAACrG,gBAAgB,CAAChB,GAAI;QACvCK,MAAM,EAAE,IAAI,CAACW,gBAAgB,CAACX,MAAM;QACpCE,OAAO,EAAE,IAAI,CAACS,gBAAgB,CAACT,OAAO;QAAE;QACxC+G,UAAU,EAAG,IAAI,CAACtG,gBAAwB,CAACuG,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAAChD,IAAI,CACLzH,GAAG,CAAC0H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,OAAO,CAACyE,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACzE,OAAO,CAAC0E,YAAY,CAACjD,GAAG,CAACkD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACF7K,QAAQ,CAAC,MAAM,IAAI,CAAC+H,eAAe,EAAE,CAAC,EACtChI,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACoE,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC2D,SAAS,EAAE;EACf;EAEA;EACAsF,eAAeA,CAACC,QAAyB;IACvC,IAAI,CAAClG,gBAAgB,GAAGkG,QAAQ;IAChC,IAAI,CAACjG,kBAAkB,GAAG,IAAI;EAChC;EAEA;EACAkG,gBAAgBA,CAACD,QAAgB;IAC/B,MAAMzH,MAAM,GAAG,IAAI,CAACoB,eAAe,CAACnB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKoJ,QAAQ,CAAC;IACvE,OAAOzH,MAAM,GAAGA,MAAM,CAAC1B,KAAK,GAAG,MAAM;EACvC;;;uCAlYWwB,yBAAyB,EAAAnF,EAAA,CAAAgN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlN,EAAA,CAAAgN,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApN,EAAA,CAAAgN,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtN,EAAA,CAAAgN,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxN,EAAA,CAAAgN,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA1N,EAAA,CAAAgN,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAA3N,EAAA,CAAAgN,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA7N,EAAA,CAAAgN,iBAAA,CAAAS,EAAA,CAAAK,cAAA;IAAA;EAAA;;;YAAzB3I,yBAAyB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9BpClO,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAW,SAAA,qBAAiC;UACnCX,EAAA,CAAAY,YAAA,EAAiB;UAEfZ,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAU,MAAA,yJACtC;UAAAV,EAAA,CAAAY,YAAA,EAAK;UAICZ,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACF;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UAE7DZ,EADF,CAAAC,cAAA,cAAqB,iCAKF;UAHfD,EAAA,CAAA6D,gBAAA,iCAAAuK,yFAAArK,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAAvG,mBAAA,EAAA7D,MAAA,MAAAoK,GAAA,CAAAvG,mBAAA,GAAA7D,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAuC;UACvC/D,EAAA,CAAAE,UAAA,6BAAAoO,qFAAAvK,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAA,OAAArO,EAAA,CAAAQ,WAAA,CAAmB2N,GAAA,CAAA/F,0BAAA,CAAArE,MAAA,CAAkC;UAAA,EAAC;UAM9D/D,EAHM,CAAAY,YAAA,EAAwB,EACpB,EACF,EACF;UAaFZ,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;UAAAD,EAAA,CAAAU,MAAA,6CAAO;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UACpEZ,EAAA,CAAAC,cAAA,iBAAwG;UAAxDD,EAAA,CAAA6D,gBAAA,2BAAA0K,mEAAAxK,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAA7L,WAAA,EAAAyB,MAAA,MAAAoK,GAAA,CAAA7L,WAAA,GAAAyB,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAyB;UAE7E/D,EAFI,CAAAY,YAAA,EAAwG,EACpG,EACF;UAIFZ,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAQ;UACjEZ,EAAA,CAAAC,cAAA,iBAAwG;UAA1DD,EAAA,CAAA6D,gBAAA,2BAAA2K,mEAAAzK,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAAtL,aAAA,EAAAkB,MAAA,MAAAoK,GAAA,CAAAtL,aAAA,GAAAkB,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAA2B;UAE7E/D,EAFI,CAAAY,YAAA,EAAwG,EACpG,EACF;UAGFZ,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAA6D,gBAAA,2BAAA4K,yEAAA1K,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAA7H,aAAA,EAAAvC,MAAA,MAAAoK,GAAA,CAAA7H,aAAA,GAAAvC,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAA2B;UACpF/D,EAAA,CAAAE,UAAA,oBAAAwO,kEAAA;YAAA1O,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAA,OAAArO,EAAA,CAAAQ,WAAA,CAAU2N,GAAA,CAAAjC,YAAA,EAAc;UAAA,EAAC;UACzBlM,EAAA,CAAAU,MAAA,gHACF;UAAAV,EAAA,CAAAY,YAAA,EAAc;UAOdZ,EANA,CAAAuC,UAAA,KAAAoM,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpF9O,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAE,UAAA,oBAAA6O,4DAAAhL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAA,OAAArO,EAAA,CAAAQ,WAAA,CAAU2N,GAAA,CAAA3D,eAAA,CAAAzG,MAAA,CAAuB;UAAA,EAAC;UAApG/D,EAAA,CAAAY,YAAA,EAAqG;UACrGZ,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAE,UAAA,mBAAA8O,4DAAA;YAAAhP,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAA,OAAArO,EAAA,CAAAQ,WAAA,CAAS2N,GAAA,CAAAxF,0BAAA,EAA4B;UAAA,EAAC;UAAC3I,EAAA,CAAAU,MAAA,6CAAO;UAAAV,EAAA,CAAAW,SAAA,aAC9C;UAG3CX,EAH2C,CAAAY,YAAA,EAAS,EAC1C,EACF,EACF;UAKEZ,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,sCAAK;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACxCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACrCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAEpCV,EAFoC,CAAAY,YAAA,EAAK,EAClC,EACC;UACRZ,EAAA,CAAAuC,UAAA,KAAA0M,2CAAA,oBAA+D;UAiCrEjP,EAFI,CAAAY,YAAA,EAAQ,EACJ,EACO;UAEbZ,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA6D,gBAAA,kCAAAqL,mFAAAnL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAAlG,YAAA,EAAAlE,MAAA,MAAAoK,GAAA,CAAAlG,YAAA,GAAAlE,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAiC,4BAAAoL,6EAAApL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAApG,QAAA,EAAAhE,MAAA,MAAAoK,GAAA,CAAApG,QAAA,GAAAhE,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAwB,wBAAAqL,yEAAArL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAArO,EAAA,CAAAiE,kBAAA,CAAAkK,GAAA,CAAA1G,SAAA,EAAA1D,MAAA,MAAAoK,GAAA,CAAA1G,SAAA,GAAA1D,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAqB;UAC5F/D,EAAA,CAAAE,UAAA,wBAAAkP,yEAAArL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAAiO,GAAA;YAAA,OAAArO,EAAA,CAAAQ,WAAA,CAAc2N,GAAA,CAAA5F,WAAA,CAAAxE,MAAA,CAAmB;UAAA,EAAC;UAGxC/D,EAFI,CAAAY,YAAA,EAAiB,EACF,EACT;UAEVZ,EAAA,CAAAuC,UAAA,KAAA8M,iDAAA,iCAAArP,EAAA,CAAAsP,sBAAA,CAAoD;UAiEpDtP,EAAA,CAAAW,SAAA,kCAEoB;;;UA5KNX,EAAA,CAAAyB,SAAA,IAAuC;UAAvCzB,EAAA,CAAAgF,gBAAA,kBAAAmJ,GAAA,CAAAvG,mBAAA,CAAuC;UAqBK5H,EAAA,CAAAyB,SAAA,GAAyB;UAAzBzB,EAAA,CAAAgF,gBAAA,YAAAmJ,GAAA,CAAA7L,WAAA,CAAyB;UAO3BtC,EAAA,CAAAyB,SAAA,GAA2B;UAA3BzB,EAAA,CAAAgF,gBAAA,YAAAmJ,GAAA,CAAAtL,aAAA,CAA2B;UAKd7C,EAAA,CAAAyB,SAAA,GAA2B;UAA3BzB,EAAA,CAAAgF,gBAAA,YAAAmJ,GAAA,CAAA7H,aAAA,CAA2B;UAI7EtG,EAAA,CAAAyB,SAAA,GAAmB;UAAnBzB,EAAA,CAAAqC,UAAA,SAAA8L,GAAA,CAAAoB,aAAA,CAAmB;UAEnBvP,EAAA,CAAAyB,SAAA,EAAY;UAAZzB,EAAA,CAAAqC,UAAA,SAAA8L,GAAA,CAAA7K,MAAA,CAAY;UAEZtD,EAAA,CAAAyB,SAAA,EAAc;UAAdzB,EAAA,CAAAqC,UAAA,SAAA8L,GAAA,CAAAqB,QAAA,CAAc;UAEYxP,EAAA,CAAAyB,SAAA,EAAmB;UAAnBzB,EAAA,CAAAqC,UAAA,SAAA8L,GAAA,CAAAsB,aAAA,CAAmB;UAqBhDzP,EAAA,CAAAyB,SAAA,IAAqD;UAArDzB,EAAA,CAAAqC,UAAA,SAAA8L,GAAA,CAAA3K,YAAA,YAAA2K,GAAA,CAAA3K,YAAA,CAAA3B,MAAA,KAAqD;UAmCjD7B,EAAA,CAAAyB,SAAA,GAAiC;UAAyBzB,EAA1D,CAAAgF,gBAAA,mBAAAmJ,GAAA,CAAAlG,YAAA,CAAiC,aAAAkG,GAAA,CAAApG,QAAA,CAAwB,SAAAoG,GAAA,CAAA1G,SAAA,CAAqB;;;qBDpFtFlI,YAAY,EAAAmQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhQ,YAAY,EAAAiQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,YAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAjD,EAAA,CAAAkD,eAAA,EAAAlD,EAAA,CAAAmD,mBAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,mBAAA,EAAAtD,EAAA,CAAAuD,gBAAA,EAAAvD,EAAA,CAAAwD,iBAAA,EAAAxD,EAAA,CAAAyD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAElR,wBAAwB,EAAEC,qBAAqB;MAAAkR,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}