<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>

  <!-- 搜尋區域 -->
  <nb-card-body class="bg-white pb-0">
    <div class="col-12">
      <div class="row">
        <div class="form-group col-12 col-md-4 d-flex align-items-center">
          <label for="buildCase" class="label mr-2 mb-0 flex-shrink-0" style="min-width: 48px;">建案</label>
          <div class="flex-grow-1">
            <app-build-case-select [(selectedValue)]="getListRequirementRequest.CBuildCaseID"
              (valueChange)="onBuildCaseChange($event)" (buildCaseListLoaded)="onBuildCaseListLoaded($event)"
              class="w-100">
            </app-build-case-select>
          </div>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="groupName" class="label mr-2">區域</label>
          <input type="text" nbInput id="groupName" name="groupName" placeholder="區域"
            [(ngModel)]="getListRequirementRequest.CLocation">
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="requirement" class="label mr-2">工程項目</label>
          <input type="text" nbInput id="requirement" name="requirement" placeholder="工程項目"
            [(ngModel)]="getListRequirementRequest.CRequirement">
        </div>
        <!-- <div class="form-group col-12 col-md-4">
          <label for="cCode" class="label mr-2">料號</label>
          <input type="text" nbInput id="cCode" name="cCode" placeholder="料號"
            [(ngModel)]="getListRequirementRequest.CCode">
        </div> -->
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="houseType" class="label mr-2">類型</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CHouseType" class="col-9" multiple>
            <nb-option *ngFor="let type of houseType" [value]="type.value">
              {{ type.label }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="status" class="label mr-2">狀態</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CStatus" class="col-9">
            <nb-option [value]="-1">全部</nb-option>
            <nb-option [value]="1">啟用</nb-option>
            <nb-option [value]="0">停用</nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="isShow" class="label mr-2">預約需求</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CIsShow" class="col-9" placeholder="全部">
            <nb-option [value]="null">全部</nb-option>
            <nb-option [value]="true">是</nb-option>
            <nb-option [value]="false">否</nb-option>
          </nb-select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="isSimple" class="label mr-2">簡易客變</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CIsSimple" class="col-9" placeholder="全部">
            <nb-option [value]="null">全部</nb-option>
            <nb-option [value]="true">是</nb-option>
            <nb-option [value]="false">否</nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4"></div>
      </div>
      <div class="row">
        <div class="col-md-6"></div>
        <div class="form-group col-12 col-md-6 text-right d-flex flex-wrap justify-content-end align-items-center">
          <button class="btn btn-secondary mr-2 mb-2" (click)="resetSearch()"><i
              class="fas fa-undo mr-1"></i>重置</button>
          <button class="btn btn-info mr-2 mb-2" (click)="getList()"><i class="fas fa-search mr-1"></i>查詢</button>
          <button class="btn btn-primary mr-2 mb-2" (click)="openBatchEdit(batchEditDialog)"
            *ngIf="selectedItems.length > 0"><i class="fas fa-edit mr-1"></i>批次編輯 ({{selectedItems.length}})</button>
          <button class="btn btn-success mr-2 mb-2" (click)="openBatchAdd(batchAddDialog)" *ngIf="isCreate"><i
              class="fas fa-plus mr-1"></i>新增</button>
        </div>
      </div>
    </div>
  </nb-card-body>

  <!-- 建案需求列表 -->
  <nb-card-body class="bg-white pb-0">
    <div class="col-12 mt-3">
      <div class="table-responsive">
        <table class="table table-striped border " style="min-width: 800px; background-color:#f3f3f3;">
          <thead>
            <tr style="background-color: #27ae60;" class="d-flex text-white">
              <th scope="col" class="col-1 text-center d-flex flex-column align-items-center">
                <nb-checkbox [ngModel]="isAllSelected" (ngModelChange)="toggleSelectAll($event)">
                </nb-checkbox>
                <small class="text-white mt-1">全選</small>
              </th>
              <th scope="col" class="col-2">區域</th>
              <th scope="col" class="col-3">工程項目</th>
              <th scope="col" class="col-2">類型</th>
              <th scope="col" class="col-1">排序</th>
              <th scope="col" class="col-1">狀態</th>
              <th scope="col" class="col-1">單價</th>
              <th scope="col" class="col-2">操作功能</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of requirementList; let i = index" class="d-flex">
              <td class="col-1 text-center">
                <nb-checkbox [ngModel]="isItemSelected(data)" (ngModelChange)="toggleItemSelection(data)">
                </nb-checkbox>
              </td>
              <td class="col-2">{{ data.CLocation }}</td>
              <td class="col-3">{{ data.CRequirement }}</td>
              <td class="col-2">{{ getHouseType(data.CHouseType || []) }}</td>
              <td class="col-1">{{ data.CSort }}</td>
              <td class="col-1">{{ data.CStatus | getStatusName }}</td>
              <td class="col-1">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>
              <td class="col-2">
                <button *ngIf="isUpdate" type="button" class="btn btn-outline-success m-1  btn-sm"
                  (click)="onEdit(data,dialog)"><i class="fas fa-edit mr-1"></i>編輯</button>
                <button *ngIf="isDelete" type="button" class="btn btn-outline-danger m-1  btn-sm"
                  (click)="onDelete(data)"><i class="far fa-trash-alt mr-1"></i>刪除</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <ngx-pagination [CollectionSize]="totalRecords" [(Page)]="pageIndex" [PageSize]="pageSize"
        (PageChange)="getList()">
      </ngx-pagination>
    </div>
  </nb-card-body>
</nb-card>

<!-- 建案對話框 -->
<ng-template #dialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; min-width: 900px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span *ngIf="isNew===true">新增建案需求</span>
      <span *ngIf="isNew===false">編輯建案需求</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="row">
        <div class="col-12 col-md-12">
          <div class="row">

            <app-form-group [label]="'區域'" [labelFor]="'CLocation'" [isRequired]="false">
              <input type="text" nbInput class="flex-grow-1" id="CLocation" name="CLocation" placeholder="區域"
                [(ngModel)]="saveRequirement.CLocation" maxlength="20">
            </app-form-group>
            <app-form-group [label]="'工程項目'" [labelFor]="'CRequirement'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CRequirement" name="CRequirement" placeholder="工程項目"
                [(ngModel)]="saveRequirement.CRequirement" maxlength="50">
            </app-form-group>
            <app-form-group [label]="'排序'" [labelFor]="'CSort'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CSort" name="CSort" placeholder="排序"
                [(ngModel)]="saveRequirement.CSort" min="0">
            </app-form-group>
            <app-form-group [label]="'類型'" [labelFor]="'CHouseType'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CHouseType" name="CHouseType"
                [(selected)]="saveRequirement.CHouseType" multiple>
                <nb-option langg *ngFor="let type of houseType" [value]="type.value"> {{type.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'狀態'" [labelFor]="'CStatus'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CStatus" name="CStatus"
                [(selected)]="saveRequirement.CStatus">
                <nb-option langg *ngFor="let status of statusOptions" [value]="status.value">
                  {{status.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'單價'" [labelFor]="'CUnitPrice'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CUnitPrice" name="CUnitPrice" placeholder="單價"
                [(ngModel)]="saveRequirement.CUnitPrice" step="0.01" min="0">
            </app-form-group>
            <app-form-group [label]="'單位'" [labelFor]="'CUnit'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CUnit" name="CUnit" placeholder="單位"
                [(ngModel)]="saveRequirement.CUnit">
            </app-form-group>
            <app-form-group [label]="'備註說明'" [labelFor]="'CRemark'" [isRequired]="false">
              <textarea nbInput class="flex-grow-1" id="CRemark" name="CRemark" placeholder="備註說明"
                [(ngModel)]="saveRequirement.CRemark" maxlength="100" rows="3"></textarea>
            </app-form-group>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="save(ref)">確定</button>
          <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 批次編輯對話框 -->
<ng-template #batchEditDialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; min-width: 900px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>{{batchEditConfig.title}} (共 {{batchEditItems.length}} 個項目)</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem;  overflow-y: auto;">
      <div class="row">
        <div class="col-12">
          <div class="alert alert-info">
            <i class="fas fa-info-circle mr-2"></i>
            {{batchEditConfig.noticeText}}
          </div>

          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>注意事項：</strong>
            <ul class="mb-0 mt-2">
              <li *ngFor="let item of batchEditConfig.noticeItems">{{item}}</li>
            </ul>
          </div>

          <!-- 批次編輯項目列表 -->
          <div *ngFor="let item of batchEditItems; let i = index" class="mb-4">
            <nb-card>
              <nb-card-header class="py-2 d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <i class="fas fa-edit mr-2"></i>
                  項目 {{i + 1}}: {{item.CRequirement}}
                </h6>
                <button type="button" class="btn btn-outline-secondary btn-sm" (click)="resetBatchEditItem(i)"
                  title="重置為原始值">
                  <i class="fas fa-undo mr-1"></i>重置
                </button>
              </nb-card-header>
              <nb-card-body class="py-3">
                <div class="row">
                  <!-- 區域 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'區域'" [labelFor]="'location_' + i" [isRequired]="false">
                      <input type="text" nbInput class="flex-grow-1" [id]="'location_' + i" [(ngModel)]="item.CLocation"
                        placeholder="區域" maxlength="20">
                    </app-form-group>
                  </div>

                  <!-- 工程項目 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'工程項目'" [labelFor]="'requirement_' + i" [isRequired]="true">
                      <input type="text" nbInput class="flex-grow-1" [id]="'requirement_' + i"
                        [(ngModel)]="item.CRequirement" placeholder="工程項目" maxlength="50">
                    </app-form-group>
                  </div>

                  <!-- 類型 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'類型'" [labelFor]="'houseType_' + i" [isRequired]="true">
                      <nb-select class="flex-grow-1" placeholder="請選擇" [id]="'houseType_' + i"
                        [(selected)]="item.CHouseType" multiple>
                        <nb-option *ngFor="let type of houseType" [value]="type.value">
                          {{type.label}}
                        </nb-option>
                      </nb-select>
                    </app-form-group>
                  </div>

                  <!-- 排序 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'排序'" [labelFor]="'sort_' + i" [isRequired]="true">
                      <input type="number" nbInput class="flex-grow-1" [id]="'sort_' + i" [(ngModel)]="item.CSort"
                        placeholder="排序" min="0">
                    </app-form-group>
                  </div>

                  <!-- 狀態 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'狀態'" [labelFor]="'status_' + i" [isRequired]="true">
                      <nb-select class="flex-grow-1" placeholder="請選擇" [id]="'status_' + i" [(selected)]="item.CStatus">
                        <nb-option *ngFor="let status of statusOptions" [value]="status.value">
                          {{status.label}}
                        </nb-option>
                      </nb-select>
                    </app-form-group>
                  </div>

                  <!-- 單價 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'單價'" [labelFor]="'unitPrice_' + i" [isRequired]="true">
                      <input type="number" nbInput class="flex-grow-1" [id]="'unitPrice_' + i"
                        [(ngModel)]="item.CUnitPrice" placeholder="單價" step="0.01" min="0">
                    </app-form-group>
                  </div>

                  <!-- 單位 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'單位'" [labelFor]="'unit_' + i" [isRequired]="true">
                      <input type="text" nbInput class="flex-grow-1" [id]="'unit_' + i" [(ngModel)]="item.CUnit"
                        placeholder="單位">
                    </app-form-group>
                  </div>

                  <!-- 備註說明 -->
                  <div class="col-12">
                    <app-form-group [label]="'備註說明'" [labelFor]="'remark_' + i" [isRequired]="false">
                      <textarea nbInput class="flex-grow-1" [id]="'remark_' + i" [(ngModel)]="item.CRemark"
                        placeholder="備註說明" maxlength="100" rows="2">
                      </textarea>
                    </app-form-group>
                  </div>
                </div>
              </nb-card-body>
            </nb-card>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="batchSave(ref)">{{batchEditConfig.confirmButtonText}}</button>
          <button class="btn btn-danger mr-2"
            (click)="cancelBatchEdit(ref)">{{batchEditConfig.cancelButtonText}}</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 批次新增對話框 -->
<ng-template #batchAddDialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; max-width: 900px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>批次新增建案需求</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem; max-height: 70vh; overflow-y: auto;">
      <!-- 操作說明 -->
      <div class="alert alert-info mb-3" *ngIf="!isBuildCasePartSelected">
        <i class="fas fa-info-circle mr-2"></i>
        請先選擇建案項目，然後添加多筆新需求項目。
      </div>

      <!-- 建案部位選擇按鈕 -->
      <div class="mb-3" *ngIf="!isBuildCasePartSelected">
        <button nbButton status="primary" (click)="openBuildCasePartSelector()" size="medium">
          <nb-icon icon="plus-outline"></nb-icon>
          選擇建案項目
        </button>
        <small class="text-muted d-block mt-1">請先選擇建案項目，然後添加多筆新需求項目</small>
      </div>

      <!-- 已選擇的建案部位顯示 -->
      <div class="mb-3" *ngIf="isBuildCasePartSelected && selectedBuildCasePart">
        <div class="alert alert-success d-flex justify-content-between align-items-center">
          <div>
            <i class="fas fa-check-circle mr-2"></i>
            <strong>已選擇建案項目：</strong>{{ selectedBuildCasePart.location }} - {{ selectedBuildCasePart.part }}
            <small class="text-muted ml-2" *ngIf="selectedBuildCasePart.code">({{ selectedBuildCasePart.code }})</small>
          </div>
          <button class="btn btn-sm btn-outline-secondary" (click)="resetBuildCasePartSelection()" title="重新選擇建案項目">
            <i class="fas fa-edit mr-1"></i>重新選擇
          </button>
        </div>
      </div>
      <div class="row">
        <div class="col-12">

          <div class="alert alert-warning" *ngIf="isBuildCasePartSelected">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>注意事項：</strong>
            <ul class="mb-0 mt-2">
              <li>工程項目、類型、排序、狀態、單價、單位為必填欄位</li>
              <li>排序和單價不能為負數</li>
              <li>區域最多20個字，工程項目最多50個字，備註說明最多100個字</li>
            </ul>
          </div>

          <div *ngIf="isBuildCasePartSelected; ">
            <!-- 步驟進度指示器 -->
            <div class="mb-4" *ngIf="batchAddItems.length > 0">
              <div class="row">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">
                      <i class="fas fa-clipboard-list mr-2"></i>
                      項目 {{currentStepIndex + 1}} / {{batchAddItems.length}}
                    </h6>
                    <button class="btn btn-primary btn-sm" (click)="addNewBatchItem()">
                      <i class="fas fa-plus mr-1"></i>添加新項目
                    </button>
                  </div>
                  
                  <!-- 步驟指示器 -->
                  <div class="progress mb-3" style="height: 6px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         [style.width]="((currentStepIndex + 1) / batchAddItems.length * 100) + '%'">
                    </div>
                  </div>

                  <!-- 步驟導航點 -->
                  <div class="d-flex flex-wrap justify-content-center mb-3">
                    <button *ngFor="let item of batchAddItems; let i = index" 
                            type="button" 
                            class="btn btn-sm mr-1 mb-1"
                            [ngClass]="{
                              'btn-success': i === currentStepIndex,
                              'btn-outline-success': i !== currentStepIndex && isItemCompleted(item),
                              'btn-outline-secondary': i !== currentStepIndex && !isItemCompleted(item)
                            }"
                            (click)="goToStep(i)"
                            [title]="'跳至項目 ' + (i + 1) + (isItemCompleted(item) ? ' (已完成)' : ' (未完成)')">
                      {{i + 1}}
                      <i class="fas fa-check ml-1" *ngIf="i !== currentStepIndex && isItemCompleted(item)"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 當前步驟項目 -->
            <nb-card *ngIf="hasCurrentStep()" class="mb-4">
              <nb-card-header class="py-3 d-flex justify-content-between align-items-center">
                <h6 class="mb-0 d-flex align-items-center">
                  <i class="fas fa-edit mr-2 text-success"></i>
                  項目 {{currentStepIndex + 1}}
                  <nb-badge *ngIf="isItemCompleted(getCurrentStepItem())" status="success" size="tiny" class="ml-2">
                    已完成
                  </nb-badge>
                  <nb-badge *ngIf="!isItemCompleted(getCurrentStepItem())" status="warning" size="tiny" class="ml-2">
                    未完成
                  </nb-badge>
                </h6>
                <div>
                  <button type="button" class="btn btn-outline-secondary btn-sm mr-2" 
                          (click)="resetBatchAddItem(currentStepIndex)"
                          title="重置為預設值">
                    <i class="fas fa-undo mr-1"></i>重置
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" 
                          (click)="removeBatchAddItem(currentStepIndex)"
                          title="移除此項目"
                          [disabled]="batchAddItems.length <= 1">
                    <i class="fas fa-trash mr-1"></i>移除
                  </button>
                </div>
              </nb-card-header>
              <nb-card-body class="py-3">
                <div class="row">
                  <!-- 區域 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'區域'" [labelFor]="'location_current'" [isRequired]="false">
                      <input type="text" nbInput class="flex-grow-1" id="location_current" 
                             [(ngModel)]="getCurrentStepItem().CLocation"
                             placeholder="區域" maxlength="20">
                    </app-form-group>
                  </div>

                  <!-- 工程項目 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'工程項目'" [labelFor]="'requirement_current'" [isRequired]="true">
                      <input type="text" nbInput class="flex-grow-1" id="requirement_current"
                             [(ngModel)]="getCurrentStepItem().CRequirement" 
                             placeholder="工程項目" maxlength="50">
                    </app-form-group>
                  </div>
                  
                  <!-- 類型 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'類型'" [labelFor]="'houseType_current'" [isRequired]="true">
                      <nb-select class="flex-grow-1" placeholder="請選擇" id="houseType_current"
                                 [(selected)]="getCurrentStepItem().CHouseType" multiple>
                        <nb-option *ngFor="let type of houseType" [value]="type.value">
                          {{type.label}}
                        </nb-option>
                      </nb-select>
                    </app-form-group>
                  </div>
                  
                  <!-- 排序 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'排序'" [labelFor]="'sort_current'" [isRequired]="true">
                      <input type="number" nbInput class="flex-grow-1" id="sort_current" 
                             [(ngModel)]="getCurrentStepItem().CSort"
                             placeholder="排序" min="0">
                    </app-form-group>
                  </div>

                  <!-- 狀態 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'狀態'" [labelFor]="'status_current'" [isRequired]="true">
                      <nb-select class="flex-grow-1" placeholder="請選擇" id="status_current" 
                                 [(selected)]="getCurrentStepItem().CStatus">
                        <nb-option *ngFor="let status of statusOptions" [value]="status.value">
                          {{status.label}}
                        </nb-option>
                      </nb-select>
                    </app-form-group>
                  </div>

                  <!-- 單價 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'單價'" [labelFor]="'unitPrice_current'" [isRequired]="true">
                      <input type="number" nbInput class="flex-grow-1" id="unitPrice_current"
                             [(ngModel)]="getCurrentStepItem().CUnitPrice" 
                             placeholder="單價" step="0.01" min="0">
                    </app-form-group>
                  </div>

                  <!-- 單位 -->
                  <div class="col-md-6">
                    <app-form-group [label]="'單位'" [labelFor]="'unit_current'" [isRequired]="true">
                      <input type="text" nbInput class="flex-grow-1" id="unit_current" 
                             [(ngModel)]="getCurrentStepItem().CUnit"
                             placeholder="單位">
                    </app-form-group>
                  </div>
                  
                  <!-- 備註說明 -->
                  <div class="col-12">
                    <app-form-group [label]="'備註說明'" [labelFor]="'remark_current'" [isRequired]="false">
                      <textarea nbInput class="flex-grow-1" id="remark_current" 
                                [(ngModel)]="getCurrentStepItem().CRemark"
                                placeholder="備註說明" maxlength="100" rows="2"></textarea>
                    </app-form-group>
                  </div>
                </div>
              </nb-card-body>
              
              <!-- 步驟導航控制 -->
              <nb-card-footer class="py-2">
                <div class="d-flex justify-content-between align-items-center">
                  <button type="button" class="btn btn-outline-secondary" 
                          (click)="goToPrevStep()" 
                          [disabled]="isFirstStep()">
                    <i class="fas fa-chevron-left mr-1"></i>上一項
                  </button>
                  
                  <div class="text-center">
                    <small class="text-muted">項目 {{currentStepIndex + 1}} / {{batchAddItems.length}}</small>
                  </div>
                  
                  <button type="button" class="btn btn-outline-success" 
                          (click)="goToNextStep()" 
                          [disabled]="isLastStep()">
                    下一項<i class="fas fa-chevron-right ml-1"></i>
                  </button>
                </div>
              </nb-card-footer>
            </nb-card>

            <!-- 沒有項目時的提示 -->
            <div *ngIf="batchAddItems.length === 0" class="text-center py-4">
              <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
              <p class="text-muted mb-3">尚未添加任何項目</p>
              <button class="btn btn-primary" (click)="addNewBatchItem()">
                <i class="fas fa-plus mr-1"></i>添加第一個項目
              </button>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="batchAddSave(ref)"
            *ngIf="isBuildCasePartSelected; ">確定新增</button>
          <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>