{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\nimport { ImagePreviewComponent } from '../../../shared/components/image-preview';\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nlet BuildingMaterialComponent = class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    this.CMaterialCode = \"\";\n    this.ShowPrice = false;\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 簡化後只保留必要的\n    this.selectedImages = []; // 右側已選擇的圖片\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 可選擇圖片分頁屬性\n    this.availableImageCurrentPage = 1;\n    this.availableImagePageSize = 30;\n    this.availableImageTotalRecords = 0;\n    // 已選擇圖片分頁屬性\n    this.selectedImageCurrentPage = 1;\n    this.selectedImagePageSize = 20;\n    this.selectedImageTotalRecords = 0;\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\n        // 不立即載入材料列表，等待建案選擇事件觸發\n      }\n    })).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        CMaterialCode: this.CMaterialCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  // 建案選擇事件處理（新）\n  onBuildCaseSelectionChange(selectedBuildCase) {\n    if (selectedBuildCase) {\n      this.selectedBuildCaseId = selectedBuildCase.cID;\n    } else if (this.listBuildCases.length > 0) {\n      this.selectedBuildCaseId = this.listBuildCases[0].cID;\n    }\n    this.search();\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1,\n      // 預設為啟用狀態\n      CPrice: 0 // 預設價格為0\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.required('[價格]', this.selectedMaterial.CPrice);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30);\n    // 價格驗證：必須為數字且大於等於0\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\n      if (this.selectedMaterial.CPrice < 0) {\n        this.valid.errorMessages.push('[價格] 不能小於0');\n      }\n    }\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CFile: target.files[0]\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        }), mergeMap(() => this.getMaterialList(1))).subscribe();\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder() {\n    // 重置選擇狀態\n    this.selectedImages = [];\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAvailableImages();\n    this.loadSelectedImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  // 載入可選擇的圖片（左側）\n  loadAvailableImages() {\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.availableImageCurrentPage,\n          PageSize: this.availableImagePageSize,\n          CName: this.imageSearchTerm || undefined\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          const allCurrentPageImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          this.availableImageTotalRecords = res.TotalItems || 0;\n          // 排除已選擇的圖片\n          const selectedIds = this.selectedImages.map(img => img.id);\n          this.availableImages = allCurrentPageImages.filter(image => !selectedIds.includes(image.id));\n        } else {\n          this.message.showErrorMSG(res.Message || '載入可選擇圖片失敗');\n          this.availableImages = [];\n          this.availableImageTotalRecords = 0;\n        }\n      });\n    } else {\n      this.availableImages = [];\n      this.availableImageTotalRecords = 0;\n    }\n  }\n  // 載入已選擇的圖片（右側），帶入 CMaterialId 參數\n  loadSelectedImages() {\n    if (this.isCategorySelected && this.selectedBuildCaseId && this.selectedMaterial?.CId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CMaterialId: this.selectedMaterial.CId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.selectedImageCurrentPage,\n          PageSize: this.selectedImagePageSize\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.selectedImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          this.selectedImageTotalRecords = res.TotalItems || 0;\n          // 更新已綁定的圖片ID\n          this.boundImageIds = this.selectedImages.map(img => img.id);\n        } else {\n          this.message.showErrorMSG(res.Message || '載入已選擇圖片失敗');\n          this.selectedImages = [];\n          this.selectedImageTotalRecords = 0;\n        }\n      });\n    } else {\n      // 如果是新增建材或沒有 MaterialId，初始化為已綁定圖片\n      if (this.boundImageIds.length > 0) {\n        this.loadInitialSelectedImages();\n      } else {\n        this.selectedImages = [];\n        this.selectedImageTotalRecords = 0;\n      }\n    }\n  }\n  // 載入初始已選擇圖片（用於新增建材時的已綁定圖片初始化）\n  loadInitialSelectedImages() {\n    this._pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        cPictureType: this.selectedCategory,\n        PageIndex: 1,\n        PageSize: 9999 // 使用大數字獲取所有圖片\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        const allAvailableImages = res.Entries?.map(picture => ({\n          id: picture.CId || 0,\n          name: picture.CPictureCode || picture.CName || '',\n          size: 0,\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n        })) || [];\n        // 從所有圖片中找出已綁定的圖片\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\n        this.selectedImages = [...boundImages];\n        this.selectedImageTotalRecords = boundImages.length;\n      }\n    });\n  }\n  // 搜尋過濾可選擇圖片\n  filterAvailableImages() {\n    // 重新載入可選擇圖片（包含搜尋條件）\n    this.availableImageCurrentPage = 1; // 搜尋時重置到第一頁\n    this.loadAvailableImages();\n  }\n  moveToSelected(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從可選移到已選\n    const index = this.availableImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.push(image);\n      this.selectedImageTotalRecords = this.selectedImages.length;\n      // 重新載入兩側資料\n      this.loadAvailableImages();\n    }\n  }\n  moveToAvailable(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n      this.selectedImageTotalRecords = this.selectedImages.length;\n      // 重新載入兩側資料\n      this.loadAvailableImages();\n    }\n  }\n  moveAllToSelected() {\n    // 將當前頁面所有可選圖片移到已選\n    this.selectedImages.push(...this.availableImages);\n    this.selectedImageTotalRecords = this.selectedImages.length;\n    // 重新載入可選圖片\n    this.loadAvailableImages();\n  }\n  moveAllToAvailable() {\n    // 清空所有已選圖片\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    // 重新載入可選圖片\n    this.loadAvailableImages();\n  }\n  isImageBound(image) {\n    return this.boundImageIds.includes(image.id);\n  }\n  getBoundImagesCount() {\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\n  }\n  getNewSelectedCount() {\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\n  }\n  // 清除所有選擇（包括已綁定的圖片）\n  clearAllSelection() {\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    this.loadAvailableImages();\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    // 建立預覽圖片陣列（合併可選和已選圖片）\n    this.allImages = [...this.availableImages, ...this.selectedImages];\n    this.currentPreviewIndex = this.allImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  onConfirmImageBinding(selectedImages) {\n    if (selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = selectedImages.map(img => img.id);\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    // 清理選擇狀態\n    this.selectedImages = [];\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    // 清理選擇狀態但不重新載入圖片（對話框即將關閉）\n    this.selectedImages = [];\n    this.selectedImageTotalRecords = 0;\n    this.imageSearchTerm = \"\";\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    ref.close();\n  }\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    this.availableImageCurrentPage = 1;\n    this.selectedImageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadAvailableImages();\n      this.loadSelectedImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  availableImagePageChanged(page) {\n    this.availableImageCurrentPage = page;\n    this.loadAvailableImages();\n  }\n  // 已選擇圖片分頁變更處理方法\n  selectedImagePageChanged(page) {\n    this.selectedImageCurrentPage = page;\n    this.loadSelectedImages();\n  }\n};\n__decorate([ViewChild('imagePreviewComponent', {\n  static: false\n})], BuildingMaterialComponent.prototype, \"imagePreviewComponent\", void 0);\nBuildingMaterialComponent = __decorate([Component({\n  selector: 'ngx-building-material',\n  templateUrl: './building-material.component.html',\n  styleUrls: ['./building-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent]\n})], BuildingMaterialComponent);\nexport { BuildingMaterialComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "BuildCaseSelectComponent", "ImagePreviewComponent", "PictureCategory", "BuildingMaterialComponent", "getStatusLabel", "status", "option", "statusOptions", "find", "opt", "value", "label", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "CSelectName", "CMaterialCode", "ShowPrice", "filterMapping", "CIsMapping", "selectedImages", "imageSearchTerm", "previewingImage", "currentPreviewIndex", "availableImageCurrentPage", "availableImagePageSize", "availableImageTotalRecords", "selectedImageCurrentPage", "selectedImagePageSize", "selectedImageTotalRecords", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "selectedCate<PERSON><PERSON>", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "length", "subscribe", "getMaterialList", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "selectedBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "materialList", "totalRecords", "TotalItems", "CShowPrice", "onBuildCaseSelectionChange", "selectedBuildCase", "cID", "search", "pageChanged", "exportExelMaterialList", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "addNew", "ref", "selectedMaterial", "CPrice", "open", "onSelectedMaterial", "data", "bindImageForMaterial", "loadAvailableImages", "loadSelectedImages", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "undefined", "errorMessages", "push", "onSubmit", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CDescription", "CMaterialId", "CId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "close", "onClose", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "utils", "sheet_to_json", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "changeFilter", "openImageBinder", "apiPictureGetPictureListPost$Json", "cPictureType", "CName", "allCurrentPageImages", "map", "picture", "id", "name", "CPictureCode", "size", "thumbnailUrl", "CBase64", "fullUrl", "lastModified", "CUpdateDT", "Date", "selectedIds", "img", "availableImages", "filter", "image", "includes", "boundImageIds", "loadInitialSelectedImages", "allAvailableImages", "boundImages", "filterAvailableImages", "moveToSelected", "stopPropagation", "index", "findIndex", "moveToAvailable", "splice", "moveAllToSelected", "moveAllToAvailable", "isImageBound", "getBoundImagesCount", "getNewSelectedCount", "clearAllSelection", "previewImage", "imagePreviewRef", "allImages", "onConfirmImageBinding", "saveImageBinding", "onCloseImageBinder", "categoryChanged", "category", "getCategoryLabel", "availableImagePageChanged", "page", "selectedImagePageChanged", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { BuildCaseSelectComponent } from '../../../shared/components/build-case-select/build-case-select.component';\r\nimport { ImagePreviewComponent, ImageItem } from '../../../shared/components/image-preview';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, BuildCaseSelectComponent, ImagePreviewComponent],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('imagePreviewComponent', { static: false }) imagePreviewComponent!: ImagePreviewComponent;\r\n  \r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 簡化後只保留必要的\r\n  selectedImages: ImageItem[] = [] // 右側已選擇的圖片\r\n  imageSearchTerm: string = \"\"\r\n<<<<<<< HEAD\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 可選擇圖片分頁屬性\r\n  availableImageCurrentPage: number = 1\r\n  availableImagePageSize: number = 30\r\n  availableImageTotalRecords: number = 0\r\n\r\n  // 已選擇圖片分頁屬性\r\n  selectedImageCurrentPage: number = 1\r\n  selectedImagePageSize: number = 20\r\n  selectedImageTotalRecords: number = 0\r\n=======\r\n>>>>>>> develop\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            // 移除強制設定，讓 build-case-select 元件的記憶功能主導選擇\r\n            // 不立即載入材料列表，等待建案選擇事件觸發\r\n          }\r\n        })\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        CMaterialCode: this.CMaterialCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  // 建案選擇事件處理（新）\r\n  onBuildCaseSelectionChange(selectedBuildCase: BuildCaseGetListReponse | null) {\r\n    if (selectedBuildCase) {\r\n      this.selectedBuildCaseId = selectedBuildCase.cID!;\r\n    } else if (this.listBuildCases.length > 0) {\r\n      this.selectedBuildCaseId = this.listBuildCases[0].cID!;\r\n    }\r\n    this.search();\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1, // 預設為啟用狀態\r\n      CPrice: 0   // 預設價格為0\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse) {\r\n    this.selectedMaterial = { ...data }\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n<<<<<<< HEAD\r\n    this.availableImageCurrentPage = 1\r\n    this.selectedImageCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAvailableImages()\r\n    this.loadSelectedImages()\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n=======\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\r\n      \r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n      \r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n>>>>>>> develop\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.required('[價格]', this.selectedMaterial.CPrice)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\r\n    // 價格驗證：必須為數字且大於等於0\r\n    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {\r\n      if (this.selectedMaterial.CPrice < 0) {\r\n        this.valid.errorMessages.push('[價格] 不能小於0')\r\n      }\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n\r\n        this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CFile: target.files[0]\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG(\"執行成功\")\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n          }),\r\n          mergeMap(() => this.getMaterialList(1))\r\n        ).subscribe();\r\n\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder() {\r\n    // 重置選擇狀態\r\n    this.selectedImages = []\r\n<<<<<<< HEAD\r\n    this.availableImageCurrentPage = 1\r\n    this.selectedImageCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAvailableImages();\r\n    this.loadSelectedImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n\r\n  // 載入可選擇的圖片（左側）\r\n  loadAvailableImages() {\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.availableImageCurrentPage,\r\n          PageSize: this.availableImagePageSize,\r\n          CName: this.imageSearchTerm || undefined\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          const allCurrentPageImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n          })) || [];\r\n\r\n          this.availableImageTotalRecords = res.TotalItems || 0;\r\n\r\n          // 排除已選擇的圖片\r\n          const selectedIds = this.selectedImages.map(img => img.id);\r\n          this.availableImages = allCurrentPageImages.filter(image => !selectedIds.includes(image.id));\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入可選擇圖片失敗');\r\n          this.availableImages = [];\r\n          this.availableImageTotalRecords = 0;\r\n        }\r\n      });\r\n    } else {\r\n      this.availableImages = [];\r\n      this.availableImageTotalRecords = 0;\r\n    }\r\n  }\r\n\r\n  // 載入已選擇的圖片（右側），帶入 CMaterialId 參數\r\n  loadSelectedImages() {\r\n    if (this.isCategorySelected && this.selectedBuildCaseId && this.selectedMaterial?.CId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CMaterialId: this.selectedMaterial.CId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.selectedImageCurrentPage,\r\n          PageSize: this.selectedImagePageSize\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.selectedImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n          })) || [];\r\n\r\n          this.selectedImageTotalRecords = res.TotalItems || 0;\r\n\r\n          // 更新已綁定的圖片ID\r\n          this.boundImageIds = this.selectedImages.map(img => img.id);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入已選擇圖片失敗');\r\n          this.selectedImages = [];\r\n          this.selectedImageTotalRecords = 0;\r\n        }\r\n      });\r\n    } else {\r\n      // 如果是新增建材或沒有 MaterialId，初始化為已綁定圖片\r\n      if (this.boundImageIds.length > 0) {\r\n        this.loadInitialSelectedImages();\r\n      } else {\r\n        this.selectedImages = [];\r\n        this.selectedImageTotalRecords = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 載入初始已選擇圖片（用於新增建材時的已綁定圖片初始化）\r\n  loadInitialSelectedImages() {\r\n    this._pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        cPictureType: this.selectedCategory,\r\n        PageIndex: 1,\r\n        PageSize: 9999 // 使用大數字獲取所有圖片\r\n      }\r\n    }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n      if (res.StatusCode === 0) {\r\n        const allAvailableImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n          id: picture.CId || 0,\r\n          name: picture.CPictureCode || picture.CName || '',\r\n          size: 0,\r\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n        })) || [];\r\n\r\n        // 從所有圖片中找出已綁定的圖片\r\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\r\n        this.selectedImages = [...boundImages];\r\n        this.selectedImageTotalRecords = boundImages.length;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 搜尋過濾可選擇圖片\r\n  filterAvailableImages() {\r\n    // 重新載入可選擇圖片（包含搜尋條件）\r\n    this.availableImageCurrentPage = 1; // 搜尋時重置到第一頁\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  moveToSelected(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從可選移到已選\r\n    const index = this.availableImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.push(image);\r\n      this.selectedImageTotalRecords = this.selectedImages.length;\r\n      // 重新載入兩側資料\r\n      this.loadAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveToAvailable(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\r\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n      this.selectedImageTotalRecords = this.selectedImages.length;\r\n      // 重新載入兩側資料\r\n      this.loadAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveAllToSelected() {\r\n    // 將當前頁面所有可選圖片移到已選\r\n    this.selectedImages.push(...this.availableImages);\r\n    this.selectedImageTotalRecords = this.selectedImages.length;\r\n    // 重新載入可選圖片\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  moveAllToAvailable() {\r\n    // 清空所有已選圖片\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    // 重新載入可選圖片\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  isImageBound(image: ImageItem): boolean {\r\n    return this.boundImageIds.includes(image.id);\r\n  }\r\n\r\n  getBoundImagesCount(): number {\r\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\r\n  }\r\n\r\n  getNewSelectedCount(): number {\r\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\r\n  }\r\n\r\n  // 清除所有選擇（包括已綁定的圖片）\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    \r\n    // 建立預覽圖片陣列（合併可選和已選圖片）\r\n    this.allImages = [...this.availableImages, ...this.selectedImages];\r\n    this.currentPreviewIndex = this.allImages.findIndex((img: ImageItem) => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n=======\r\n    this.imageSearchTerm = \"\"\r\n\r\n    // 使用 imagePreviewComponent 的綁定界面\r\n    if (this.imagePreviewComponent) {\r\n      this.imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n      this.imagePreviewComponent.materialId = this.selectedMaterial.CId;\r\n      this.imagePreviewComponent.pictureType = this.selectedCategory;\r\n      this.imagePreviewComponent.materialName = this.selectedMaterial.CSelectName || undefined;\r\n      \r\n      // 監聽圖片綁定確認事件\r\n      this.imagePreviewComponent.confirmImageBinding.subscribe((selectedImages: ImageItem[]) => {\r\n        this.onConfirmImageBinding(selectedImages);\r\n      });\r\n      \r\n      this.imagePreviewComponent.openBindingInterface();\r\n    }\r\n  }\r\n\r\n  // 處理圖片預覽的方法，使用重構後的 ImagePreviewComponent\r\n  previewImage(image: ImageItem, imagePreviewComponent: ImagePreviewComponent, event: Event) {\r\n    event.stopPropagation();\r\n\r\n    // 設定預覽元件參數，讓它自行載入圖片\r\n    imagePreviewComponent.buildCaseId = this.selectedBuildCaseId;\r\n    imagePreviewComponent.materialId = this.selectedMaterial?.CId;\r\n    imagePreviewComponent.pictureType = this.selectedCategory;\r\n    imagePreviewComponent.searchTerm = this.imageSearchTerm;\r\n    imagePreviewComponent.showSelectionToggle = true;\r\n\r\n    // 開啟預覽對話框\r\n    imagePreviewComponent.openPreview();\r\n>>>>>>> develop\r\n  }\r\n\r\n  onConfirmImageBinding(selectedImages: ImageItem[]) {\r\n    if (selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = selectedImages.map(img => img.id);\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    // 清理選擇狀態\r\n    this.selectedImages = [];\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n<<<<<<< HEAD\r\n  onCloseImageBinder(ref: any) {\r\n    // 清理選擇狀態但不重新載入圖片（對話框即將關閉）\r\n    this.selectedImages = [];\r\n    this.selectedImageTotalRecords = 0;\r\n    this.imageSearchTerm = \"\";\r\n    this.availableImageCurrentPage = 1;\r\n    this.selectedImageCurrentPage = 1;\r\n    ref.close();\r\n  }\r\n=======\r\n>>>>>>> develop\r\n\r\n  // 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n<<<<<<< HEAD\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.availableImageCurrentPage = 1;\r\n    this.selectedImageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadAvailableImages();\r\n      this.loadSelectedImages();\r\n    }\r\n=======\r\n>>>>>>> develop\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n<<<<<<< HEAD\r\n  // 可選擇圖片分頁變更處理方法\r\n  availableImagePageChanged(page: number) {\r\n    this.availableImageCurrentPage = page;\r\n    this.loadAvailableImages();\r\n  }\r\n\r\n  // 已選擇圖片分頁變更處理方法\r\n  selectedImagePageChanged(page: number) {\r\n    this.selectedImageCurrentPage = page;\r\n    this.loadSelectedImages();\r\n  }\r\n=======\r\n>>>>>>> develop\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAuBC,SAAS,QAAQ,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,qBAAqB,QAAmB,0CAA0C;AAE3F;AACA,IAAKC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAcb,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQJ,aAAa;EAkE1D;EACAK,cAAcA,CAACC,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,KAAK;EACtC;EAEAC,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA7EzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEb,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAAa,iBAAiB,GAAG,IAAI;IAC9B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAC,cAAc,GAAgB,EAAE,EAAC;IACjC,KAAAC,eAAe,GAAW,EAAE;IAE5B,KAAAC,eAAe,GAAqB,IAAI;IACxC,KAAAC,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAC,yBAAyB,GAAW,CAAC;IACrC,KAAAC,sBAAsB,GAAW,EAAE;IACnC,KAAAC,0BAA0B,GAAW,CAAC;IAEtC;IACA,KAAAC,wBAAwB,GAAW,CAAC;IACpC,KAAAC,qBAAqB,GAAW,EAAE;IAClC,KAAAC,yBAAyB,GAAW,CAAC;IAKrC,KAAAC,eAAe,GAAG,CAChB;MAAE9B,KAAK,EAAER,eAAe,CAACuC,iBAAiB;MAAE9B,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAER,eAAe,CAACwC,SAAS;MAAE/B,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAgC,gBAAgB,GAAoBzC,eAAe,CAACuC,iBAAiB;IACrE,KAAAG,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAA1C,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAAK,aAAa,GAAG,CAAC;MACfG,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAESkC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC7B,iBAAiB,CAAC8B,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACHvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC/B,cAAc,GAAG8B,GAAG,CAACE,OAAO,EAAEC,MAAM,GAAGH,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D;QACA;MACF;IACF,CAAC,CAAC,CACH,CAACE,SAAS,EAAE;EACjB;EAAEC,eAAeA,CAACC,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAACxC,gBAAgB,CAACyC,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtCC,QAAQ,EAAE,IAAI,CAACtC,iBAAiB;QAChCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCqC,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEP,SAAS;QACpB7B,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACsB,IAAI,CACLvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACa,YAAY,GAAGd,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACa,YAAY,GAAGf,GAAG,CAACgB,UAAW;QAEnC,IAAI,IAAI,CAACF,YAAY,CAACX,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAACuC,YAAY,CAAC,CAAC,CAAC,CAACG,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA;EACAC,0BAA0BA,CAACC,iBAAiD;IAC1E,IAAIA,iBAAiB,EAAE;MACrB,IAAI,CAACV,mBAAmB,GAAGU,iBAAiB,CAACC,GAAI;IACnD,CAAC,MAAM,IAAI,IAAI,CAAClD,cAAc,CAACiC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACvC,cAAc,CAAC,CAAC,CAAC,CAACkD,GAAI;IACxD;IACA,IAAI,CAACC,MAAM,EAAE;EACf;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAChB,eAAe,EAAE,CAACD,SAAS,EAAE;EACpC;EAEAkB,WAAWA,CAAChB,SAAiB;IAC3B,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACF,SAAS,EAAE;EAC7C;EAEAmB,sBAAsBA,CAAA;IACpB,IAAI,CAACzD,gBAAgB,CAAC0D,2CAA2C,CAAC;MAChE5B,IAAI,EAAE,IAAI,CAACa;KACZ,CAAC,CAACL,SAAS,CAACJ,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE;UACzB,IAAI,CAAC1D,eAAe,CAAC2D,iBAAiB,CAAC1B,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAAC7D,gBAAgB,CAAC8D,+CAA+C,CAAC;MACpEhC,IAAI,EAAE,IAAI,CAACa;KACZ,CAAC,CAACL,SAAS,CAACJ,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE;UACzB,IAAI,CAAC1D,eAAe,CAAC2D,iBAAiB,CAAC1B,GAAG,CAACE,OAAQ,CAACuB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACAI,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC7D,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC8D,gBAAgB,GAAG;MACtBjC,OAAO,EAAE,CAAC;MAAE;MACZkC,MAAM,EAAE,CAAC,CAAG;KACb;IACD,IAAI,CAACtE,aAAa,CAACuE,IAAI,CAACH,GAAG,CAAC;EAC9B;EACAI,kBAAkBA,CAACC,IAA6B,EAAEL,GAAQ;IACxD,IAAI,CAAC7D,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC8D,gBAAgB,GAAG;MAAE,GAAGI;IAAI,CAAE;IACnC,IAAI,CAACzE,aAAa,CAACuE,IAAI,CAACH,GAAG,CAAC;EAC9B;EACAM,oBAAoBA,CAACD,IAA6B;IAChD,IAAI,CAACJ,gBAAgB,GAAG;MAAE,GAAGI;IAAI,CAAE;IACnC;IACA,IAAI,CAACzD,cAAc,GAAG,EAAE;IAExB,IAAI,CAACI,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACG,wBAAwB,GAAG,CAAC;IACjC,IAAI,CAACN,eAAe,GAAG,EAAE;IAEzB,IAAI,CAAC0D,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAAC5E,aAAa,CAACuE,IAAI,CAACH,GAAG,EAAE;MAAES,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAmB/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC5E,KAAK,CAAC6E,KAAK,EAAE;IAElB,IAAI,CAAC7E,KAAK,CAAC8E,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACX,gBAAgB,CAAC1D,WAAW,CAAC;IAClE;IACA,IAAI,CAACT,KAAK,CAAC8E,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACX,gBAAgB,CAACzD,aAAa,CAAC;IAClE,IAAI,CAACV,KAAK,CAAC8E,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACX,gBAAgB,CAACjC,OAAO,CAAC;IAC1D,IAAI,CAAClC,KAAK,CAAC8E,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACX,gBAAgB,CAACC,MAAM,CAAC;IACzD,IAAI,CAACpE,KAAK,CAAC+E,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACZ,gBAAgB,CAAC1D,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACT,KAAK,CAAC+E,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACZ,gBAAgB,CAACzD,aAAa,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,IAAI,CAACyD,gBAAgB,CAACC,MAAM,KAAKY,SAAS,IAAI,IAAI,CAACb,gBAAgB,CAACC,MAAM,KAAK,IAAI,EAAE;MACvF,IAAI,IAAI,CAACD,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAACpE,KAAK,CAACiF,aAAa,CAACC,IAAI,CAAC,YAAY,CAAC;MAC7C;IACF;EACF;EAEAC,QAAQA,CAACjB,GAAQ;IACf,IAAI,CAACU,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5E,KAAK,CAACiF,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxC,OAAO,CAACqF,aAAa,CAAC,IAAI,CAACpF,KAAK,CAACiF,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC/E,gBAAgB,CAACmF,qCAAqC,CAAC;MAC5DrD,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC;QACAnC,aAAa,EAAE,IAAI,CAACyD,gBAAgB,CAACzD,aAAa;QAClDD,WAAW,EAAE,IAAI,CAAC0D,gBAAgB,CAAC1D,WAAW;QAC9C6E,YAAY,EAAE,IAAI,CAACnB,gBAAgB,CAACmB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAAClF,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC8D,gBAAgB,CAACqB,GAAI;QAC3DpB,MAAM,EAAE,IAAI,CAACD,gBAAgB,CAACC,MAAM;QACpClC,OAAO,EAAE,IAAI,CAACiC,gBAAgB,CAACjC,OAAO;QAAE;QACxCuD,UAAU,EAAG,IAAI,CAACtB,gBAAwB,CAACuB,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCvD,IAAI,CACHvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtC,OAAO,CAAC4F,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC5F,OAAO,CAAC6F,YAAY,CAACxD,GAAG,CAACyD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFlH,QAAQ,CAAC,MAAM,IAAI,CAAC8D,eAAe,EAAE,CAAC,EACtC/D,QAAQ,CAAC,MAAMwF,GAAG,CAAC4B,KAAK,EAAE,CAAC,CAC5B,CAACtD,SAAS,EAAE;EACjB;EAEAuD,OAAOA,CAAC7B,GAAQ;IACdA,GAAG,CAAC4B,KAAK,EAAE;EACb;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkB9H,IAAI,CAAC+H,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,MAAMvC,IAAI,GAAG1F,IAAI,CAACqI,KAAK,CAACC,aAAa,CAACH,EAAE,CAAC;MACzC,IAAIzC,IAAI,IAAIA,IAAI,CAAChC,MAAM,GAAG,CAAC,EAAE;QAE3B,IAAI,CAACrC,gBAAgB,CAACkH,2CAA2C,CAAC;UAChEpF,IAAI,EAAE;YACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;YACtCwE,KAAK,EAAEnB,MAAM,CAACI,KAAK,CAAC,CAAC;;SAExB,CAAC,CAACnE,IAAI,CACLvD,GAAG,CAACwD,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACtC,OAAO,CAAC4F,aAAa,CAAC,MAAM,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAAC5F,OAAO,CAAC6F,YAAY,CAACxD,GAAG,CAACyD,OAAQ,CAAC;UACzC;QACF,CAAC,CAAC,EACFlH,QAAQ,CAAC,MAAM,IAAI,CAAC8D,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACD,SAAS,EAAE;MAEf,CAAC,MAAM;QACL,IAAI,CAACzC,OAAO,CAAC6F,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAK,KAAK,CAACC,MAAM,CAACxG,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA4H,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC1G,aAAa,EAAE;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC4B,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAAC3B,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4B,eAAe,EAAE,CAACD,SAAS,EAAE;IACpC;EACF;EACA;EACA+E,eAAeA,CAAA;IACb;IACA,IAAI,CAACzG,cAAc,GAAG,EAAE;IAExB,IAAI,CAACI,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACG,wBAAwB,GAAG,CAAC;IACjC,IAAI,CAACN,eAAe,GAAG,EAAE;IAEzB,IAAI,CAAC0D,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAAC5E,aAAa,CAACuE,IAAI,CAACH,GAAG,EAAE;MAAES,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAEA;EACAF,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC7C,kBAAkB,IAAI,IAAI,CAACiB,mBAAmB,EAAE;MACvD,IAAI,CAACzC,eAAe,CAACoH,iCAAiC,CAAC;QACrDxF,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;UACtC4E,YAAY,EAAE,IAAI,CAAC9F,gBAAgB;UACnCsB,SAAS,EAAE,IAAI,CAAC/B,yBAAyB;UACzC6B,QAAQ,EAAE,IAAI,CAAC5B,sBAAsB;UACrCuG,KAAK,EAAE,IAAI,CAAC3G,eAAe,IAAIiE;;OAElC,CAAC,CAACxC,SAAS,CAAEJ,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,MAAMsF,oBAAoB,GAAGvF,GAAG,CAACE,OAAO,EAAEsF,GAAG,CAAEC,OAA+B,KAAM;YAClFC,EAAE,EAAED,OAAO,CAACrC,GAAG,IAAI,CAAC;YACpBuC,IAAI,EAAEF,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACH,KAAK,IAAI,EAAE;YACjDO,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEP,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI;WACzE,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACnH,0BAA0B,GAAGgB,GAAG,CAACgB,UAAU,IAAI,CAAC;UAErD;UACA,MAAMoF,WAAW,GAAG,IAAI,CAAC1H,cAAc,CAAC8G,GAAG,CAACa,GAAG,IAAIA,GAAG,CAACX,EAAE,CAAC;UAC1D,IAAI,CAACY,eAAe,GAAGf,oBAAoB,CAACgB,MAAM,CAACC,KAAK,IAAI,CAACJ,WAAW,CAACK,QAAQ,CAACD,KAAK,CAACd,EAAE,CAAC,CAAC;QAC9F,CAAC,MAAM;UACL,IAAI,CAAC/H,OAAO,CAAC6F,YAAY,CAACxD,GAAG,CAACyD,OAAO,IAAI,WAAW,CAAC;UACrD,IAAI,CAAC6C,eAAe,GAAG,EAAE;UACzB,IAAI,CAACtH,0BAA0B,GAAG,CAAC;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACsH,eAAe,GAAG,EAAE;MACzB,IAAI,CAACtH,0BAA0B,GAAG,CAAC;IACrC;EACF;EAEA;EACAsD,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC9C,kBAAkB,IAAI,IAAI,CAACiB,mBAAmB,IAAI,IAAI,CAACsB,gBAAgB,EAAEqB,GAAG,EAAE;MACrF,IAAI,CAACpF,eAAe,CAACoH,iCAAiC,CAAC;QACrDxF,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;UACtC0C,WAAW,EAAE,IAAI,CAACpB,gBAAgB,CAACqB,GAAG;UACtCiC,YAAY,EAAE,IAAI,CAAC9F,gBAAgB;UACnCsB,SAAS,EAAE,IAAI,CAAC5B,wBAAwB;UACxC0B,QAAQ,EAAE,IAAI,CAACzB;;OAElB,CAAC,CAACkB,SAAS,CAAEJ,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAACvB,cAAc,GAAGsB,GAAG,CAACE,OAAO,EAAEsF,GAAG,CAAEC,OAA+B,KAAM;YAC3EC,EAAE,EAAED,OAAO,CAACrC,GAAG,IAAI,CAAC;YACpBuC,IAAI,EAAEF,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACH,KAAK,IAAI,EAAE;YACjDO,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEL,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEP,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI;WACzE,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAAChH,yBAAyB,GAAGa,GAAG,CAACgB,UAAU,IAAI,CAAC;UAEpD;UACA,IAAI,CAAC0F,aAAa,GAAG,IAAI,CAAChI,cAAc,CAAC8G,GAAG,CAACa,GAAG,IAAIA,GAAG,CAACX,EAAE,CAAC;QAC7D,CAAC,MAAM;UACL,IAAI,CAAC/H,OAAO,CAAC6F,YAAY,CAACxD,GAAG,CAACyD,OAAO,IAAI,WAAW,CAAC;UACrD,IAAI,CAAC/E,cAAc,GAAG,EAAE;UACxB,IAAI,CAACS,yBAAyB,GAAG,CAAC;QACpC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACuH,aAAa,CAACvG,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACwG,yBAAyB,EAAE;MAClC,CAAC,MAAM;QACL,IAAI,CAACjI,cAAc,GAAG,EAAE;QACxB,IAAI,CAACS,yBAAyB,GAAG,CAAC;MACpC;IACF;EACF;EAIA;EACAwH,yBAAyBA,CAAA;IACvB,IAAI,CAAC3I,eAAe,CAACoH,iCAAiC,CAAC;MACrDxF,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtC4E,YAAY,EAAE,IAAI,CAAC9F,gBAAgB;QACnCsB,SAAS,EAAE,CAAC;QACZF,QAAQ,EAAE,IAAI,CAAC;;KAElB,CAAC,CAACP,SAAS,CAAEJ,GAA2C,IAAI;MAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,MAAM2G,kBAAkB,GAAG5G,GAAG,CAACE,OAAO,EAAEsF,GAAG,CAAEC,OAA+B,KAAM;UAChFC,EAAE,EAAED,OAAO,CAACrC,GAAG,IAAI,CAAC;UACpBuC,IAAI,EAAEF,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACH,KAAK,IAAI,EAAE;UACjDO,IAAI,EAAE,CAAC;UACPC,YAAY,EAAEL,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;UAChFC,OAAO,EAAEP,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;UAC3EE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI;SACzE,CAAC,CAAC,IAAI,EAAE;QAET;QACA,MAAMU,WAAW,GAAGD,kBAAkB,CAACL,MAAM,CAACC,KAAK,IAAI,IAAI,CAACE,aAAa,CAACD,QAAQ,CAACD,KAAK,CAACd,EAAE,CAAC,CAAC;QAC7F,IAAI,CAAChH,cAAc,GAAG,CAAC,GAAGmI,WAAW,CAAC;QACtC,IAAI,CAAC1H,yBAAyB,GAAG0H,WAAW,CAAC1G,MAAM;MACrD;IACF,CAAC,CAAC;EACJ;EAEA;EACA2G,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAChI,yBAAyB,GAAG,CAAC,CAAC,CAAC;IACpC,IAAI,CAACuD,mBAAmB,EAAE;EAC5B;EAEA0E,cAAcA,CAACP,KAAgB,EAAE3C,KAAa;IAC5C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACmD,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACX,eAAe,CAACY,SAAS,CAACb,GAAG,IAAIA,GAAG,CAACX,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;IACxE,IAAIuB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACvI,cAAc,CAACoE,IAAI,CAAC0D,KAAK,CAAC;MAC/B,IAAI,CAACrH,yBAAyB,GAAG,IAAI,CAACT,cAAc,CAACyB,MAAM;MAC3D;MACA,IAAI,CAACkC,mBAAmB,EAAE;IAC5B;EACF;EAEA8E,eAAeA,CAACX,KAAgB,EAAE3C,KAAa;IAC7C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACmD,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACvI,cAAc,CAACwI,SAAS,CAACb,GAAG,IAAIA,GAAG,CAACX,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;IACvE,IAAIuB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACvI,cAAc,CAAC0I,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACpC,IAAI,CAAC9H,yBAAyB,GAAG,IAAI,CAACT,cAAc,CAACyB,MAAM;MAC3D;MACA,IAAI,CAACkC,mBAAmB,EAAE;IAC5B;EACF;EAEAgF,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC3I,cAAc,CAACoE,IAAI,CAAC,GAAG,IAAI,CAACwD,eAAe,CAAC;IACjD,IAAI,CAACnH,yBAAyB,GAAG,IAAI,CAACT,cAAc,CAACyB,MAAM;IAC3D;IACA,IAAI,CAACkC,mBAAmB,EAAE;EAC5B;EAEAiF,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC5I,cAAc,GAAG,EAAE;IACxB,IAAI,CAACS,yBAAyB,GAAG,CAAC;IAClC;IACA,IAAI,CAACkD,mBAAmB,EAAE;EAC5B;EAEAkF,YAAYA,CAACf,KAAgB;IAC3B,OAAO,IAAI,CAACE,aAAa,CAACD,QAAQ,CAACD,KAAK,CAACd,EAAE,CAAC;EAC9C;EAEA8B,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC9I,cAAc,CAAC6H,MAAM,CAACC,KAAK,IAAI,IAAI,CAACe,YAAY,CAACf,KAAK,CAAC,CAAC,CAACrG,MAAM;EAC7E;EAEAsH,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC/I,cAAc,CAAC6H,MAAM,CAACC,KAAK,IAAI,CAAC,IAAI,CAACe,YAAY,CAACf,KAAK,CAAC,CAAC,CAACrG,MAAM;EAC9E;EAEA;EACAuH,iBAAiBA,CAAA;IACf,IAAI,CAAChJ,cAAc,GAAG,EAAE;IACxB,IAAI,CAACS,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACkD,mBAAmB,EAAE;EAC5B;EAEAsF,YAAYA,CAACnB,KAAgB,EAAEoB,eAAiC,EAAE/D,KAAY;IAC5EA,KAAK,CAACmD,eAAe,EAAE;IACvB,IAAI,CAACpI,eAAe,GAAG4H,KAAK;IAE5B;IACA,IAAI,CAACqB,SAAS,GAAG,CAAC,GAAG,IAAI,CAACvB,eAAe,EAAE,GAAG,IAAI,CAAC5H,cAAc,CAAC;IAClE,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACgJ,SAAS,CAACX,SAAS,CAAEb,GAAc,IAAKA,GAAG,CAACX,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;IAC5F,IAAI,CAAChI,aAAa,CAACuE,IAAI,CAAC2F,eAAe,CAAC;EAkC1C;EAEAE,qBAAqBA,CAACpJ,cAA2B;IAC/C,IAAIA,cAAc,CAACyB,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,MAAMmD,gBAAgB,GAAG5E,cAAc,CAAC8G,GAAG,CAACa,GAAG,IAAIA,GAAG,CAACX,EAAE,CAAC;MAE1D;MACC,IAAI,CAAC3D,gBAAwB,CAACuB,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAACvB,gBAAgB,CAACqB,GAAG,EAAE;QAC7B,IAAI,CAAC2E,gBAAgB,EAAE;MACzB;IACF;IAEA;IACA,IAAI,CAACrJ,cAAc,GAAG,EAAE;EAC1B,CAAC,CAAE;EACHqJ,gBAAgBA,CAAA;IACd,IAAI,CAACjK,gBAAgB,CAACmF,qCAAqC,CAAC;MAC1DrD,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACC,mBAAmB;QACtCnC,aAAa,EAAE,IAAI,CAACyD,gBAAgB,CAACzD,aAAa;QAClDD,WAAW,EAAE,IAAI,CAAC0D,gBAAgB,CAAC1D,WAAW;QAC9C6E,YAAY,EAAE,IAAI,CAACnB,gBAAgB,CAACmB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAACpB,gBAAgB,CAACqB,GAAI;QACvCpB,MAAM,EAAE,IAAI,CAACD,gBAAgB,CAACC,MAAM;QACpClC,OAAO,EAAE,IAAI,CAACiC,gBAAgB,CAACjC,OAAO;QAAE;QACxCuD,UAAU,EAAG,IAAI,CAACtB,gBAAwB,CAACuB,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACvD,IAAI,CACLvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtC,OAAO,CAAC4F,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAAC5F,OAAO,CAAC6F,YAAY,CAACxD,GAAG,CAACyD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFlH,QAAQ,CAAC,MAAM,IAAI,CAAC8D,eAAe,EAAE,CAAC,EACtC/D,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACyF,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC3B,SAAS,EAAE;EACf;EAEA4H,kBAAkBA,CAAClG,GAAQ;IACzB;IACA,IAAI,CAACpD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACS,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACR,eAAe,GAAG,EAAE;IACzB,IAAI,CAACG,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACG,wBAAwB,GAAG,CAAC;IACjC6C,GAAG,CAAC4B,KAAK,EAAE;EACb;EAKAuE,eAAeA,CAACC,QAAyB;IACvC,IAAI,CAAC3I,gBAAgB,GAAG2I,QAAQ;IAChC,IAAI,CAAC1I,kBAAkB,GAAG,IAAI;IAG9B,IAAI,CAACV,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACG,wBAAwB,GAAG,CAAC;IACjC,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC5B,IAAI,CAAC4B,mBAAmB,EAAE;MAC1B,IAAI,CAACC,kBAAkB,EAAE;IAC3B;EAGF;EAEA;EACA6F,gBAAgBA,CAACD,QAAgB;IAC/B,MAAMhL,MAAM,GAAG,IAAI,CAACkC,eAAe,CAAChC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAK4K,QAAQ,CAAC;IACvE,OAAOhL,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,MAAM;EACvC;EAIA6K,yBAAyBA,CAACC,IAAY;IACpC,IAAI,CAACvJ,yBAAyB,GAAGuJ,IAAI;IACrC,IAAI,CAAChG,mBAAmB,EAAE;EAC5B;EAEA;EACAiG,wBAAwBA,CAACD,IAAY;IACnC,IAAI,CAACpJ,wBAAwB,GAAGoJ,IAAI;IACpC,IAAI,CAAC/F,kBAAkB,EAAE;EAC3B;CAGD;AA1oBwDiG,UAAA,EAAtDnM,SAAS,CAAC,uBAAuB,EAAE;EAAEoM,MAAM,EAAE;AAAK,CAAE,CAAC,C,uEAA+C;AAD1FzL,yBAAyB,GAAAwL,UAAA,EARrCpM,SAAS,CAAC;EACTsM,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxM,YAAY,EAAEK,YAAY,EAAEE,wBAAwB,EAAEC,qBAAqB;CACtF,CAAC,C,EAEWE,yBAAyB,CA2oBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}